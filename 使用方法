窗口01
cd ~/alpha_lidar_GPS
roslaunch state_estimation mapping_robosense.launch

窗口02  保存中间结果
cd ~/alpha_lidar/software/global_optimization
python map_saver.py /home/<USER>/slam_share/aLidar/tempstd /cloud_registered_body /Odometry


窗口03  后期gto计算最终结果
cd   ~/alpha_lidar/software/global_optimization
source ~/.bashrc
conda activate alpha_lidar
python GTO_V2.py --root /home/<USER>/slam_share/aLidar/temp



