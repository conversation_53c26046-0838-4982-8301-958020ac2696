#!/bin/bash

# 极端GPS偏移修复测试脚本

echo "=========================================="
echo "🧪 极端GPS偏移修复测试"
echo "=========================================="

# 设置工作目录
WORKSPACE_DIR="/home/<USER>/slam_share/AI_code/github_alidar03/alpha_lidar_GPS/software/alpha_lidar_ws"
cd "$WORKSPACE_DIR"

echo ""
echo "步骤1: 环境检查"

# 检查ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查关键文件
echo "检查关键文件..."
FILES=(
    "src/state_estimation/launch/mapping_robosense_extreme_gps_fix.launch"
    "src/state_estimation/config/gps_loop_closure_params.yaml"
    "devel/lib/state_estimation/state_estimation_node"
    "src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 缺失"
        exit 1
    fi
done

echo ""
echo "步骤2: 启动测试"

# 启动roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 测试启动文件
echo "测试极端GPS修复启动文件..."
timeout 10 roslaunch state_estimation mapping_robosense_extreme_gps_fix.launch --screen &
LAUNCH_PID=$!

sleep 8

# 检查节点状态
echo ""
echo "步骤3: 节点状态检查"

NODES=(
    "/state_estimation_node"
    "/enhanced_gps_loop_closure_optimizer"
    "/enhanced_slam_loop_closure_integration"
    "/rs_to_velodyne"
)

for node in "${NODES[@]}"; do
    if rosnode info "$node" >/dev/null 2>&1; then
        echo "✅ $node - 运行中"
    else
        echo "❌ $node - 未运行"
    fi
done

echo ""
echo "步骤4: 话题检查"

TOPICS=(
    "/rtk/gnss"
    "/force_loop_closure"
    "/loop_closure_distance"
    "/loop_closure_result"
    "/matching_score"
    "/detected_loop_type"
)

for topic in "${TOPICS[@]}"; do
    if rostopic list | grep -q "$topic"; then
        echo "✅ $topic - 存在"
    else
        echo "❌ $topic - 不存在"
    fi
done

echo ""
echo "步骤5: 参数检查"

echo "检查关键GPS参数..."
PARAMS=(
    "/state_estimation_node/gps/enable_correction"
    "/state_estimation_node/gps/enable_loop_closure"
    "/state_estimation_node/gps/correction_rate"
    "/enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold"
    "/enhanced_slam_loop_closure_integration/force_search_radius"
)

for param in "${PARAMS[@]}"; do
    VALUE=$(rosparam get "$param" 2>/dev/null || echo "N/A")
    echo "  $param: $VALUE"
done

echo ""
echo "步骤6: 清理"

# 停止测试进程
kill $LAUNCH_PID 2>/dev/null
sleep 2

echo ""
echo "🎯 测试完成！"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

if [ $? -eq 0 ]; then
    echo "✅ 极端GPS修复系统配置正确"
    echo ""
    echo "📋 下一步操作："
    echo "1. 运行完整测试: ./compile_and_run_gps_fix.sh"
    echo "2. 监控性能: ./monitor_gps_performance.sh"
    echo "3. 播放数据包并观察首尾偏移变化"
else
    echo "❌ 配置存在问题，请检查编译和文件"
fi

echo ""
echo "💡 预期效果："
echo "- GPS回环距离阈值: 150.0m"
echo "- SLAM搜索半径: 250.0m"
echo "- 匹配分数阈值: 5.0 (极度宽松)"
echo "- GPS校正率: 95-98% (接近完全校正)"
echo "- 首尾偏移预期从几十米降低到几米"
