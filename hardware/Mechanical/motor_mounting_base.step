ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('/home/<USER>/Code/alpha_lidar/1_hardware/Mechanical/body.step',
  '2024-09-13T03:15:04',('Author'),(''),
  'Open CASCADE STEP processor 7.3','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('body_v2_001','body_v2_001','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#3911);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#354,#638,#694,#837,#979,#1248,#1286,#1345,
    #1399,#1453,#1507,#1561,#1615,#1861,#2121,#2175,#2229,#2283,#2337,
    #2391,#2445,#2494,#2543,#2571,#2630,#2684,#2738,#2792,#2846,#2900,
    #2954,#3008,#3094,#3103,#3157,#3211,#3265,#3320,#3374,#3428,#3482,
    #3537,#3592,#3656,#3723,#3765,#3820,#3875,#3891,#3895,#3899,#3903,
    #3907));
#17 = ADVANCED_FACE('',(#18,#199,#230,#261,#292,#323),#32,.T.);
#18 = FACE_BOUND('',#19,.T.);
#19 = EDGE_LOOP('',(#20,#55,#83,#111,#140,#168));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(-42.35343933105,176.66576869934,
    -9.204296955109));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#35 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#36 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(1.,0.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(-42.35343933105,158.12994453503,28.69088087862
    ));
#47 = DIRECTION('',(-1.,-7.183090749755E-15,-3.765531969661E-15));
#48 = DIRECTION('',(5.462009588019E-15,-0.939692620786,0.342020143326));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(-30.37889134143,33.729806263612));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#22,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(42.646560668945,198.21303772886,
    49.996338154403));
#59 = SURFACE_CURVE('',#60,(#64,#71),.PCURVE_S1.);
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#64 = PCURVE('',#32,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(0.,0.));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(0.,-1.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = PCURVE('',#72,#77);
#72 = PLANE('',#73);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#75 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#76 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#77 = DEFINITIONAL_REPRESENTATION('',(#78),#82);
#78 = LINE('',#79,#80);
#79 = CARTESIAN_POINT('',(0.,0.));
#80 = VECTOR('',#81,1.);
#81 = DIRECTION('',(1.,0.));
#82 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#83 = ORIENTED_EDGE('',*,*,#84,.T.);
#84 = EDGE_CURVE('',#57,#85,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(42.646560668945,176.66576869934,
    -9.204296955109));
#87 = SURFACE_CURVE('',#88,(#92,#99),.PCURVE_S1.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(42.646560668945,198.21303772886,
    49.996338154403));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#92 = PCURVE('',#32,#93);
#93 = DEFINITIONAL_REPRESENTATION('',(#94),#98);
#94 = LINE('',#95,#96);
#95 = CARTESIAN_POINT('',(0.,-85.));
#96 = VECTOR('',#97,1.);
#97 = DIRECTION('',(1.,0.));
#98 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#99 = PCURVE('',#100,#105);
#100 = PLANE('',#101);
#101 = AXIS2_PLACEMENT_3D('',#102,#103,#104);
#102 = CARTESIAN_POINT('',(42.646560668945,158.12994453503,
    28.69088087862));
#103 = DIRECTION('',(-1.,-7.183090749755E-15,-3.765531969661E-15));
#104 = DIRECTION('',(5.462009588019E-15,-0.939692620786,0.342020143326)
  );
#105 = DEFINITIONAL_REPRESENTATION('',(#106),#110);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(-30.37889134143,33.729806263612));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(0.,-1.));
#110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#111 = ORIENTED_EDGE('',*,*,#112,.T.);
#112 = EDGE_CURVE('',#85,#113,#115,.T.);
#113 = VERTEX_POINT('',#114);
#114 = CARTESIAN_POINT('',(30.646560668945,172.56152697943,
    -20.48060840454));
#115 = SURFACE_CURVE('',#116,(#121,#128),.PCURVE_S1.);
#116 = CIRCLE('',#117,12.);
#117 = AXIS2_PLACEMENT_3D('',#118,#119,#120);
#118 = CARTESIAN_POINT('',(30.646560668945,176.66576869934,
    -9.204296955109));
#119 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#120 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#121 = PCURVE('',#32,#122);
#122 = DEFINITIONAL_REPRESENTATION('',(#123),#127);
#123 = CIRCLE('',#124,12.);
#124 = AXIS2_PLACEMENT_2D('',#125,#126);
#125 = CARTESIAN_POINT('',(63.,-73.));
#126 = DIRECTION('',(0.,-1.));
#127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#128 = PCURVE('',#129,#134);
#129 = CYLINDRICAL_SURFACE('',#130,12.);
#130 = AXIS2_PLACEMENT_3D('',#131,#132,#133);
#131 = CARTESIAN_POINT('',(30.646560668945,176.66576869934,
    -9.204296955109));
#132 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#133 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#134 = DEFINITIONAL_REPRESENTATION('',(#135),#139);
#135 = LINE('',#136,#137);
#136 = CARTESIAN_POINT('',(-0.,0.));
#137 = VECTOR('',#138,1.);
#138 = DIRECTION('',(-1.,0.));
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#140 = ORIENTED_EDGE('',*,*,#141,.F.);
#141 = EDGE_CURVE('',#142,#113,#144,.T.);
#142 = VERTEX_POINT('',#143);
#143 = CARTESIAN_POINT('',(-30.35343933105,172.56152697943,
    -20.48060840454));
#144 = SURFACE_CURVE('',#145,(#149,#156),.PCURVE_S1.);
#145 = LINE('',#146,#147);
#146 = CARTESIAN_POINT('',(-42.35343933105,172.56152697943,
    -20.48060840454));
#147 = VECTOR('',#148,1.);
#148 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#149 = PCURVE('',#32,#150);
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(75.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,-1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = PCURVE('',#157,#162);
#157 = PLANE('',#158);
#158 = AXIS2_PLACEMENT_3D('',#159,#160,#161);
#159 = CARTESIAN_POINT('',(-42.35343933105,172.56152697943,
    -20.48060840454));
#160 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#161 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(0.,0.));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(0.,-1.));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = ORIENTED_EDGE('',*,*,#169,.F.);
#169 = EDGE_CURVE('',#24,#142,#170,.T.);
#170 = SURFACE_CURVE('',#171,(#176,#187),.PCURVE_S1.);
#171 = CIRCLE('',#172,12.);
#172 = AXIS2_PLACEMENT_3D('',#173,#174,#175);
#173 = CARTESIAN_POINT('',(-30.35343933105,176.66576869934,
    -9.204296955109));
#174 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#175 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#176 = PCURVE('',#32,#177);
#177 = DEFINITIONAL_REPRESENTATION('',(#178),#186);
#178 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#179,#180,#181,#182,#183,#184
,#185),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#179 = CARTESIAN_POINT('',(63.,-24.));
#180 = CARTESIAN_POINT('',(42.215390309173,-24.));
#181 = CARTESIAN_POINT('',(52.607695154587,-6.));
#182 = CARTESIAN_POINT('',(63.,12.));
#183 = CARTESIAN_POINT('',(73.392304845413,-6.));
#184 = CARTESIAN_POINT('',(83.784609690827,-24.));
#185 = CARTESIAN_POINT('',(63.,-24.));
#186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#187 = PCURVE('',#188,#193);
#188 = CYLINDRICAL_SURFACE('',#189,12.);
#189 = AXIS2_PLACEMENT_3D('',#190,#191,#192);
#190 = CARTESIAN_POINT('',(-30.35343933105,176.66576869934,
    -9.204296955109));
#191 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#192 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#193 = DEFINITIONAL_REPRESENTATION('',(#194),#198);
#194 = LINE('',#195,#196);
#195 = CARTESIAN_POINT('',(-3.14159265359,0.));
#196 = VECTOR('',#197,1.);
#197 = DIRECTION('',(1.,0.));
#198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#199 = FACE_BOUND('',#200,.T.);
#200 = EDGE_LOOP('',(#201));
#201 = ORIENTED_EDGE('',*,*,#202,.F.);
#202 = EDGE_CURVE('',#203,#203,#205,.T.);
#203 = VERTEX_POINT('',#204);
#204 = CARTESIAN_POINT('',(20.146560668945,176.68286970651,
    -9.15731232407));
#205 = SURFACE_CURVE('',#206,(#211,#218),.PCURVE_S1.);
#206 = CIRCLE('',#207,2.05);
#207 = AXIS2_PLACEMENT_3D('',#208,#209,#210);
#208 = CARTESIAN_POINT('',(20.146560668945,175.98172841269,
    -11.08368219668));
#209 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#210 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#211 = PCURVE('',#32,#212);
#212 = DEFINITIONAL_REPRESENTATION('',(#213),#217);
#213 = CIRCLE('',#214,2.05);
#214 = AXIS2_PLACEMENT_2D('',#215,#216);
#215 = CARTESIAN_POINT('',(65.,-62.5));
#216 = DIRECTION('',(-1.,0.));
#217 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#218 = PCURVE('',#219,#224);
#219 = CYLINDRICAL_SURFACE('',#220,2.05);
#220 = AXIS2_PLACEMENT_3D('',#221,#222,#223);
#221 = CARTESIAN_POINT('',(20.146560668945,175.98172841269,
    -11.08368219668));
#222 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#223 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#224 = DEFINITIONAL_REPRESENTATION('',(#225),#229);
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(0.,0.));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(1.,0.));
#229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#230 = FACE_BOUND('',#231,.T.);
#231 = EDGE_LOOP('',(#232));
#232 = ORIENTED_EDGE('',*,*,#233,.F.);
#233 = EDGE_CURVE('',#234,#234,#236,.T.);
#234 = VERTEX_POINT('',#235);
#235 = CARTESIAN_POINT('',(20.146560668945,195.49397758942,
    42.525781819155));
#236 = SURFACE_CURVE('',#237,(#242,#249),.PCURVE_S1.);
#237 = CIRCLE('',#238,2.05);
#238 = AXIS2_PLACEMENT_3D('',#239,#240,#241);
#239 = CARTESIAN_POINT('',(20.146560668945,194.7928362956,
    40.599411946544));
#240 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#241 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#242 = PCURVE('',#32,#243);
#243 = DEFINITIONAL_REPRESENTATION('',(#244),#248);
#244 = CIRCLE('',#245,2.05);
#245 = AXIS2_PLACEMENT_2D('',#246,#247);
#246 = CARTESIAN_POINT('',(10.,-62.5));
#247 = DIRECTION('',(-1.,0.));
#248 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#249 = PCURVE('',#250,#255);
#250 = CYLINDRICAL_SURFACE('',#251,2.05);
#251 = AXIS2_PLACEMENT_3D('',#252,#253,#254);
#252 = CARTESIAN_POINT('',(20.146560668945,194.7928362956,
    40.599411946544));
#253 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#254 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#255 = DEFINITIONAL_REPRESENTATION('',(#256),#260);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(0.,0.));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(1.,0.));
#260 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#261 = FACE_BOUND('',#262,.T.);
#262 = EDGE_LOOP('',(#263));
#263 = ORIENTED_EDGE('',*,*,#264,.F.);
#264 = EDGE_CURVE('',#265,#265,#267,.T.);
#265 = VERTEX_POINT('',#266);
#266 = CARTESIAN_POINT('',(0.146560668945,189.33088234303,
    25.592816795769));
#267 = SURFACE_CURVE('',#268,(#273,#280),.PCURVE_S1.);
#268 = CIRCLE('',#269,12.5);
#269 = AXIS2_PLACEMENT_3D('',#270,#271,#272);
#270 = CARTESIAN_POINT('',(0.146560668945,185.05563055146,
    13.846659035945));
#271 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#272 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#273 = PCURVE('',#32,#274);
#274 = DEFINITIONAL_REPRESENTATION('',(#275),#279);
#275 = CIRCLE('',#276,12.5);
#276 = AXIS2_PLACEMENT_2D('',#277,#278);
#277 = CARTESIAN_POINT('',(38.469685,-42.5));
#278 = DIRECTION('',(-1.,0.));
#279 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#280 = PCURVE('',#281,#286);
#281 = CYLINDRICAL_SURFACE('',#282,12.5);
#282 = AXIS2_PLACEMENT_3D('',#283,#284,#285);
#283 = CARTESIAN_POINT('',(0.146560668945,185.05563055146,
    13.846659035945));
#284 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#285 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#286 = DEFINITIONAL_REPRESENTATION('',(#287),#291);
#287 = LINE('',#288,#289);
#288 = CARTESIAN_POINT('',(0.,0.));
#289 = VECTOR('',#290,1.);
#290 = DIRECTION('',(1.,0.));
#291 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#292 = FACE_BOUND('',#293,.T.);
#293 = EDGE_LOOP('',(#294));
#294 = ORIENTED_EDGE('',*,*,#295,.F.);
#295 = EDGE_CURVE('',#296,#296,#298,.T.);
#296 = VERTEX_POINT('',#297);
#297 = CARTESIAN_POINT('',(-19.85343933105,195.49397758942,
    42.525781819155));
#298 = SURFACE_CURVE('',#299,(#304,#311),.PCURVE_S1.);
#299 = CIRCLE('',#300,2.05);
#300 = AXIS2_PLACEMENT_3D('',#301,#302,#303);
#301 = CARTESIAN_POINT('',(-19.85343933105,194.7928362956,
    40.599411946544));
#302 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#303 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#304 = PCURVE('',#32,#305);
#305 = DEFINITIONAL_REPRESENTATION('',(#306),#310);
#306 = CIRCLE('',#307,2.05);
#307 = AXIS2_PLACEMENT_2D('',#308,#309);
#308 = CARTESIAN_POINT('',(10.,-22.5));
#309 = DIRECTION('',(-1.,0.));
#310 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#311 = PCURVE('',#312,#317);
#312 = CYLINDRICAL_SURFACE('',#313,2.05);
#313 = AXIS2_PLACEMENT_3D('',#314,#315,#316);
#314 = CARTESIAN_POINT('',(-19.85343933105,194.7928362956,
    40.599411946544));
#315 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#316 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#322);
#318 = LINE('',#319,#320);
#319 = CARTESIAN_POINT('',(0.,0.));
#320 = VECTOR('',#321,1.);
#321 = DIRECTION('',(1.,0.));
#322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#323 = FACE_BOUND('',#324,.T.);
#324 = EDGE_LOOP('',(#325));
#325 = ORIENTED_EDGE('',*,*,#326,.F.);
#326 = EDGE_CURVE('',#327,#327,#329,.T.);
#327 = VERTEX_POINT('',#328);
#328 = CARTESIAN_POINT('',(-19.85343933105,176.68286970651,
    -9.15731232407));
#329 = SURFACE_CURVE('',#330,(#335,#342),.PCURVE_S1.);
#330 = CIRCLE('',#331,2.05);
#331 = AXIS2_PLACEMENT_3D('',#332,#333,#334);
#332 = CARTESIAN_POINT('',(-19.85343933105,175.98172841269,
    -11.08368219668));
#333 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#334 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#335 = PCURVE('',#32,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#341);
#337 = CIRCLE('',#338,2.05);
#338 = AXIS2_PLACEMENT_2D('',#339,#340);
#339 = CARTESIAN_POINT('',(65.,-22.5));
#340 = DIRECTION('',(-1.,0.));
#341 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#342 = PCURVE('',#343,#348);
#343 = CYLINDRICAL_SURFACE('',#344,2.05);
#344 = AXIS2_PLACEMENT_3D('',#345,#346,#347);
#345 = CARTESIAN_POINT('',(-19.85343933105,175.98172841269,
    -11.08368219668));
#346 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#347 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#348 = DEFINITIONAL_REPRESENTATION('',(#349),#353);
#349 = LINE('',#350,#351);
#350 = CARTESIAN_POINT('',(0.,0.));
#351 = VECTOR('',#352,1.);
#352 = DIRECTION('',(1.,0.));
#353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#354 = ADVANCED_FACE('',(#355,#463,#498,#533,#568,#603),#44,.T.);
#355 = FACE_BOUND('',#356,.T.);
#356 = EDGE_LOOP('',(#357,#358,#381,#409,#437));
#357 = ORIENTED_EDGE('',*,*,#21,.T.);
#358 = ORIENTED_EDGE('',*,*,#359,.T.);
#359 = EDGE_CURVE('',#24,#360,#362,.T.);
#360 = VERTEX_POINT('',#361);
#361 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    7.578588807869));
#362 = SURFACE_CURVE('',#363,(#367,#374),.PCURVE_S1.);
#363 = LINE('',#364,#365);
#364 = CARTESIAN_POINT('',(-42.35343933105,176.66576869934,
    -9.204296955109));
#365 = VECTOR('',#366,1.);
#366 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#367 = PCURVE('',#44,#368);
#368 = DEFINITIONAL_REPRESENTATION('',(#369),#373);
#369 = LINE('',#370,#371);
#370 = CARTESIAN_POINT('',(-30.37889134143,-29.27019373638));
#371 = VECTOR('',#372,1.);
#372 = DIRECTION('',(1.,0.));
#373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#374 = PCURVE('',#188,#375);
#375 = DEFINITIONAL_REPRESENTATION('',(#376),#380);
#376 = LINE('',#377,#378);
#377 = CARTESIAN_POINT('',(0.,0.));
#378 = VECTOR('',#379,1.);
#379 = DIRECTION('',(0.,1.));
#380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#381 = ORIENTED_EDGE('',*,*,#382,.T.);
#382 = EDGE_CURVE('',#360,#383,#385,.T.);
#383 = VERTEX_POINT('',#384);
#384 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    74.621788473851));
#385 = SURFACE_CURVE('',#386,(#390,#397),.PCURVE_S1.);
#386 = LINE('',#387,#388);
#387 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    -5.191544461842));
#388 = VECTOR('',#389,1.);
#389 = DIRECTION('',(-3.845531969661E-15,-7.216449660064E-16,1.));
#390 = PCURVE('',#44,#391);
#391 = DEFINITIONAL_REPRESENTATION('',(#392),#396);
#392 = LINE('',#393,#394);
#393 = CARTESIAN_POINT('',(14.323341088603,-41.27019373638));
#394 = VECTOR('',#395,1.);
#395 = DIRECTION('',(0.342020143326,0.939692620786));
#396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#397 = PCURVE('',#398,#403);
#398 = PLANE('',#399);
#399 = AXIS2_PLACEMENT_3D('',#400,#401,#402);
#400 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    -5.191544461842));
#401 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#402 = DIRECTION('',(-3.845531969661E-15,-7.216449660064E-16,1.));
#403 = DEFINITIONAL_REPRESENTATION('',(#404),#408);
#404 = LINE('',#405,#406);
#405 = CARTESIAN_POINT('',(-2.15E-15,0.));
#406 = VECTOR('',#407,1.);
#407 = DIRECTION('',(1.,0.));
#408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#409 = ORIENTED_EDGE('',*,*,#410,.T.);
#410 = EDGE_CURVE('',#383,#411,#413,.T.);
#411 = VERTEX_POINT('',#412);
#412 = CARTESIAN_POINT('',(-42.35343933105,196.33365248728,
    50.680378441054));
#413 = SURFACE_CURVE('',#414,(#418,#425),.PCURVE_S1.);
#414 = LINE('',#415,#416);
#415 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    74.621788473851));
#416 = VECTOR('',#417,1.);
#417 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#418 = PCURVE('',#44,#419);
#419 = DEFINITIONAL_REPRESENTATION('',(#420),#424);
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(41.621108658568,33.729806263612));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(-1.,0.));
#424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#425 = PCURVE('',#426,#431);
#426 = PLANE('',#427);
#427 = AXIS2_PLACEMENT_3D('',#428,#429,#430);
#428 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    74.621788473851));
#429 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#430 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#431 = DEFINITIONAL_REPRESENTATION('',(#432),#436);
#432 = LINE('',#433,#434);
#433 = CARTESIAN_POINT('',(-1.421E-14,-0.));
#434 = VECTOR('',#435,1.);
#435 = DIRECTION('',(1.,0.));
#436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#437 = ORIENTED_EDGE('',*,*,#438,.T.);
#438 = EDGE_CURVE('',#411,#22,#439,.T.);
#439 = SURFACE_CURVE('',#440,(#444,#451),.PCURVE_S1.);
#440 = LINE('',#441,#442);
#441 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    74.621788473851));
#442 = VECTOR('',#443,1.);
#443 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#444 = PCURVE('',#44,#445);
#445 = DEFINITIONAL_REPRESENTATION('',(#446),#450);
#446 = LINE('',#447,#448);
#447 = CARTESIAN_POINT('',(41.621108658568,33.729806263612));
#448 = VECTOR('',#449,1.);
#449 = DIRECTION('',(-1.,0.));
#450 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#451 = PCURVE('',#452,#457);
#452 = PLANE('',#453);
#453 = AXIS2_PLACEMENT_3D('',#454,#455,#456);
#454 = CARTESIAN_POINT('',(-42.35343933105,196.33365248728,
    50.680378441054));
#455 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#456 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#457 = DEFINITIONAL_REPRESENTATION('',(#458),#462);
#458 = LINE('',#459,#460);
#459 = CARTESIAN_POINT('',(-70.,1.54E-14));
#460 = VECTOR('',#461,1.);
#461 = DIRECTION('',(1.,-2.2E-16));
#462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#463 = FACE_BOUND('',#464,.T.);
#464 = EDGE_LOOP('',(#465));
#465 = ORIENTED_EDGE('',*,*,#466,.T.);
#466 = EDGE_CURVE('',#467,#467,#469,.T.);
#467 = VERTEX_POINT('',#468);
#468 = CARTESIAN_POINT('',(-42.35343933105,174.88008816843,
    21.432767705577));
#469 = SURFACE_CURVE('',#470,(#475,#486),.PCURVE_S1.);
#470 = CIRCLE('',#471,0.95);
#471 = AXIS2_PLACEMENT_3D('',#472,#473,#474);
#472 = CARTESIAN_POINT('',(-42.35343933105,174.55516903227,
    20.540059715831));
#473 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#474 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#475 = PCURVE('',#44,#476);
#476 = DEFINITIONAL_REPRESENTATION('',(#477),#485);
#477 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#478,#479,#480,#481,#482,#483
,#484),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#478 = CARTESIAN_POINT('',(-18.22240727713,-1.091508863315));
#479 = CARTESIAN_POINT('',(-16.57695900994,-1.091508863315));
#480 = CARTESIAN_POINT('',(-17.39968314353,-2.516508863315));
#481 = CARTESIAN_POINT('',(-18.22240727713,-3.941508863315));
#482 = CARTESIAN_POINT('',(-19.04513141072,-2.516508863315));
#483 = CARTESIAN_POINT('',(-19.86785554432,-1.091508863315));
#484 = CARTESIAN_POINT('',(-18.22240727713,-1.091508863315));
#485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#486 = PCURVE('',#487,#492);
#487 = CYLINDRICAL_SURFACE('',#488,0.95);
#488 = AXIS2_PLACEMENT_3D('',#489,#490,#491);
#489 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    20.540059715831));
#490 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#491 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#492 = DEFINITIONAL_REPRESENTATION('',(#493),#497);
#493 = LINE('',#494,#495);
#494 = CARTESIAN_POINT('',(0.,-85.));
#495 = VECTOR('',#496,1.);
#496 = DIRECTION('',(1.,0.));
#497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#498 = FACE_BOUND('',#499,.T.);
#499 = EDGE_LOOP('',(#500));
#500 = ORIENTED_EDGE('',*,*,#501,.T.);
#501 = EDGE_CURVE('',#502,#502,#504,.T.);
#502 = VERTEX_POINT('',#503);
#503 = CARTESIAN_POINT('',(-42.35343933105,140.88008816843,
    21.432767705577));
#504 = SURFACE_CURVE('',#505,(#510,#521),.PCURVE_S1.);
#505 = CIRCLE('',#506,0.95);
#506 = AXIS2_PLACEMENT_3D('',#507,#508,#509);
#507 = CARTESIAN_POINT('',(-42.35343933105,140.55516903227,
    20.540059715831));
#508 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#509 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#510 = PCURVE('',#44,#511);
#511 = DEFINITIONAL_REPRESENTATION('',(#512),#520);
#512 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#513,#514,#515,#516,#517,#518
,#519),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#513 = CARTESIAN_POINT('',(13.727141829591,-12.72019373638));
#514 = CARTESIAN_POINT('',(15.372590096781,-12.72019373638));
#515 = CARTESIAN_POINT('',(14.549865963186,-14.14519373638));
#516 = CARTESIAN_POINT('',(13.727141829591,-15.57019373638));
#517 = CARTESIAN_POINT('',(12.904417695996,-14.14519373638));
#518 = CARTESIAN_POINT('',(12.0816935624,-12.72019373638));
#519 = CARTESIAN_POINT('',(13.727141829591,-12.72019373638));
#520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#521 = PCURVE('',#522,#527);
#522 = CYLINDRICAL_SURFACE('',#523,0.95);
#523 = AXIS2_PLACEMENT_3D('',#524,#525,#526);
#524 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    20.540059715831));
#525 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#526 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#527 = DEFINITIONAL_REPRESENTATION('',(#528),#532);
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(0.,-85.));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(1.,0.));
#532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#533 = FACE_BOUND('',#534,.T.);
#534 = EDGE_LOOP('',(#535));
#535 = ORIENTED_EDGE('',*,*,#536,.T.);
#536 = EDGE_CURVE('',#537,#537,#539,.T.);
#537 = VERTEX_POINT('',#538);
#538 = CARTESIAN_POINT('',(-42.35343933105,159.76165480551,
    32.066163830596));
#539 = SURFACE_CURVE('',#540,(#545,#556),.PCURVE_S1.);
#540 = CIRCLE('',#541,10.);
#541 = AXIS2_PLACEMENT_3D('',#542,#543,#544);
#542 = CARTESIAN_POINT('',(-42.35343933105,156.34145337225,
    22.669237622737));
#543 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#544 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#545 = PCURVE('',#44,#546);
#546 = DEFINITIONAL_REPRESENTATION('',(#547),#555);
#547 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#548,#549,#550,#551,#552,#553
,#554),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#548 = CARTESIAN_POINT('',(-0.378891341432,3.729806263612));
#549 = CARTESIAN_POINT('',(16.941616734257,3.729806263612));
#550 = CARTESIAN_POINT('',(8.281362696412,-11.27019373638));
#551 = CARTESIAN_POINT('',(-0.378891341432,-26.27019373638));
#552 = CARTESIAN_POINT('',(-9.039145379276,-11.27019373638));
#553 = CARTESIAN_POINT('',(-17.69939941712,3.729806263612));
#554 = CARTESIAN_POINT('',(-0.378891341432,3.729806263612));
#555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#556 = PCURVE('',#557,#562);
#557 = CYLINDRICAL_SURFACE('',#558,10.);
#558 = AXIS2_PLACEMENT_3D('',#559,#560,#561);
#559 = CARTESIAN_POINT('',(42.646560668945,156.34145337225,
    22.669237622737));
#560 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#561 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#562 = DEFINITIONAL_REPRESENTATION('',(#563),#567);
#563 = LINE('',#564,#565);
#564 = CARTESIAN_POINT('',(0.,-85.));
#565 = VECTOR('',#566,1.);
#566 = DIRECTION('',(1.,0.));
#567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#568 = FACE_BOUND('',#569,.T.);
#569 = EDGE_LOOP('',(#570));
#570 = ORIENTED_EDGE('',*,*,#571,.T.);
#571 = EDGE_CURVE('',#572,#572,#574,.T.);
#572 = VERTEX_POINT('',#573);
#573 = CARTESIAN_POINT('',(-42.35343933105,174.88008816843,
    45.432767705577));
#574 = SURFACE_CURVE('',#575,(#580,#591),.PCURVE_S1.);
#575 = CIRCLE('',#576,0.95);
#576 = AXIS2_PLACEMENT_3D('',#577,#578,#579);
#577 = CARTESIAN_POINT('',(-42.35343933105,174.55516903227,
    44.540059715831));
#578 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#579 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#580 = PCURVE('',#44,#581);
#581 = DEFINITIONAL_REPRESENTATION('',(#582),#590);
#582 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#583,#584,#585,#586,#587,#588
,#589),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#583 = CARTESIAN_POINT('',(-10.01392383731,21.461114035547));
#584 = CARTESIAN_POINT('',(-8.368475570124,21.461114035547));
#585 = CARTESIAN_POINT('',(-9.191199703719,20.036114035547));
#586 = CARTESIAN_POINT('',(-10.01392383731,18.611114035547));
#587 = CARTESIAN_POINT('',(-10.8366479709,20.036114035547));
#588 = CARTESIAN_POINT('',(-11.6593721045,21.461114035547));
#589 = CARTESIAN_POINT('',(-10.01392383731,21.461114035547));
#590 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#591 = PCURVE('',#592,#597);
#592 = CYLINDRICAL_SURFACE('',#593,0.95);
#593 = AXIS2_PLACEMENT_3D('',#594,#595,#596);
#594 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    44.540059715831));
#595 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#596 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#597 = DEFINITIONAL_REPRESENTATION('',(#598),#602);
#598 = LINE('',#599,#600);
#599 = CARTESIAN_POINT('',(0.,-85.));
#600 = VECTOR('',#601,1.);
#601 = DIRECTION('',(1.,0.));
#602 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#603 = FACE_BOUND('',#604,.T.);
#604 = EDGE_LOOP('',(#605));
#605 = ORIENTED_EDGE('',*,*,#606,.T.);
#606 = EDGE_CURVE('',#607,#607,#609,.T.);
#607 = VERTEX_POINT('',#608);
#608 = CARTESIAN_POINT('',(-42.35343933105,140.88008816843,
    45.432767705577));
#609 = SURFACE_CURVE('',#610,(#615,#626),.PCURVE_S1.);
#610 = CIRCLE('',#611,0.95);
#611 = AXIS2_PLACEMENT_3D('',#612,#613,#614);
#612 = CARTESIAN_POINT('',(-42.35343933105,140.55516903227,
    44.540059715831));
#613 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#614 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#615 = PCURVE('',#44,#616);
#616 = DEFINITIONAL_REPRESENTATION('',(#617),#625);
#617 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#618,#619,#620,#621,#622,#623
,#624),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#618 = CARTESIAN_POINT('',(21.935625269407,9.832429162474));
#619 = CARTESIAN_POINT('',(23.581073536597,9.832429162474));
#620 = CARTESIAN_POINT('',(22.758349403002,8.407429162474));
#621 = CARTESIAN_POINT('',(21.935625269407,6.982429162474));
#622 = CARTESIAN_POINT('',(21.112901135812,8.407429162474));
#623 = CARTESIAN_POINT('',(20.290177002216,9.832429162474));
#624 = CARTESIAN_POINT('',(21.935625269407,9.832429162474));
#625 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#626 = PCURVE('',#627,#632);
#627 = CYLINDRICAL_SURFACE('',#628,0.95);
#628 = AXIS2_PLACEMENT_3D('',#629,#630,#631);
#629 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    44.540059715831));
#630 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#631 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#632 = DEFINITIONAL_REPRESENTATION('',(#633),#637);
#633 = LINE('',#634,#635);
#634 = CARTESIAN_POINT('',(0.,-85.));
#635 = VECTOR('',#636,1.);
#636 = DIRECTION('',(1.,0.));
#637 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#638 = ADVANCED_FACE('',(#639),#188,.T.);
#639 = FACE_BOUND('',#640,.T.);
#640 = EDGE_LOOP('',(#641,#642,#665,#693));
#641 = ORIENTED_EDGE('',*,*,#169,.T.);
#642 = ORIENTED_EDGE('',*,*,#643,.T.);
#643 = EDGE_CURVE('',#142,#644,#646,.T.);
#644 = VERTEX_POINT('',#645);
#645 = CARTESIAN_POINT('',(-30.35343933105,130.55516903227,
    -5.191544461842));
#646 = SURFACE_CURVE('',#647,(#651,#658),.PCURVE_S1.);
#647 = LINE('',#648,#649);
#648 = CARTESIAN_POINT('',(-30.35343933105,172.56152697943,
    -20.48060840454));
#649 = VECTOR('',#650,1.);
#650 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#651 = PCURVE('',#188,#652);
#652 = DEFINITIONAL_REPRESENTATION('',(#653),#657);
#653 = LINE('',#654,#655);
#654 = CARTESIAN_POINT('',(1.570796326795,0.));
#655 = VECTOR('',#656,1.);
#656 = DIRECTION('',(0.,1.));
#657 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#658 = PCURVE('',#157,#659);
#659 = DEFINITIONAL_REPRESENTATION('',(#660),#664);
#660 = LINE('',#661,#662);
#661 = CARTESIAN_POINT('',(3.E-17,-12.));
#662 = VECTOR('',#663,1.);
#663 = DIRECTION('',(1.,0.));
#664 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#665 = ORIENTED_EDGE('',*,*,#666,.F.);
#666 = EDGE_CURVE('',#360,#644,#667,.T.);
#667 = SURFACE_CURVE('',#668,(#673,#686),.PCURVE_S1.);
#668 = ELLIPSE('',#669,12.770133269711,12.);
#669 = AXIS2_PLACEMENT_3D('',#670,#671,#672);
#670 = CARTESIAN_POINT('',(-30.35343933105,130.55516903227,
    7.578588807869));
#671 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#672 = DIRECTION('',(-3.845531969661E-15,-7.216449660064E-16,1.));
#673 = PCURVE('',#188,#674);
#674 = DEFINITIONAL_REPRESENTATION('',(#675),#685);
#675 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#676,#677,#678,#679,#680,#681,
    #682,#683,#684),.UNSPECIFIED.,.F.,.F.,(9,9),(1.570796326795,
    3.14159265359),.PIECEWISE_BEZIER_KNOTS.);
#676 = CARTESIAN_POINT('',(0.,49.069875241229));
#677 = CARTESIAN_POINT('',(0.196349540849,48.212290580657));
#678 = CARTESIAN_POINT('',(0.392699081699,47.354705716461));
#679 = CARTESIAN_POINT('',(0.589048622549,46.547503597336));
#680 = CARTESIAN_POINT('',(0.785398163393,45.841057073732));
#681 = CARTESIAN_POINT('',(0.981747704249,45.279555989517));
#682 = CARTESIAN_POINT('',(1.178097245096,44.894674161507));
#683 = CARTESIAN_POINT('',(1.374446785946,44.702232430035));
#684 = CARTESIAN_POINT('',(1.570796326795,44.702232430035));
#685 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#686 = PCURVE('',#398,#687);
#687 = DEFINITIONAL_REPRESENTATION('',(#688),#692);
#688 = ELLIPSE('',#689,12.770133269711,12.);
#689 = AXIS2_PLACEMENT_2D('',#690,#691);
#690 = CARTESIAN_POINT('',(12.770133269711,-12.));
#691 = DIRECTION('',(1.,0.));
#692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#693 = ORIENTED_EDGE('',*,*,#359,.F.);
#694 = ADVANCED_FACE('',(#695),#72,.T.);
#695 = FACE_BOUND('',#696,.T.);
#696 = EDGE_LOOP('',(#697,#720,#749,#777,#810,#836));
#697 = ORIENTED_EDGE('',*,*,#698,.T.);
#698 = EDGE_CURVE('',#22,#699,#701,.T.);
#699 = VERTEX_POINT('',#700);
#700 = CARTESIAN_POINT('',(-42.35343933105,200.94919887546,
    57.51387912069));
#701 = SURFACE_CURVE('',#702,(#706,#713),.PCURVE_S1.);
#702 = LINE('',#703,#704);
#703 = CARTESIAN_POINT('',(-42.35343933105,198.21303772886,
    49.996338154403));
#704 = VECTOR('',#705,1.);
#705 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#706 = PCURVE('',#72,#707);
#707 = DEFINITIONAL_REPRESENTATION('',(#708),#712);
#708 = LINE('',#709,#710);
#709 = CARTESIAN_POINT('',(0.,0.));
#710 = VECTOR('',#711,1.);
#711 = DIRECTION('',(-0.,-1.));
#712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#713 = PCURVE('',#452,#714);
#714 = DEFINITIONAL_REPRESENTATION('',(#715),#719);
#715 = LINE('',#716,#717);
#716 = CARTESIAN_POINT('',(2.,-4.4E-16));
#717 = VECTOR('',#718,1.);
#718 = DIRECTION('',(0.,-1.));
#719 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#720 = ORIENTED_EDGE('',*,*,#721,.T.);
#721 = EDGE_CURVE('',#699,#722,#724,.T.);
#722 = VERTEX_POINT('',#723);
#723 = CARTESIAN_POINT('',(-30.35343933105,205.05344059537,
    68.790190570121));
#724 = SURFACE_CURVE('',#725,(#730,#737),.PCURVE_S1.);
#725 = CIRCLE('',#726,12.);
#726 = AXIS2_PLACEMENT_3D('',#727,#728,#729);
#727 = CARTESIAN_POINT('',(-30.35343933105,200.94919887546,
    57.51387912069));
#728 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#729 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#730 = PCURVE('',#72,#731);
#731 = DEFINITIONAL_REPRESENTATION('',(#732),#736);
#732 = CIRCLE('',#733,12.);
#733 = AXIS2_PLACEMENT_2D('',#734,#735);
#734 = CARTESIAN_POINT('',(12.,-8.));
#735 = DIRECTION('',(-0.,-1.));
#736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#737 = PCURVE('',#738,#743);
#738 = CYLINDRICAL_SURFACE('',#739,12.);
#739 = AXIS2_PLACEMENT_3D('',#740,#741,#742);
#740 = CARTESIAN_POINT('',(-30.35343933105,199.06981363389,
    58.197919407342));
#741 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#742 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#743 = DEFINITIONAL_REPRESENTATION('',(#744),#748);
#744 = LINE('',#745,#746);
#745 = CARTESIAN_POINT('',(-4.712388980385,2.));
#746 = VECTOR('',#747,1.);
#747 = DIRECTION('',(1.,0.));
#748 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#749 = ORIENTED_EDGE('',*,*,#750,.T.);
#750 = EDGE_CURVE('',#722,#751,#753,.T.);
#751 = VERTEX_POINT('',#752);
#752 = CARTESIAN_POINT('',(30.646560668945,205.05344059537,
    68.790190570121));
#753 = SURFACE_CURVE('',#754,(#758,#765),.PCURVE_S1.);
#754 = LINE('',#755,#756);
#755 = CARTESIAN_POINT('',(-42.35343933105,205.05344059537,
    68.790190570121));
#756 = VECTOR('',#757,1.);
#757 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#758 = PCURVE('',#72,#759);
#759 = DEFINITIONAL_REPRESENTATION('',(#760),#764);
#760 = LINE('',#761,#762);
#761 = CARTESIAN_POINT('',(-0.,-20.));
#762 = VECTOR('',#763,1.);
#763 = DIRECTION('',(1.,0.));
#764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#765 = PCURVE('',#766,#771);
#766 = PLANE('',#767);
#767 = AXIS2_PLACEMENT_3D('',#768,#769,#770);
#768 = CARTESIAN_POINT('',(0.146560668945,204.11374797458,
    69.132210713447));
#769 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#770 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#771 = DEFINITIONAL_REPRESENTATION('',(#772),#776);
#772 = LINE('',#773,#774);
#773 = CARTESIAN_POINT('',(1.,-42.5));
#774 = VECTOR('',#775,1.);
#775 = DIRECTION('',(0.,1.));
#776 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#777 = ORIENTED_EDGE('',*,*,#778,.F.);
#778 = EDGE_CURVE('',#779,#751,#781,.T.);
#779 = VERTEX_POINT('',#780);
#780 = CARTESIAN_POINT('',(42.646560668945,200.94919887546,
    57.513879120691));
#781 = SURFACE_CURVE('',#782,(#787,#798),.PCURVE_S1.);
#782 = CIRCLE('',#783,12.);
#783 = AXIS2_PLACEMENT_3D('',#784,#785,#786);
#784 = CARTESIAN_POINT('',(30.646560668945,200.94919887546,
    57.513879120691));
#785 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#786 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#787 = PCURVE('',#72,#788);
#788 = DEFINITIONAL_REPRESENTATION('',(#789),#797);
#789 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#790,#791,#792,#793,#794,#795
,#796),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#790 = CARTESIAN_POINT('',(73.,4.));
#791 = CARTESIAN_POINT('',(93.784609690827,4.));
#792 = CARTESIAN_POINT('',(83.392304845413,-14.));
#793 = CARTESIAN_POINT('',(73.,-32.));
#794 = CARTESIAN_POINT('',(62.607695154587,-14.));
#795 = CARTESIAN_POINT('',(52.215390309173,4.));
#796 = CARTESIAN_POINT('',(73.,4.));
#797 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#798 = PCURVE('',#799,#804);
#799 = CYLINDRICAL_SURFACE('',#800,12.);
#800 = AXIS2_PLACEMENT_3D('',#801,#802,#803);
#801 = CARTESIAN_POINT('',(30.646560668945,200.94919887546,
    57.513879120691));
#802 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#803 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#804 = DEFINITIONAL_REPRESENTATION('',(#805),#809);
#805 = LINE('',#806,#807);
#806 = CARTESIAN_POINT('',(-1.570796326795,2.66E-15));
#807 = VECTOR('',#808,1.);
#808 = DIRECTION('',(1.,0.));
#809 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#810 = ORIENTED_EDGE('',*,*,#811,.F.);
#811 = EDGE_CURVE('',#57,#779,#812,.T.);
#812 = SURFACE_CURVE('',#813,(#817,#824),.PCURVE_S1.);
#813 = LINE('',#814,#815);
#814 = CARTESIAN_POINT('',(42.646560668945,198.21303772886,
    49.996338154403));
#815 = VECTOR('',#816,1.);
#816 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#817 = PCURVE('',#72,#818);
#818 = DEFINITIONAL_REPRESENTATION('',(#819),#823);
#819 = LINE('',#820,#821);
#820 = CARTESIAN_POINT('',(85.,0.));
#821 = VECTOR('',#822,1.);
#822 = DIRECTION('',(-0.,-1.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = PCURVE('',#825,#830);
#825 = PLANE('',#826);
#826 = AXIS2_PLACEMENT_3D('',#827,#828,#829);
#827 = CARTESIAN_POINT('',(42.646560668945,198.21303772886,
    49.996338154403));
#828 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#829 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#830 = DEFINITIONAL_REPRESENTATION('',(#831),#835);
#831 = LINE('',#832,#833);
#832 = CARTESIAN_POINT('',(0.,0.));
#833 = VECTOR('',#834,1.);
#834 = DIRECTION('',(0.,-1.));
#835 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#836 = ORIENTED_EDGE('',*,*,#56,.F.);
#837 = ADVANCED_FACE('',(#838,#886,#917,#948),#157,.T.);
#838 = FACE_BOUND('',#839,.T.);
#839 = EDGE_LOOP('',(#840,#841,#864,#885));
#840 = ORIENTED_EDGE('',*,*,#141,.T.);
#841 = ORIENTED_EDGE('',*,*,#842,.T.);
#842 = EDGE_CURVE('',#113,#843,#845,.T.);
#843 = VERTEX_POINT('',#844);
#844 = CARTESIAN_POINT('',(30.646560668946,130.55516903227,
    -5.191544461842));
#845 = SURFACE_CURVE('',#846,(#850,#857),.PCURVE_S1.);
#846 = LINE('',#847,#848);
#847 = CARTESIAN_POINT('',(30.646560668945,172.56152697943,
    -20.48060840454));
#848 = VECTOR('',#849,1.);
#849 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#850 = PCURVE('',#157,#851);
#851 = DEFINITIONAL_REPRESENTATION('',(#852),#856);
#852 = LINE('',#853,#854);
#853 = CARTESIAN_POINT('',(1.5E-16,-73.));
#854 = VECTOR('',#855,1.);
#855 = DIRECTION('',(1.,0.));
#856 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#857 = PCURVE('',#129,#858);
#858 = DEFINITIONAL_REPRESENTATION('',(#859),#863);
#859 = LINE('',#860,#861);
#860 = CARTESIAN_POINT('',(-1.570796326795,0.));
#861 = VECTOR('',#862,1.);
#862 = DIRECTION('',(-0.,1.));
#863 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#864 = ORIENTED_EDGE('',*,*,#865,.F.);
#865 = EDGE_CURVE('',#644,#843,#866,.T.);
#866 = SURFACE_CURVE('',#867,(#871,#878),.PCURVE_S1.);
#867 = LINE('',#868,#869);
#868 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    -5.191544461842));
#869 = VECTOR('',#870,1.);
#870 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#871 = PCURVE('',#157,#872);
#872 = DEFINITIONAL_REPRESENTATION('',(#873),#877);
#873 = LINE('',#874,#875);
#874 = CARTESIAN_POINT('',(44.702232430035,-2.73E-15));
#875 = VECTOR('',#876,1.);
#876 = DIRECTION('',(0.,-1.));
#877 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#878 = PCURVE('',#398,#879);
#879 = DEFINITIONAL_REPRESENTATION('',(#880),#884);
#880 = LINE('',#881,#882);
#881 = CARTESIAN_POINT('',(0.,0.));
#882 = VECTOR('',#883,1.);
#883 = DIRECTION('',(-0.,-1.));
#884 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#885 = ORIENTED_EDGE('',*,*,#643,.F.);
#886 = FACE_BOUND('',#887,.T.);
#887 = EDGE_LOOP('',(#888));
#888 = ORIENTED_EDGE('',*,*,#889,.F.);
#889 = EDGE_CURVE('',#890,#890,#892,.T.);
#890 = VERTEX_POINT('',#891);
#891 = CARTESIAN_POINT('',(22.646560668945,150.61970428408,
    -12.49443805788));
#892 = SURFACE_CURVE('',#893,(#898,#905),.PCURVE_S1.);
#893 = CIRCLE('',#894,1.65);
#894 = AXIS2_PLACEMENT_3D('',#895,#896,#897);
#895 = CARTESIAN_POINT('',(22.646560668945,149.06921145978,
    -11.93010482139));
#896 = DIRECTION('',(5.775204332976E-15,-0.342020143326,-0.939692620786)
  );
#897 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#898 = PCURVE('',#157,#899);
#899 = DEFINITIONAL_REPRESENTATION('',(#900),#904);
#900 = CIRCLE('',#901,1.65);
#901 = AXIS2_PLACEMENT_2D('',#902,#903);
#902 = CARTESIAN_POINT('',(25.,-65.));
#903 = DIRECTION('',(-1.,0.));
#904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#905 = PCURVE('',#906,#911);
#906 = CYLINDRICAL_SURFACE('',#907,1.65);
#907 = AXIS2_PLACEMENT_3D('',#908,#909,#910);
#908 = CARTESIAN_POINT('',(22.646560668945,149.06921145978,
    -11.93010482139));
#909 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#910 = DIRECTION('',(-5.132009588019E-15,0.939692620786,-0.342020143326)
  );
#911 = DEFINITIONAL_REPRESENTATION('',(#912),#916);
#912 = LINE('',#913,#914);
#913 = CARTESIAN_POINT('',(0.,0.));
#914 = VECTOR('',#915,1.);
#915 = DIRECTION('',(1.,0.));
#916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#917 = FACE_BOUND('',#918,.T.);
#918 = EDGE_LOOP('',(#919));
#919 = ORIENTED_EDGE('',*,*,#920,.F.);
#920 = EDGE_CURVE('',#921,#921,#923,.T.);
#921 = VERTEX_POINT('',#922);
#922 = CARTESIAN_POINT('',(0.146560668945,158.46613766764,
    -15.35030625465));
#923 = SURFACE_CURVE('',#924,(#929,#936),.PCURVE_S1.);
#924 = CIRCLE('',#925,10.);
#925 = AXIS2_PLACEMENT_3D('',#926,#927,#928);
#926 = CARTESIAN_POINT('',(0.146560668945,149.06921145978,
    -11.93010482139));
#927 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#928 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#929 = PCURVE('',#157,#930);
#930 = DEFINITIONAL_REPRESENTATION('',(#931),#935);
#931 = CIRCLE('',#932,10.);
#932 = AXIS2_PLACEMENT_2D('',#933,#934);
#933 = CARTESIAN_POINT('',(25.,-42.5));
#934 = DIRECTION('',(-1.,0.));
#935 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#936 = PCURVE('',#937,#942);
#937 = CYLINDRICAL_SURFACE('',#938,10.);
#938 = AXIS2_PLACEMENT_3D('',#939,#940,#941);
#939 = CARTESIAN_POINT('',(0.146560668945,149.06921145978,
    -11.93010482139));
#940 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#941 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#942 = DEFINITIONAL_REPRESENTATION('',(#943),#947);
#943 = LINE('',#944,#945);
#944 = CARTESIAN_POINT('',(0.,0.));
#945 = VECTOR('',#946,1.);
#946 = DIRECTION('',(1.,0.));
#947 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#948 = FACE_BOUND('',#949,.T.);
#949 = EDGE_LOOP('',(#950));
#950 = ORIENTED_EDGE('',*,*,#951,.F.);
#951 = EDGE_CURVE('',#952,#952,#954,.T.);
#952 = VERTEX_POINT('',#953);
#953 = CARTESIAN_POINT('',(-22.35343933105,150.61970428408,
    -12.49443805788));
#954 = SURFACE_CURVE('',#955,(#960,#967),.PCURVE_S1.);
#955 = CIRCLE('',#956,1.65);
#956 = AXIS2_PLACEMENT_3D('',#957,#958,#959);
#957 = CARTESIAN_POINT('',(-22.35343933105,149.06921145978,
    -11.93010482139));
#958 = DIRECTION('',(5.775204332976E-15,-0.342020143326,-0.939692620786)
  );
#959 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326)
  );
#960 = PCURVE('',#157,#961);
#961 = DEFINITIONAL_REPRESENTATION('',(#962),#966);
#962 = CIRCLE('',#963,1.65);
#963 = AXIS2_PLACEMENT_2D('',#964,#965);
#964 = CARTESIAN_POINT('',(25.,-20.));
#965 = DIRECTION('',(-1.,0.));
#966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#967 = PCURVE('',#968,#973);
#968 = CYLINDRICAL_SURFACE('',#969,1.65);
#969 = AXIS2_PLACEMENT_3D('',#970,#971,#972);
#970 = CARTESIAN_POINT('',(-22.35343933105,149.06921145978,
    -11.93010482139));
#971 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786)
  );
#972 = DIRECTION('',(-5.132009588019E-15,0.939692620786,-0.342020143326)
  );
#973 = DEFINITIONAL_REPRESENTATION('',(#974),#978);
#974 = LINE('',#975,#976);
#975 = CARTESIAN_POINT('',(0.,0.));
#976 = VECTOR('',#977,1.);
#977 = DIRECTION('',(1.,0.));
#978 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#979 = ADVANCED_FACE('',(#980,#1073,#1108,#1143,#1178,#1213),#100,.F.);
#980 = FACE_BOUND('',#981,.F.);
#981 = EDGE_LOOP('',(#982,#983,#1006,#1029,#1052));
#982 = ORIENTED_EDGE('',*,*,#84,.T.);
#983 = ORIENTED_EDGE('',*,*,#984,.T.);
#984 = EDGE_CURVE('',#85,#985,#987,.T.);
#985 = VERTEX_POINT('',#986);
#986 = CARTESIAN_POINT('',(42.646560668945,130.55516903227,
    7.578588807869));
#987 = SURFACE_CURVE('',#988,(#992,#999),.PCURVE_S1.);
#988 = LINE('',#989,#990);
#989 = CARTESIAN_POINT('',(42.646560668945,176.66576869934,
    -9.204296955109));
#990 = VECTOR('',#991,1.);
#991 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#992 = PCURVE('',#100,#993);
#993 = DEFINITIONAL_REPRESENTATION('',(#994),#998);
#994 = LINE('',#995,#996);
#995 = CARTESIAN_POINT('',(-30.37889134143,-29.27019373638));
#996 = VECTOR('',#997,1.);
#997 = DIRECTION('',(1.,0.));
#998 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#999 = PCURVE('',#129,#1000);
#1000 = DEFINITIONAL_REPRESENTATION('',(#1001),#1005);
#1001 = LINE('',#1002,#1003);
#1002 = CARTESIAN_POINT('',(-0.,0.));
#1003 = VECTOR('',#1004,1.);
#1004 = DIRECTION('',(-0.,1.));
#1005 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1006 = ORIENTED_EDGE('',*,*,#1007,.T.);
#1007 = EDGE_CURVE('',#985,#1008,#1010,.T.);
#1008 = VERTEX_POINT('',#1009);
#1009 = CARTESIAN_POINT('',(42.646560668945,130.55516903227,
    74.621788473851));
#1010 = SURFACE_CURVE('',#1011,(#1015,#1022),.PCURVE_S1.);
#1011 = LINE('',#1012,#1013);
#1012 = CARTESIAN_POINT('',(42.646560668946,130.55516903227,
    -5.191544461842));
#1013 = VECTOR('',#1014,1.);
#1014 = DIRECTION('',(-3.845531969661E-15,-7.216449660064E-16,1.));
#1015 = PCURVE('',#100,#1016);
#1016 = DEFINITIONAL_REPRESENTATION('',(#1017),#1021);
#1017 = LINE('',#1018,#1019);
#1018 = CARTESIAN_POINT('',(14.323341088603,-41.27019373638));
#1019 = VECTOR('',#1020,1.);
#1020 = DIRECTION('',(0.342020143326,0.939692620786));
#1021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1022 = PCURVE('',#398,#1023);
#1023 = DEFINITIONAL_REPRESENTATION('',(#1024),#1028);
#1024 = LINE('',#1025,#1026);
#1025 = CARTESIAN_POINT('',(-9.5E-16,-85.));
#1026 = VECTOR('',#1027,1.);
#1027 = DIRECTION('',(1.,0.));
#1028 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1029 = ORIENTED_EDGE('',*,*,#1030,.T.);
#1030 = EDGE_CURVE('',#1008,#1031,#1033,.T.);
#1031 = VERTEX_POINT('',#1032);
#1032 = CARTESIAN_POINT('',(42.646560668945,196.33365248728,
    50.680378441055));
#1033 = SURFACE_CURVE('',#1034,(#1038,#1045),.PCURVE_S1.);
#1034 = LINE('',#1035,#1036);
#1035 = CARTESIAN_POINT('',(42.646560668945,130.55516903227,
    74.621788473851));
#1036 = VECTOR('',#1037,1.);
#1037 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1038 = PCURVE('',#100,#1039);
#1039 = DEFINITIONAL_REPRESENTATION('',(#1040),#1044);
#1040 = LINE('',#1041,#1042);
#1041 = CARTESIAN_POINT('',(41.621108658568,33.729806263612));
#1042 = VECTOR('',#1043,1.);
#1043 = DIRECTION('',(-1.,0.));
#1044 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1045 = PCURVE('',#426,#1046);
#1046 = DEFINITIONAL_REPRESENTATION('',(#1047),#1051);
#1047 = LINE('',#1048,#1049);
#1048 = CARTESIAN_POINT('',(4.49E-15,-85.));
#1049 = VECTOR('',#1050,1.);
#1050 = DIRECTION('',(1.,0.));
#1051 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1052 = ORIENTED_EDGE('',*,*,#1053,.T.);
#1053 = EDGE_CURVE('',#1031,#57,#1054,.T.);
#1054 = SURFACE_CURVE('',#1055,(#1059,#1066),.PCURVE_S1.);
#1055 = LINE('',#1056,#1057);
#1056 = CARTESIAN_POINT('',(42.646560668945,130.55516903227,
    74.621788473851));
#1057 = VECTOR('',#1058,1.);
#1058 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1059 = PCURVE('',#100,#1060);
#1060 = DEFINITIONAL_REPRESENTATION('',(#1061),#1065);
#1061 = LINE('',#1062,#1063);
#1062 = CARTESIAN_POINT('',(41.621108658568,33.729806263612));
#1063 = VECTOR('',#1064,1.);
#1064 = DIRECTION('',(-1.,0.));
#1065 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1066 = PCURVE('',#825,#1067);
#1067 = DEFINITIONAL_REPRESENTATION('',(#1068),#1072);
#1068 = LINE('',#1069,#1070);
#1069 = CARTESIAN_POINT('',(72.,1.584E-14));
#1070 = VECTOR('',#1071,1.);
#1071 = DIRECTION('',(-1.,-2.2E-16));
#1072 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1073 = FACE_BOUND('',#1074,.F.);
#1074 = EDGE_LOOP('',(#1075));
#1075 = ORIENTED_EDGE('',*,*,#1076,.T.);
#1076 = EDGE_CURVE('',#1077,#1077,#1079,.T.);
#1077 = VERTEX_POINT('',#1078);
#1078 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    21.432767705578));
#1079 = SURFACE_CURVE('',#1080,(#1085,#1096),.PCURVE_S1.);
#1080 = CIRCLE('',#1081,0.95);
#1081 = AXIS2_PLACEMENT_3D('',#1082,#1083,#1084);
#1082 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    20.540059715831));
#1083 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1084 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1085 = PCURVE('',#100,#1086);
#1086 = DEFINITIONAL_REPRESENTATION('',(#1087),#1095);
#1087 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1088,#1089,#1090,#1091,
#1092,#1093,#1094),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1088 = CARTESIAN_POINT('',(-18.22240727713,-1.091508863315));
#1089 = CARTESIAN_POINT('',(-16.57695900994,-1.091508863315));
#1090 = CARTESIAN_POINT('',(-17.39968314353,-2.516508863315));
#1091 = CARTESIAN_POINT('',(-18.22240727713,-3.941508863315));
#1092 = CARTESIAN_POINT('',(-19.04513141072,-2.516508863315));
#1093 = CARTESIAN_POINT('',(-19.86785554432,-1.091508863315));
#1094 = CARTESIAN_POINT('',(-18.22240727713,-1.091508863315));
#1095 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1096 = PCURVE('',#1097,#1102);
#1097 = CYLINDRICAL_SURFACE('',#1098,0.95);
#1098 = AXIS2_PLACEMENT_3D('',#1099,#1100,#1101);
#1099 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    20.540059715831));
#1100 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#1101 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1102 = DEFINITIONAL_REPRESENTATION('',(#1103),#1107);
#1103 = LINE('',#1104,#1105);
#1104 = CARTESIAN_POINT('',(0.,0.));
#1105 = VECTOR('',#1106,1.);
#1106 = DIRECTION('',(1.,0.));
#1107 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1108 = FACE_BOUND('',#1109,.F.);
#1109 = EDGE_LOOP('',(#1110));
#1110 = ORIENTED_EDGE('',*,*,#1111,.T.);
#1111 = EDGE_CURVE('',#1112,#1112,#1114,.T.);
#1112 = VERTEX_POINT('',#1113);
#1113 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    21.432767705578));
#1114 = SURFACE_CURVE('',#1115,(#1120,#1131),.PCURVE_S1.);
#1115 = CIRCLE('',#1116,0.95);
#1116 = AXIS2_PLACEMENT_3D('',#1117,#1118,#1119);
#1117 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    20.540059715831));
#1118 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1119 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1120 = PCURVE('',#100,#1121);
#1121 = DEFINITIONAL_REPRESENTATION('',(#1122),#1130);
#1122 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1123,#1124,#1125,#1126,
#1127,#1128,#1129),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1123 = CARTESIAN_POINT('',(13.727141829591,-12.72019373638));
#1124 = CARTESIAN_POINT('',(15.372590096781,-12.72019373638));
#1125 = CARTESIAN_POINT('',(14.549865963186,-14.14519373638));
#1126 = CARTESIAN_POINT('',(13.727141829591,-15.57019373638));
#1127 = CARTESIAN_POINT('',(12.904417695996,-14.14519373638));
#1128 = CARTESIAN_POINT('',(12.0816935624,-12.72019373638));
#1129 = CARTESIAN_POINT('',(13.727141829591,-12.72019373638));
#1130 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1131 = PCURVE('',#1132,#1137);
#1132 = CYLINDRICAL_SURFACE('',#1133,0.95);
#1133 = AXIS2_PLACEMENT_3D('',#1134,#1135,#1136);
#1134 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    20.540059715831));
#1135 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#1136 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1137 = DEFINITIONAL_REPRESENTATION('',(#1138),#1142);
#1138 = LINE('',#1139,#1140);
#1139 = CARTESIAN_POINT('',(0.,0.));
#1140 = VECTOR('',#1141,1.);
#1141 = DIRECTION('',(1.,0.));
#1142 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1143 = FACE_BOUND('',#1144,.F.);
#1144 = EDGE_LOOP('',(#1145));
#1145 = ORIENTED_EDGE('',*,*,#1146,.T.);
#1146 = EDGE_CURVE('',#1147,#1147,#1149,.T.);
#1147 = VERTEX_POINT('',#1148);
#1148 = CARTESIAN_POINT('',(42.646560668945,159.76165480551,
    32.066163830596));
#1149 = SURFACE_CURVE('',#1150,(#1155,#1166),.PCURVE_S1.);
#1150 = CIRCLE('',#1151,10.);
#1151 = AXIS2_PLACEMENT_3D('',#1152,#1153,#1154);
#1152 = CARTESIAN_POINT('',(42.646560668945,156.34145337225,
    22.669237622737));
#1153 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1154 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1155 = PCURVE('',#100,#1156);
#1156 = DEFINITIONAL_REPRESENTATION('',(#1157),#1165);
#1157 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1158,#1159,#1160,#1161,
#1162,#1163,#1164),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1158 = CARTESIAN_POINT('',(-0.378891341432,3.729806263612));
#1159 = CARTESIAN_POINT('',(16.941616734257,3.729806263612));
#1160 = CARTESIAN_POINT('',(8.281362696412,-11.27019373638));
#1161 = CARTESIAN_POINT('',(-0.378891341432,-26.27019373638));
#1162 = CARTESIAN_POINT('',(-9.039145379276,-11.27019373638));
#1163 = CARTESIAN_POINT('',(-17.69939941712,3.729806263612));
#1164 = CARTESIAN_POINT('',(-0.378891341432,3.729806263612));
#1165 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1166 = PCURVE('',#1167,#1172);
#1167 = CYLINDRICAL_SURFACE('',#1168,10.);
#1168 = AXIS2_PLACEMENT_3D('',#1169,#1170,#1171);
#1169 = CARTESIAN_POINT('',(42.646560668945,156.34145337225,
    22.669237622737));
#1170 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1171 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1172 = DEFINITIONAL_REPRESENTATION('',(#1173),#1177);
#1173 = LINE('',#1174,#1175);
#1174 = CARTESIAN_POINT('',(0.,0.));
#1175 = VECTOR('',#1176,1.);
#1176 = DIRECTION('',(1.,0.));
#1177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1178 = FACE_BOUND('',#1179,.F.);
#1179 = EDGE_LOOP('',(#1180));
#1180 = ORIENTED_EDGE('',*,*,#1181,.T.);
#1181 = EDGE_CURVE('',#1182,#1182,#1184,.T.);
#1182 = VERTEX_POINT('',#1183);
#1183 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    45.432767705578));
#1184 = SURFACE_CURVE('',#1185,(#1190,#1201),.PCURVE_S1.);
#1185 = CIRCLE('',#1186,0.95);
#1186 = AXIS2_PLACEMENT_3D('',#1187,#1188,#1189);
#1187 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    44.540059715831));
#1188 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1189 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1190 = PCURVE('',#100,#1191);
#1191 = DEFINITIONAL_REPRESENTATION('',(#1192),#1200);
#1192 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1193,#1194,#1195,#1196,
#1197,#1198,#1199),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1193 = CARTESIAN_POINT('',(-10.01392383731,21.461114035547));
#1194 = CARTESIAN_POINT('',(-8.368475570124,21.461114035547));
#1195 = CARTESIAN_POINT('',(-9.191199703719,20.036114035547));
#1196 = CARTESIAN_POINT('',(-10.01392383731,18.611114035547));
#1197 = CARTESIAN_POINT('',(-10.8366479709,20.036114035547));
#1198 = CARTESIAN_POINT('',(-11.6593721045,21.461114035547));
#1199 = CARTESIAN_POINT('',(-10.01392383731,21.461114035547));
#1200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1201 = PCURVE('',#1202,#1207);
#1202 = CYLINDRICAL_SURFACE('',#1203,0.95);
#1203 = AXIS2_PLACEMENT_3D('',#1204,#1205,#1206);
#1204 = CARTESIAN_POINT('',(42.646560668945,174.55516903227,
    44.540059715831));
#1205 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#1206 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1207 = DEFINITIONAL_REPRESENTATION('',(#1208),#1212);
#1208 = LINE('',#1209,#1210);
#1209 = CARTESIAN_POINT('',(0.,0.));
#1210 = VECTOR('',#1211,1.);
#1211 = DIRECTION('',(1.,0.));
#1212 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1213 = FACE_BOUND('',#1214,.F.);
#1214 = EDGE_LOOP('',(#1215));
#1215 = ORIENTED_EDGE('',*,*,#1216,.T.);
#1216 = EDGE_CURVE('',#1217,#1217,#1219,.T.);
#1217 = VERTEX_POINT('',#1218);
#1218 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    45.432767705578));
#1219 = SURFACE_CURVE('',#1220,(#1225,#1236),.PCURVE_S1.);
#1220 = CIRCLE('',#1221,0.95);
#1221 = AXIS2_PLACEMENT_3D('',#1222,#1223,#1224);
#1222 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    44.540059715831));
#1223 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1224 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1225 = PCURVE('',#100,#1226);
#1226 = DEFINITIONAL_REPRESENTATION('',(#1227),#1235);
#1227 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1228,#1229,#1230,#1231,
#1232,#1233,#1234),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1228 = CARTESIAN_POINT('',(21.935625269407,9.832429162474));
#1229 = CARTESIAN_POINT('',(23.581073536597,9.832429162474));
#1230 = CARTESIAN_POINT('',(22.758349403002,8.407429162474));
#1231 = CARTESIAN_POINT('',(21.935625269407,6.982429162474));
#1232 = CARTESIAN_POINT('',(21.112901135812,8.407429162474));
#1233 = CARTESIAN_POINT('',(20.290177002216,9.832429162474));
#1234 = CARTESIAN_POINT('',(21.935625269407,9.832429162474));
#1235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1236 = PCURVE('',#1237,#1242);
#1237 = CYLINDRICAL_SURFACE('',#1238,0.95);
#1238 = AXIS2_PLACEMENT_3D('',#1239,#1240,#1241);
#1239 = CARTESIAN_POINT('',(42.646560668945,140.55516903227,
    44.540059715831));
#1240 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#1241 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1242 = DEFINITIONAL_REPRESENTATION('',(#1243),#1247);
#1243 = LINE('',#1244,#1245);
#1244 = CARTESIAN_POINT('',(0.,0.));
#1245 = VECTOR('',#1246,1.);
#1246 = DIRECTION('',(1.,0.));
#1247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1248 = ADVANCED_FACE('',(#1249),#129,.T.);
#1249 = FACE_BOUND('',#1250,.F.);
#1250 = EDGE_LOOP('',(#1251,#1252,#1253,#1285));
#1251 = ORIENTED_EDGE('',*,*,#112,.T.);
#1252 = ORIENTED_EDGE('',*,*,#842,.T.);
#1253 = ORIENTED_EDGE('',*,*,#1254,.F.);
#1254 = EDGE_CURVE('',#985,#843,#1255,.T.);
#1255 = SURFACE_CURVE('',#1256,(#1261,#1274),.PCURVE_S1.);
#1256 = ELLIPSE('',#1257,12.770133269711,12.);
#1257 = AXIS2_PLACEMENT_3D('',#1258,#1259,#1260);
#1258 = CARTESIAN_POINT('',(30.646560668945,130.55516903227,
    7.578588807869));
#1259 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660063E-16));
#1260 = DIRECTION('',(-3.845531969661E-15,-7.216449660064E-16,1.));
#1261 = PCURVE('',#129,#1262);
#1262 = DEFINITIONAL_REPRESENTATION('',(#1263),#1273);
#1263 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1264,#1265,#1266,#1267,#1268,
    #1269,#1270,#1271,#1272),.UNSPECIFIED.,.F.,.F.,(9,9),(1.570796326795
    ,3.14159265359),.PIECEWISE_BEZIER_KNOTS.);
#1264 = CARTESIAN_POINT('',(0.,49.069875241229));
#1265 = CARTESIAN_POINT('',(-0.196349540849,48.212290580657));
#1266 = CARTESIAN_POINT('',(-0.392699081699,47.354705716461));
#1267 = CARTESIAN_POINT('',(-0.589048622549,46.547503597336));
#1268 = CARTESIAN_POINT('',(-0.785398163393,45.841057073732));
#1269 = CARTESIAN_POINT('',(-0.981747704249,45.279555989517));
#1270 = CARTESIAN_POINT('',(-1.178097245096,44.894674161507));
#1271 = CARTESIAN_POINT('',(-1.374446785946,44.702232430035));
#1272 = CARTESIAN_POINT('',(-1.570796326795,44.702232430035));
#1273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1274 = PCURVE('',#398,#1275);
#1275 = DEFINITIONAL_REPRESENTATION('',(#1276),#1284);
#1276 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1277,#1278,#1279,#1280,
#1281,#1282,#1283),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1277 = CARTESIAN_POINT('',(25.540266539422,-73.));
#1278 = CARTESIAN_POINT('',(25.540266539422,-93.78460969082));
#1279 = CARTESIAN_POINT('',(6.385066634855,-83.39230484541));
#1280 = CARTESIAN_POINT('',(-12.77013326971,-73.));
#1281 = CARTESIAN_POINT('',(6.385066634855,-62.60769515458));
#1282 = CARTESIAN_POINT('',(25.540266539422,-52.21539030917));
#1283 = CARTESIAN_POINT('',(25.540266539422,-73.));
#1284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1285 = ORIENTED_EDGE('',*,*,#984,.F.);
#1286 = ADVANCED_FACE('',(#1287),#219,.F.);
#1287 = FACE_BOUND('',#1288,.F.);
#1288 = EDGE_LOOP('',(#1289,#1290,#1313,#1344));
#1289 = ORIENTED_EDGE('',*,*,#202,.F.);
#1290 = ORIENTED_EDGE('',*,*,#1291,.T.);
#1291 = EDGE_CURVE('',#203,#1292,#1294,.T.);
#1292 = VERTEX_POINT('',#1293);
#1293 = CARTESIAN_POINT('',(20.146560668945,167.28594349865,
    -5.737110890813));
#1294 = SEAM_CURVE('',#1295,(#1299,#1306),.PCURVE_S1.);
#1295 = LINE('',#1296,#1297);
#1296 = CARTESIAN_POINT('',(20.146560668945,176.68286970651,
    -9.15731232407));
#1297 = VECTOR('',#1298,1.);
#1298 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1299 = PCURVE('',#219,#1300);
#1300 = DEFINITIONAL_REPRESENTATION('',(#1301),#1305);
#1301 = LINE('',#1302,#1303);
#1302 = CARTESIAN_POINT('',(6.28318530718,0.));
#1303 = VECTOR('',#1304,1.);
#1304 = DIRECTION('',(0.,-1.));
#1305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1306 = PCURVE('',#219,#1307);
#1307 = DEFINITIONAL_REPRESENTATION('',(#1308),#1312);
#1308 = LINE('',#1309,#1310);
#1309 = CARTESIAN_POINT('',(0.,0.));
#1310 = VECTOR('',#1311,1.);
#1311 = DIRECTION('',(0.,-1.));
#1312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1313 = ORIENTED_EDGE('',*,*,#1314,.T.);
#1314 = EDGE_CURVE('',#1292,#1292,#1315,.T.);
#1315 = SURFACE_CURVE('',#1316,(#1321,#1328),.PCURVE_S1.);
#1316 = CIRCLE('',#1317,2.05);
#1317 = AXIS2_PLACEMENT_3D('',#1318,#1319,#1320);
#1318 = CARTESIAN_POINT('',(20.146560668945,166.58480220483,
    -7.663480763424));
#1319 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1320 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1321 = PCURVE('',#219,#1322);
#1322 = DEFINITIONAL_REPRESENTATION('',(#1323),#1327);
#1323 = LINE('',#1324,#1325);
#1324 = CARTESIAN_POINT('',(0.,-10.));
#1325 = VECTOR('',#1326,1.);
#1326 = DIRECTION('',(1.,0.));
#1327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1328 = PCURVE('',#1329,#1334);
#1329 = PLANE('',#1330);
#1330 = AXIS2_PLACEMENT_3D('',#1331,#1332,#1333);
#1331 = CARTESIAN_POINT('',(35.146560668945,192.23631295425,
    62.813465795519));
#1332 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1333 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#1334 = DEFINITIONAL_REPRESENTATION('',(#1335),#1343);
#1335 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1336,#1337,#1338,#1339,
#1340,#1341,#1342),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1336 = CARTESIAN_POINT('',(72.95,-15.));
#1337 = CARTESIAN_POINT('',(72.95,-11.44929584448));
#1338 = CARTESIAN_POINT('',(76.025,-13.22464792224));
#1339 = CARTESIAN_POINT('',(79.1,-15.));
#1340 = CARTESIAN_POINT('',(76.025,-16.77535207775));
#1341 = CARTESIAN_POINT('',(72.95,-18.55070415551));
#1342 = CARTESIAN_POINT('',(72.95,-15.));
#1343 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1344 = ORIENTED_EDGE('',*,*,#1291,.F.);
#1345 = ADVANCED_FACE('',(#1346),#250,.F.);
#1346 = FACE_BOUND('',#1347,.F.);
#1347 = EDGE_LOOP('',(#1348,#1349,#1372,#1398));
#1348 = ORIENTED_EDGE('',*,*,#233,.F.);
#1349 = ORIENTED_EDGE('',*,*,#1350,.T.);
#1350 = EDGE_CURVE('',#234,#1351,#1353,.T.);
#1351 = VERTEX_POINT('',#1352);
#1352 = CARTESIAN_POINT('',(20.146560668945,186.09705138156,
    45.945983252412));
#1353 = SEAM_CURVE('',#1354,(#1358,#1365),.PCURVE_S1.);
#1354 = LINE('',#1355,#1356);
#1355 = CARTESIAN_POINT('',(20.146560668945,195.49397758942,
    42.525781819155));
#1356 = VECTOR('',#1357,1.);
#1357 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1358 = PCURVE('',#250,#1359);
#1359 = DEFINITIONAL_REPRESENTATION('',(#1360),#1364);
#1360 = LINE('',#1361,#1362);
#1361 = CARTESIAN_POINT('',(6.28318530718,0.));
#1362 = VECTOR('',#1363,1.);
#1363 = DIRECTION('',(0.,-1.));
#1364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1365 = PCURVE('',#250,#1366);
#1366 = DEFINITIONAL_REPRESENTATION('',(#1367),#1371);
#1367 = LINE('',#1368,#1369);
#1368 = CARTESIAN_POINT('',(0.,0.));
#1369 = VECTOR('',#1370,1.);
#1370 = DIRECTION('',(0.,-1.));
#1371 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1372 = ORIENTED_EDGE('',*,*,#1373,.T.);
#1373 = EDGE_CURVE('',#1351,#1351,#1374,.T.);
#1374 = SURFACE_CURVE('',#1375,(#1380,#1387),.PCURVE_S1.);
#1375 = CIRCLE('',#1376,2.05);
#1376 = AXIS2_PLACEMENT_3D('',#1377,#1378,#1379);
#1377 = CARTESIAN_POINT('',(20.146560668945,185.39591008774,
    44.019613379801));
#1378 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1379 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1380 = PCURVE('',#250,#1381);
#1381 = DEFINITIONAL_REPRESENTATION('',(#1382),#1386);
#1382 = LINE('',#1383,#1384);
#1383 = CARTESIAN_POINT('',(0.,-10.));
#1384 = VECTOR('',#1385,1.);
#1385 = DIRECTION('',(1.,0.));
#1386 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1387 = PCURVE('',#1329,#1388);
#1388 = DEFINITIONAL_REPRESENTATION('',(#1389),#1397);
#1389 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1390,#1391,#1392,#1393,
#1394,#1395,#1396),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1390 = CARTESIAN_POINT('',(17.95,-15.));
#1391 = CARTESIAN_POINT('',(17.95,-11.44929584448));
#1392 = CARTESIAN_POINT('',(21.025,-13.22464792224));
#1393 = CARTESIAN_POINT('',(24.1,-15.));
#1394 = CARTESIAN_POINT('',(21.025,-16.77535207775));
#1395 = CARTESIAN_POINT('',(17.95,-18.55070415551));
#1396 = CARTESIAN_POINT('',(17.95,-15.));
#1397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1398 = ORIENTED_EDGE('',*,*,#1350,.F.);
#1399 = ADVANCED_FACE('',(#1400),#281,.F.);
#1400 = FACE_BOUND('',#1401,.F.);
#1401 = EDGE_LOOP('',(#1402,#1403,#1426,#1452));
#1402 = ORIENTED_EDGE('',*,*,#264,.F.);
#1403 = ORIENTED_EDGE('',*,*,#1404,.T.);
#1404 = EDGE_CURVE('',#265,#1405,#1407,.T.);
#1405 = VERTEX_POINT('',#1406);
#1406 = CARTESIAN_POINT('',(0.146560668945,179.93395613517,
    29.013018229025));
#1407 = SEAM_CURVE('',#1408,(#1412,#1419),.PCURVE_S1.);
#1408 = LINE('',#1409,#1410);
#1409 = CARTESIAN_POINT('',(0.146560668945,189.33088234303,
    25.592816795769));
#1410 = VECTOR('',#1411,1.);
#1411 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1412 = PCURVE('',#281,#1413);
#1413 = DEFINITIONAL_REPRESENTATION('',(#1414),#1418);
#1414 = LINE('',#1415,#1416);
#1415 = CARTESIAN_POINT('',(6.28318530718,0.));
#1416 = VECTOR('',#1417,1.);
#1417 = DIRECTION('',(0.,-1.));
#1418 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1419 = PCURVE('',#281,#1420);
#1420 = DEFINITIONAL_REPRESENTATION('',(#1421),#1425);
#1421 = LINE('',#1422,#1423);
#1422 = CARTESIAN_POINT('',(0.,0.));
#1423 = VECTOR('',#1424,1.);
#1424 = DIRECTION('',(0.,-1.));
#1425 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1426 = ORIENTED_EDGE('',*,*,#1427,.T.);
#1427 = EDGE_CURVE('',#1405,#1405,#1428,.T.);
#1428 = SURFACE_CURVE('',#1429,(#1434,#1441),.PCURVE_S1.);
#1429 = CIRCLE('',#1430,12.5);
#1430 = AXIS2_PLACEMENT_3D('',#1431,#1432,#1433);
#1431 = CARTESIAN_POINT('',(0.146560668945,175.6587043436,
    17.266860469201));
#1432 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1433 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1434 = PCURVE('',#281,#1435);
#1435 = DEFINITIONAL_REPRESENTATION('',(#1436),#1440);
#1436 = LINE('',#1437,#1438);
#1437 = CARTESIAN_POINT('',(0.,-10.));
#1438 = VECTOR('',#1439,1.);
#1439 = DIRECTION('',(1.,0.));
#1440 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1441 = PCURVE('',#1329,#1442);
#1442 = DEFINITIONAL_REPRESENTATION('',(#1443),#1451);
#1443 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1444,#1445,#1446,#1447,
#1448,#1449,#1450),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1444 = CARTESIAN_POINT('',(35.969685,-35.));
#1445 = CARTESIAN_POINT('',(35.969685,-13.34936490538));
#1446 = CARTESIAN_POINT('',(54.719685,-24.17468245269));
#1447 = CARTESIAN_POINT('',(73.469685,-35.));
#1448 = CARTESIAN_POINT('',(54.719685,-45.8253175473));
#1449 = CARTESIAN_POINT('',(35.969685,-56.65063509461));
#1450 = CARTESIAN_POINT('',(35.969685,-35.));
#1451 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1452 = ORIENTED_EDGE('',*,*,#1404,.F.);
#1453 = ADVANCED_FACE('',(#1454),#312,.F.);
#1454 = FACE_BOUND('',#1455,.F.);
#1455 = EDGE_LOOP('',(#1456,#1457,#1480,#1506));
#1456 = ORIENTED_EDGE('',*,*,#295,.F.);
#1457 = ORIENTED_EDGE('',*,*,#1458,.T.);
#1458 = EDGE_CURVE('',#296,#1459,#1461,.T.);
#1459 = VERTEX_POINT('',#1460);
#1460 = CARTESIAN_POINT('',(-19.85343933105,186.09705138156,
    45.945983252412));
#1461 = SEAM_CURVE('',#1462,(#1466,#1473),.PCURVE_S1.);
#1462 = LINE('',#1463,#1464);
#1463 = CARTESIAN_POINT('',(-19.85343933105,195.49397758942,
    42.525781819155));
#1464 = VECTOR('',#1465,1.);
#1465 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1466 = PCURVE('',#312,#1467);
#1467 = DEFINITIONAL_REPRESENTATION('',(#1468),#1472);
#1468 = LINE('',#1469,#1470);
#1469 = CARTESIAN_POINT('',(6.28318530718,0.));
#1470 = VECTOR('',#1471,1.);
#1471 = DIRECTION('',(0.,-1.));
#1472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1473 = PCURVE('',#312,#1474);
#1474 = DEFINITIONAL_REPRESENTATION('',(#1475),#1479);
#1475 = LINE('',#1476,#1477);
#1476 = CARTESIAN_POINT('',(0.,0.));
#1477 = VECTOR('',#1478,1.);
#1478 = DIRECTION('',(0.,-1.));
#1479 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1480 = ORIENTED_EDGE('',*,*,#1481,.T.);
#1481 = EDGE_CURVE('',#1459,#1459,#1482,.T.);
#1482 = SURFACE_CURVE('',#1483,(#1488,#1495),.PCURVE_S1.);
#1483 = CIRCLE('',#1484,2.05);
#1484 = AXIS2_PLACEMENT_3D('',#1485,#1486,#1487);
#1485 = CARTESIAN_POINT('',(-19.85343933105,185.39591008774,
    44.019613379801));
#1486 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1487 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1488 = PCURVE('',#312,#1489);
#1489 = DEFINITIONAL_REPRESENTATION('',(#1490),#1494);
#1490 = LINE('',#1491,#1492);
#1491 = CARTESIAN_POINT('',(0.,-10.));
#1492 = VECTOR('',#1493,1.);
#1493 = DIRECTION('',(1.,0.));
#1494 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1495 = PCURVE('',#1329,#1496);
#1496 = DEFINITIONAL_REPRESENTATION('',(#1497),#1505);
#1497 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1498,#1499,#1500,#1501,
#1502,#1503,#1504),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1498 = CARTESIAN_POINT('',(17.95,-55.));
#1499 = CARTESIAN_POINT('',(17.95,-51.44929584448));
#1500 = CARTESIAN_POINT('',(21.025,-53.22464792224));
#1501 = CARTESIAN_POINT('',(24.1,-55.));
#1502 = CARTESIAN_POINT('',(21.025,-56.77535207775));
#1503 = CARTESIAN_POINT('',(17.95,-58.55070415551));
#1504 = CARTESIAN_POINT('',(17.95,-55.));
#1505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1506 = ORIENTED_EDGE('',*,*,#1458,.F.);
#1507 = ADVANCED_FACE('',(#1508),#343,.F.);
#1508 = FACE_BOUND('',#1509,.F.);
#1509 = EDGE_LOOP('',(#1510,#1511,#1534,#1560));
#1510 = ORIENTED_EDGE('',*,*,#326,.F.);
#1511 = ORIENTED_EDGE('',*,*,#1512,.T.);
#1512 = EDGE_CURVE('',#327,#1513,#1515,.T.);
#1513 = VERTEX_POINT('',#1514);
#1514 = CARTESIAN_POINT('',(-19.85343933105,167.28594349865,
    -5.737110890813));
#1515 = SEAM_CURVE('',#1516,(#1520,#1527),.PCURVE_S1.);
#1516 = LINE('',#1517,#1518);
#1517 = CARTESIAN_POINT('',(-19.85343933105,176.68286970651,
    -9.15731232407));
#1518 = VECTOR('',#1519,1.);
#1519 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1520 = PCURVE('',#343,#1521);
#1521 = DEFINITIONAL_REPRESENTATION('',(#1522),#1526);
#1522 = LINE('',#1523,#1524);
#1523 = CARTESIAN_POINT('',(6.28318530718,0.));
#1524 = VECTOR('',#1525,1.);
#1525 = DIRECTION('',(0.,-1.));
#1526 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1527 = PCURVE('',#343,#1528);
#1528 = DEFINITIONAL_REPRESENTATION('',(#1529),#1533);
#1529 = LINE('',#1530,#1531);
#1530 = CARTESIAN_POINT('',(0.,0.));
#1531 = VECTOR('',#1532,1.);
#1532 = DIRECTION('',(0.,-1.));
#1533 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1534 = ORIENTED_EDGE('',*,*,#1535,.T.);
#1535 = EDGE_CURVE('',#1513,#1513,#1536,.T.);
#1536 = SURFACE_CURVE('',#1537,(#1542,#1549),.PCURVE_S1.);
#1537 = CIRCLE('',#1538,2.05);
#1538 = AXIS2_PLACEMENT_3D('',#1539,#1540,#1541);
#1539 = CARTESIAN_POINT('',(-19.85343933105,166.58480220483,
    -7.663480763424));
#1540 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1541 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1542 = PCURVE('',#343,#1543);
#1543 = DEFINITIONAL_REPRESENTATION('',(#1544),#1548);
#1544 = LINE('',#1545,#1546);
#1545 = CARTESIAN_POINT('',(0.,-10.));
#1546 = VECTOR('',#1547,1.);
#1547 = DIRECTION('',(1.,0.));
#1548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1549 = PCURVE('',#1329,#1550);
#1550 = DEFINITIONAL_REPRESENTATION('',(#1551),#1559);
#1551 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1552,#1553,#1554,#1555,
#1556,#1557,#1558),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1552 = CARTESIAN_POINT('',(72.95,-55.));
#1553 = CARTESIAN_POINT('',(72.95,-51.44929584448));
#1554 = CARTESIAN_POINT('',(76.025,-53.22464792224));
#1555 = CARTESIAN_POINT('',(79.1,-55.));
#1556 = CARTESIAN_POINT('',(76.025,-56.77535207775));
#1557 = CARTESIAN_POINT('',(72.95,-58.55070415551));
#1558 = CARTESIAN_POINT('',(72.95,-55.));
#1559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1560 = ORIENTED_EDGE('',*,*,#1512,.F.);
#1561 = ADVANCED_FACE('',(#1562),#452,.T.);
#1562 = FACE_BOUND('',#1563,.T.);
#1563 = EDGE_LOOP('',(#1564,#1592,#1613,#1614));
#1564 = ORIENTED_EDGE('',*,*,#1565,.T.);
#1565 = EDGE_CURVE('',#411,#1566,#1568,.T.);
#1566 = VERTEX_POINT('',#1567);
#1567 = CARTESIAN_POINT('',(-42.35343933105,199.06981363389,
    58.197919407342));
#1568 = SURFACE_CURVE('',#1569,(#1573,#1580),.PCURVE_S1.);
#1569 = LINE('',#1570,#1571);
#1570 = CARTESIAN_POINT('',(-42.35343933105,196.33365248728,
    50.680378441054));
#1571 = VECTOR('',#1572,1.);
#1572 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1573 = PCURVE('',#452,#1574);
#1574 = DEFINITIONAL_REPRESENTATION('',(#1575),#1579);
#1575 = LINE('',#1576,#1577);
#1576 = CARTESIAN_POINT('',(-0.,0.));
#1577 = VECTOR('',#1578,1.);
#1578 = DIRECTION('',(0.,-1.));
#1579 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1580 = PCURVE('',#1581,#1586);
#1581 = PLANE('',#1582);
#1582 = AXIS2_PLACEMENT_3D('',#1583,#1584,#1585);
#1583 = CARTESIAN_POINT('',(42.646560668945,196.33365248728,
    50.680378441055));
#1584 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1585 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#1586 = DEFINITIONAL_REPRESENTATION('',(#1587),#1591);
#1587 = LINE('',#1588,#1589);
#1588 = CARTESIAN_POINT('',(85.,0.));
#1589 = VECTOR('',#1590,1.);
#1590 = DIRECTION('',(0.,-1.));
#1591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1592 = ORIENTED_EDGE('',*,*,#1593,.T.);
#1593 = EDGE_CURVE('',#1566,#699,#1594,.T.);
#1594 = SURFACE_CURVE('',#1595,(#1599,#1606),.PCURVE_S1.);
#1595 = LINE('',#1596,#1597);
#1596 = CARTESIAN_POINT('',(-42.35343933105,199.06981363389,
    58.197919407342));
#1597 = VECTOR('',#1598,1.);
#1598 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#1599 = PCURVE('',#452,#1600);
#1600 = DEFINITIONAL_REPRESENTATION('',(#1601),#1605);
#1601 = LINE('',#1602,#1603);
#1602 = CARTESIAN_POINT('',(2.68E-15,-8.));
#1603 = VECTOR('',#1604,1.);
#1604 = DIRECTION('',(1.,0.));
#1605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1606 = PCURVE('',#738,#1607);
#1607 = DEFINITIONAL_REPRESENTATION('',(#1608),#1612);
#1608 = LINE('',#1609,#1610);
#1609 = CARTESIAN_POINT('',(0.,0.));
#1610 = VECTOR('',#1611,1.);
#1611 = DIRECTION('',(0.,1.));
#1612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1613 = ORIENTED_EDGE('',*,*,#698,.F.);
#1614 = ORIENTED_EDGE('',*,*,#438,.F.);
#1615 = ADVANCED_FACE('',(#1616,#1644,#1675,#1706,#1737,#1768,#1799,
    #1830),#398,.T.);
#1616 = FACE_BOUND('',#1617,.T.);
#1617 = EDGE_LOOP('',(#1618,#1619,#1620,#1621,#1642,#1643));
#1618 = ORIENTED_EDGE('',*,*,#865,.T.);
#1619 = ORIENTED_EDGE('',*,*,#1254,.F.);
#1620 = ORIENTED_EDGE('',*,*,#1007,.T.);
#1621 = ORIENTED_EDGE('',*,*,#1622,.F.);
#1622 = EDGE_CURVE('',#383,#1008,#1623,.T.);
#1623 = SURFACE_CURVE('',#1624,(#1628,#1635),.PCURVE_S1.);
#1624 = LINE('',#1625,#1626);
#1625 = CARTESIAN_POINT('',(-42.35343933105,130.55516903227,
    74.621788473851));
#1626 = VECTOR('',#1627,1.);
#1627 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#1628 = PCURVE('',#398,#1629);
#1629 = DEFINITIONAL_REPRESENTATION('',(#1630),#1634);
#1630 = LINE('',#1631,#1632);
#1631 = CARTESIAN_POINT('',(79.813332935693,7.2E-16));
#1632 = VECTOR('',#1633,1.);
#1633 = DIRECTION('',(-0.,-1.));
#1634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1635 = PCURVE('',#426,#1636);
#1636 = DEFINITIONAL_REPRESENTATION('',(#1637),#1641);
#1637 = LINE('',#1638,#1639);
#1638 = CARTESIAN_POINT('',(0.,0.));
#1639 = VECTOR('',#1640,1.);
#1640 = DIRECTION('',(0.,-1.));
#1641 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1642 = ORIENTED_EDGE('',*,*,#382,.F.);
#1643 = ORIENTED_EDGE('',*,*,#666,.T.);
#1644 = FACE_BOUND('',#1645,.T.);
#1645 = EDGE_LOOP('',(#1646));
#1646 = ORIENTED_EDGE('',*,*,#1647,.F.);
#1647 = EDGE_CURVE('',#1648,#1648,#1650,.T.);
#1648 = VERTEX_POINT('',#1649);
#1649 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    59.49439159546));
#1650 = SURFACE_CURVE('',#1651,(#1656,#1663),.PCURVE_S1.);
#1651 = CIRCLE('',#1652,2.025);
#1652 = AXIS2_PLACEMENT_3D('',#1653,#1654,#1655);
#1653 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    61.51939159546));
#1654 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1655 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1656 = PCURVE('',#398,#1657);
#1657 = DEFINITIONAL_REPRESENTATION('',(#1658),#1662);
#1658 = CIRCLE('',#1659,2.025);
#1659 = AXIS2_PLACEMENT_2D('',#1660,#1661);
#1660 = CARTESIAN_POINT('',(66.710936057302,-42.5));
#1661 = DIRECTION('',(-1.,-3.E-17));
#1662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1663 = PCURVE('',#1664,#1669);
#1664 = CYLINDRICAL_SURFACE('',#1665,2.025);
#1665 = AXIS2_PLACEMENT_3D('',#1666,#1667,#1668);
#1666 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    61.51939159546));
#1667 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1668 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1669 = DEFINITIONAL_REPRESENTATION('',(#1670),#1674);
#1670 = LINE('',#1671,#1672);
#1671 = CARTESIAN_POINT('',(0.,0.));
#1672 = VECTOR('',#1673,1.);
#1673 = DIRECTION('',(1.,0.));
#1674 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1675 = FACE_BOUND('',#1676,.T.);
#1676 = EDGE_LOOP('',(#1677));
#1677 = ORIENTED_EDGE('',*,*,#1678,.F.);
#1678 = EDGE_CURVE('',#1679,#1679,#1681,.T.);
#1679 = VERTEX_POINT('',#1680);
#1680 = CARTESIAN_POINT('',(22.646560668945,130.55516903227,
    54.01939159546));
#1681 = SURFACE_CURVE('',#1682,(#1687,#1694),.PCURVE_S1.);
#1682 = CIRCLE('',#1683,7.5);
#1683 = AXIS2_PLACEMENT_3D('',#1684,#1685,#1686);
#1684 = CARTESIAN_POINT('',(22.646560668945,130.55516903227,
    61.51939159546));
#1685 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1686 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1687 = PCURVE('',#398,#1688);
#1688 = DEFINITIONAL_REPRESENTATION('',(#1689),#1693);
#1689 = CIRCLE('',#1690,7.5);
#1690 = AXIS2_PLACEMENT_2D('',#1691,#1692);
#1691 = CARTESIAN_POINT('',(66.710936057302,-65.));
#1692 = DIRECTION('',(-1.,-3.E-17));
#1693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1694 = PCURVE('',#1695,#1700);
#1695 = CYLINDRICAL_SURFACE('',#1696,7.5);
#1696 = AXIS2_PLACEMENT_3D('',#1697,#1698,#1699);
#1697 = CARTESIAN_POINT('',(22.646560668945,130.55516903227,
    61.51939159546));
#1698 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1699 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1700 = DEFINITIONAL_REPRESENTATION('',(#1701),#1705);
#1701 = LINE('',#1702,#1703);
#1702 = CARTESIAN_POINT('',(0.,0.));
#1703 = VECTOR('',#1704,1.);
#1704 = DIRECTION('',(1.,0.));
#1705 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1706 = FACE_BOUND('',#1707,.T.);
#1707 = EDGE_LOOP('',(#1708));
#1708 = ORIENTED_EDGE('',*,*,#1709,.F.);
#1709 = EDGE_CURVE('',#1710,#1710,#1712,.T.);
#1710 = VERTEX_POINT('',#1711);
#1711 = CARTESIAN_POINT('',(25.261297378395,130.55516903227,
    15.994391594875));
#1712 = SURFACE_CURVE('',#1713,(#1718,#1725),.PCURVE_S1.);
#1713 = CIRCLE('',#1714,2.025);
#1714 = AXIS2_PLACEMENT_3D('',#1715,#1716,#1717);
#1715 = CARTESIAN_POINT('',(25.261297378395,130.55516903227,
    18.019391594875));
#1716 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1717 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1718 = PCURVE('',#398,#1719);
#1719 = DEFINITIONAL_REPRESENTATION('',(#1720),#1724);
#1720 = CIRCLE('',#1721,2.025);
#1721 = AXIS2_PLACEMENT_2D('',#1722,#1723);
#1722 = CARTESIAN_POINT('',(23.210936056717,-67.61473670945));
#1723 = DIRECTION('',(-1.,-3.E-17));
#1724 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1725 = PCURVE('',#1726,#1731);
#1726 = CYLINDRICAL_SURFACE('',#1727,2.025);
#1727 = AXIS2_PLACEMENT_3D('',#1728,#1729,#1730);
#1728 = CARTESIAN_POINT('',(25.261297378395,130.55516903227,
    18.019391594875));
#1729 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1730 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1731 = DEFINITIONAL_REPRESENTATION('',(#1732),#1736);
#1732 = LINE('',#1733,#1734);
#1733 = CARTESIAN_POINT('',(0.,0.));
#1734 = VECTOR('',#1735,1.);
#1735 = DIRECTION('',(1.,0.));
#1736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1737 = FACE_BOUND('',#1738,.T.);
#1738 = EDGE_LOOP('',(#1739));
#1739 = ORIENTED_EDGE('',*,*,#1740,.F.);
#1740 = EDGE_CURVE('',#1741,#1741,#1743,.T.);
#1741 = VERTEX_POINT('',#1742);
#1742 = CARTESIAN_POINT('',(16.817549691578,130.55516903227,
    18.644391595083));
#1743 = SURFACE_CURVE('',#1744,(#1749,#1756),.PCURVE_S1.);
#1744 = CIRCLE('',#1745,4.25);
#1745 = AXIS2_PLACEMENT_3D('',#1746,#1747,#1748);
#1746 = CARTESIAN_POINT('',(16.817549691578,130.55516903227,
    22.894391595083));
#1747 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1748 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1749 = PCURVE('',#398,#1750);
#1750 = DEFINITIONAL_REPRESENTATION('',(#1751),#1755);
#1751 = CIRCLE('',#1752,4.25);
#1752 = AXIS2_PLACEMENT_2D('',#1753,#1754);
#1753 = CARTESIAN_POINT('',(28.085936056925,-59.17098902263));
#1754 = DIRECTION('',(-1.,-3.E-17));
#1755 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1756 = PCURVE('',#1757,#1762);
#1757 = CYLINDRICAL_SURFACE('',#1758,4.25);
#1758 = AXIS2_PLACEMENT_3D('',#1759,#1760,#1761);
#1759 = CARTESIAN_POINT('',(16.817549691578,130.55516903227,
    22.894391595083));
#1760 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1761 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1762 = DEFINITIONAL_REPRESENTATION('',(#1763),#1767);
#1763 = LINE('',#1764,#1765);
#1764 = CARTESIAN_POINT('',(0.,0.));
#1765 = VECTOR('',#1766,1.);
#1766 = DIRECTION('',(1.,0.));
#1767 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1768 = FACE_BOUND('',#1769,.T.);
#1769 = EDGE_LOOP('',(#1770));
#1770 = ORIENTED_EDGE('',*,*,#1771,.F.);
#1771 = EDGE_CURVE('',#1772,#1772,#1774,.T.);
#1772 = VERTEX_POINT('',#1773);
#1773 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    21.01939159546));
#1774 = SURFACE_CURVE('',#1775,(#1780,#1787),.PCURVE_S1.);
#1775 = CIRCLE('',#1776,11.5);
#1776 = AXIS2_PLACEMENT_3D('',#1777,#1778,#1779);
#1777 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    32.51939159546));
#1778 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1779 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1780 = PCURVE('',#398,#1781);
#1781 = DEFINITIONAL_REPRESENTATION('',(#1782),#1786);
#1782 = CIRCLE('',#1783,11.5);
#1783 = AXIS2_PLACEMENT_2D('',#1784,#1785);
#1784 = CARTESIAN_POINT('',(37.710936057302,-42.5));
#1785 = DIRECTION('',(-1.,-3.E-17));
#1786 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1787 = PCURVE('',#1788,#1793);
#1788 = CYLINDRICAL_SURFACE('',#1789,11.5);
#1789 = AXIS2_PLACEMENT_3D('',#1790,#1791,#1792);
#1790 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    32.51939159546));
#1791 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1792 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1793 = DEFINITIONAL_REPRESENTATION('',(#1794),#1798);
#1794 = LINE('',#1795,#1796);
#1795 = CARTESIAN_POINT('',(0.,0.));
#1796 = VECTOR('',#1797,1.);
#1797 = DIRECTION('',(1.,0.));
#1798 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1799 = FACE_BOUND('',#1800,.T.);
#1800 = EDGE_LOOP('',(#1801));
#1801 = ORIENTED_EDGE('',*,*,#1802,.F.);
#1802 = EDGE_CURVE('',#1803,#1803,#1805,.T.);
#1803 = VERTEX_POINT('',#1804);
#1804 = CARTESIAN_POINT('',(-24.96817604051,130.55516903227,
    15.994391594895));
#1805 = SURFACE_CURVE('',#1806,(#1811,#1818),.PCURVE_S1.);
#1806 = CIRCLE('',#1807,2.025);
#1807 = AXIS2_PLACEMENT_3D('',#1808,#1809,#1810);
#1808 = CARTESIAN_POINT('',(-24.96817604051,130.55516903227,
    18.019391594895));
#1809 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1810 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1811 = PCURVE('',#398,#1812);
#1812 = DEFINITIONAL_REPRESENTATION('',(#1813),#1817);
#1813 = CIRCLE('',#1814,2.025);
#1814 = AXIS2_PLACEMENT_2D('',#1815,#1816);
#1815 = CARTESIAN_POINT('',(23.210936056737,-17.38526329054));
#1816 = DIRECTION('',(-1.,-3.E-17));
#1817 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1818 = PCURVE('',#1819,#1824);
#1819 = CYLINDRICAL_SURFACE('',#1820,2.025);
#1820 = AXIS2_PLACEMENT_3D('',#1821,#1822,#1823);
#1821 = CARTESIAN_POINT('',(-24.96817604051,130.55516903227,
    18.019391594895));
#1822 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1823 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1824 = DEFINITIONAL_REPRESENTATION('',(#1825),#1829);
#1825 = LINE('',#1826,#1827);
#1826 = CARTESIAN_POINT('',(0.,0.));
#1827 = VECTOR('',#1828,1.);
#1828 = DIRECTION('',(1.,0.));
#1829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1830 = FACE_BOUND('',#1831,.T.);
#1831 = EDGE_LOOP('',(#1832));
#1832 = ORIENTED_EDGE('',*,*,#1833,.F.);
#1833 = EDGE_CURVE('',#1834,#1834,#1836,.T.);
#1834 = VERTEX_POINT('',#1835);
#1835 = CARTESIAN_POINT('',(-22.35343933105,130.55516903227,
    54.01939159546));
#1836 = SURFACE_CURVE('',#1837,(#1842,#1849),.PCURVE_S1.);
#1837 = CIRCLE('',#1838,7.5);
#1838 = AXIS2_PLACEMENT_3D('',#1839,#1840,#1841);
#1839 = CARTESIAN_POINT('',(-22.35343933105,130.55516903227,
    61.51939159546));
#1840 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#1841 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#1842 = PCURVE('',#398,#1843);
#1843 = DEFINITIONAL_REPRESENTATION('',(#1844),#1848);
#1844 = CIRCLE('',#1845,7.5);
#1845 = AXIS2_PLACEMENT_2D('',#1846,#1847);
#1846 = CARTESIAN_POINT('',(66.710936057302,-20.));
#1847 = DIRECTION('',(-1.,-3.E-17));
#1848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1849 = PCURVE('',#1850,#1855);
#1850 = CYLINDRICAL_SURFACE('',#1851,7.5);
#1851 = AXIS2_PLACEMENT_3D('',#1852,#1853,#1854);
#1852 = CARTESIAN_POINT('',(-22.35343933105,130.55516903227,
    61.51939159546));
#1853 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660064E-16));
#1854 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#1855 = DEFINITIONAL_REPRESENTATION('',(#1856),#1860);
#1856 = LINE('',#1857,#1858);
#1857 = CARTESIAN_POINT('',(0.,0.));
#1858 = VECTOR('',#1859,1.);
#1859 = DIRECTION('',(1.,0.));
#1860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1861 = ADVANCED_FACE('',(#1862,#1888,#1919,#1950,#2059,#2090),#426,.T.
  );
#1862 = FACE_BOUND('',#1863,.T.);
#1863 = EDGE_LOOP('',(#1864,#1865,#1866,#1867));
#1864 = ORIENTED_EDGE('',*,*,#410,.F.);
#1865 = ORIENTED_EDGE('',*,*,#1622,.T.);
#1866 = ORIENTED_EDGE('',*,*,#1030,.T.);
#1867 = ORIENTED_EDGE('',*,*,#1868,.T.);
#1868 = EDGE_CURVE('',#1031,#411,#1869,.T.);
#1869 = SURFACE_CURVE('',#1870,(#1874,#1881),.PCURVE_S1.);
#1870 = LINE('',#1871,#1872);
#1871 = CARTESIAN_POINT('',(42.646560668945,196.33365248728,
    50.680378441055));
#1872 = VECTOR('',#1873,1.);
#1873 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#1874 = PCURVE('',#426,#1875);
#1875 = DEFINITIONAL_REPRESENTATION('',(#1876),#1880);
#1876 = LINE('',#1877,#1878);
#1877 = CARTESIAN_POINT('',(70.,-85.));
#1878 = VECTOR('',#1879,1.);
#1879 = DIRECTION('',(0.,1.));
#1880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1881 = PCURVE('',#1581,#1882);
#1882 = DEFINITIONAL_REPRESENTATION('',(#1883),#1887);
#1883 = LINE('',#1884,#1885);
#1884 = CARTESIAN_POINT('',(0.,0.));
#1885 = VECTOR('',#1886,1.);
#1886 = DIRECTION('',(1.,0.));
#1887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1888 = FACE_BOUND('',#1889,.T.);
#1889 = EDGE_LOOP('',(#1890));
#1890 = ORIENTED_EDGE('',*,*,#1891,.F.);
#1891 = EDGE_CURVE('',#1892,#1892,#1894,.T.);
#1892 = VERTEX_POINT('',#1893);
#1893 = CARTESIAN_POINT('',(40.146560668945,190.1316811901,
    52.937711387004));
#1894 = SURFACE_CURVE('',#1895,(#1900,#1907),.PCURVE_S1.);
#1895 = CIRCLE('',#1896,1.6);
#1896 = AXIS2_PLACEMENT_3D('',#1897,#1898,#1899);
#1897 = CARTESIAN_POINT('',(40.146560668945,191.63518938336,
    52.390479157683));
#1898 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1899 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1900 = PCURVE('',#426,#1901);
#1901 = DEFINITIONAL_REPRESENTATION('',(#1902),#1906);
#1902 = CIRCLE('',#1903,1.6);
#1903 = AXIS2_PLACEMENT_2D('',#1904,#1905);
#1904 = CARTESIAN_POINT('',(65.,-82.5));
#1905 = DIRECTION('',(-1.,0.));
#1906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1907 = PCURVE('',#1908,#1913);
#1908 = CYLINDRICAL_SURFACE('',#1909,1.6);
#1909 = AXIS2_PLACEMENT_3D('',#1910,#1911,#1912);
#1910 = CARTESIAN_POINT('',(40.146560668945,191.63518938336,
    52.390479157683));
#1911 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1912 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1913 = DEFINITIONAL_REPRESENTATION('',(#1914),#1918);
#1914 = LINE('',#1915,#1916);
#1915 = CARTESIAN_POINT('',(0.,0.));
#1916 = VECTOR('',#1917,1.);
#1917 = DIRECTION('',(1.,0.));
#1918 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1919 = FACE_BOUND('',#1920,.T.);
#1920 = EDGE_LOOP('',(#1921));
#1921 = ORIENTED_EDGE('',*,*,#1922,.F.);
#1922 = EDGE_CURVE('',#1923,#1923,#1925,.T.);
#1923 = VERTEX_POINT('',#1924);
#1924 = CARTESIAN_POINT('',(40.146560668945,133.75012394294,
    73.458919986544));
#1925 = SURFACE_CURVE('',#1926,(#1931,#1938),.PCURVE_S1.);
#1926 = CIRCLE('',#1927,1.6);
#1927 = AXIS2_PLACEMENT_3D('',#1928,#1929,#1930);
#1928 = CARTESIAN_POINT('',(40.146560668945,135.2536321362,
    72.911687757223));
#1929 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1930 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1931 = PCURVE('',#426,#1932);
#1932 = DEFINITIONAL_REPRESENTATION('',(#1933),#1937);
#1933 = CIRCLE('',#1934,1.6);
#1934 = AXIS2_PLACEMENT_2D('',#1935,#1936);
#1935 = CARTESIAN_POINT('',(5.,-82.5));
#1936 = DIRECTION('',(-1.,0.));
#1937 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1938 = PCURVE('',#1939,#1944);
#1939 = CYLINDRICAL_SURFACE('',#1940,1.6);
#1940 = AXIS2_PLACEMENT_3D('',#1941,#1942,#1943);
#1941 = CARTESIAN_POINT('',(40.146560668945,135.2536321362,
    72.911687757223));
#1942 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#1943 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1944 = DEFINITIONAL_REPRESENTATION('',(#1945),#1949);
#1945 = LINE('',#1946,#1947);
#1946 = CARTESIAN_POINT('',(0.,0.));
#1947 = VECTOR('',#1948,1.);
#1948 = DIRECTION('',(1.,0.));
#1949 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1950 = FACE_BOUND('',#1951,.T.);
#1951 = EDGE_LOOP('',(#1952,#1982,#2010,#2038));
#1952 = ORIENTED_EDGE('',*,*,#1953,.T.);
#1953 = EDGE_CURVE('',#1954,#1956,#1958,.T.);
#1954 = VERTEX_POINT('',#1955);
#1955 = CARTESIAN_POINT('',(35.146560668945,188.816111521,53.41653958766
    ));
#1956 = VERTEX_POINT('',#1957);
#1957 = CARTESIAN_POINT('',(35.146560668945,138.55516903227,
    71.710026599722));
#1958 = SURFACE_CURVE('',#1959,(#1963,#1970),.PCURVE_S1.);
#1959 = LINE('',#1960,#1961);
#1960 = CARTESIAN_POINT('',(35.146560668945,149.08248193725,
    67.878398055501));
#1961 = VECTOR('',#1962,1.);
#1962 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#1963 = PCURVE('',#426,#1964);
#1964 = DEFINITIONAL_REPRESENTATION('',(#1965),#1969);
#1965 = LINE('',#1966,#1967);
#1966 = CARTESIAN_POINT('',(19.716354577189,-77.5));
#1967 = VECTOR('',#1968,1.);
#1968 = DIRECTION('',(-1.,0.));
#1969 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1970 = PCURVE('',#1971,#1976);
#1971 = PLANE('',#1972);
#1972 = AXIS2_PLACEMENT_3D('',#1973,#1974,#1975);
#1973 = CARTESIAN_POINT('',(35.146560668945,158.94088677873,
    37.317378481331));
#1974 = DIRECTION('',(-1.,-7.183090749755E-15,-3.765531969661E-15));
#1975 = DIRECTION('',(5.462009588019E-15,-0.939692620786,0.342020143326)
  );
#1976 = DEFINITIONAL_REPRESENTATION('',(#1977),#1981);
#1977 = LINE('',#1978,#1979);
#1978 = CARTESIAN_POINT('',(19.716354577189,25.346191540698));
#1979 = VECTOR('',#1980,1.);
#1980 = DIRECTION('',(1.,0.));
#1981 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1982 = ORIENTED_EDGE('',*,*,#1983,.T.);
#1983 = EDGE_CURVE('',#1956,#1984,#1986,.T.);
#1984 = VERTEX_POINT('',#1985);
#1985 = CARTESIAN_POINT('',(-34.85343933105,138.55516903227,
    71.710026599721));
#1986 = SURFACE_CURVE('',#1987,(#1991,#1998),.PCURVE_S1.);
#1987 = LINE('',#1988,#1989);
#1988 = CARTESIAN_POINT('',(-3.603439331055,138.55516903227,
    71.710026599722));
#1989 = VECTOR('',#1990,1.);
#1990 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#1991 = PCURVE('',#426,#1992);
#1992 = DEFINITIONAL_REPRESENTATION('',(#1993),#1997);
#1993 = LINE('',#1994,#1995);
#1994 = CARTESIAN_POINT('',(8.513422179808,-38.75));
#1995 = VECTOR('',#1996,1.);
#1996 = DIRECTION('',(0.,1.));
#1997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1998 = PCURVE('',#1999,#2004);
#1999 = PLANE('',#2000);
#2000 = AXIS2_PLACEMENT_3D('',#2001,#2002,#2003);
#2001 = CARTESIAN_POINT('',(35.146560668945,138.55516903227,
    -2.782417473592));
#2002 = DIRECTION('',(-6.973090749755E-15,1.,-3.774758283726E-15));
#2003 = DIRECTION('',(-3.845531969661E-15,3.774758283726E-15,1.));
#2004 = DEFINITIONAL_REPRESENTATION('',(#2005),#2009);
#2005 = LINE('',#2006,#2007);
#2006 = CARTESIAN_POINT('',(74.492444073314,-38.75));
#2007 = VECTOR('',#2008,1.);
#2008 = DIRECTION('',(0.,-1.));
#2009 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2010 = ORIENTED_EDGE('',*,*,#2011,.F.);
#2011 = EDGE_CURVE('',#2012,#1984,#2014,.T.);
#2012 = VERTEX_POINT('',#2013);
#2013 = CARTESIAN_POINT('',(-34.85343933105,188.816111521,53.41653958766
    ));
#2014 = SURFACE_CURVE('',#2015,(#2019,#2026),.PCURVE_S1.);
#2015 = LINE('',#2016,#2017);
#2016 = CARTESIAN_POINT('',(-34.85343933105,149.08248193725,
    67.878398055501));
#2017 = VECTOR('',#2018,1.);
#2018 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2019 = PCURVE('',#426,#2020);
#2020 = DEFINITIONAL_REPRESENTATION('',(#2021),#2025);
#2021 = LINE('',#2022,#2023);
#2022 = CARTESIAN_POINT('',(19.716354577189,-7.5));
#2023 = VECTOR('',#2024,1.);
#2024 = DIRECTION('',(-1.,0.));
#2025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2026 = PCURVE('',#2027,#2032);
#2027 = PLANE('',#2028);
#2028 = AXIS2_PLACEMENT_3D('',#2029,#2030,#2031);
#2029 = CARTESIAN_POINT('',(-34.85343933105,158.94088677873,
    37.317378481331));
#2030 = DIRECTION('',(-1.,-7.183090749755E-15,-3.765531969661E-15));
#2031 = DIRECTION('',(5.462009588019E-15,-0.939692620786,0.342020143326)
  );
#2032 = DEFINITIONAL_REPRESENTATION('',(#2033),#2037);
#2033 = LINE('',#2034,#2035);
#2034 = CARTESIAN_POINT('',(19.716354577189,25.346191540698));
#2035 = VECTOR('',#2036,1.);
#2036 = DIRECTION('',(1.,0.));
#2037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2038 = ORIENTED_EDGE('',*,*,#2039,.T.);
#2039 = EDGE_CURVE('',#2012,#1954,#2040,.T.);
#2040 = SURFACE_CURVE('',#2041,(#2045,#2052),.PCURVE_S1.);
#2041 = LINE('',#2042,#2043);
#2042 = CARTESIAN_POINT('',(-3.603439331055,188.816111521,53.41653958766
    ));
#2043 = VECTOR('',#2044,1.);
#2044 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#2045 = PCURVE('',#426,#2046);
#2046 = DEFINITIONAL_REPRESENTATION('',(#2047),#2051);
#2047 = LINE('',#2048,#2049);
#2048 = CARTESIAN_POINT('',(62.,-38.75));
#2049 = VECTOR('',#2050,1.);
#2050 = DIRECTION('',(0.,-1.));
#2051 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2052 = PCURVE('',#1329,#2053);
#2053 = DEFINITIONAL_REPRESENTATION('',(#2054),#2058);
#2054 = LINE('',#2055,#2056);
#2055 = CARTESIAN_POINT('',(10.,-38.75));
#2056 = VECTOR('',#2057,1.);
#2057 = DIRECTION('',(0.,1.));
#2058 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2059 = FACE_BOUND('',#2060,.T.);
#2060 = EDGE_LOOP('',(#2061));
#2061 = ORIENTED_EDGE('',*,*,#2062,.F.);
#2062 = EDGE_CURVE('',#2063,#2063,#2065,.T.);
#2063 = VERTEX_POINT('',#2064);
#2064 = CARTESIAN_POINT('',(-39.85343933105,133.75012394294,
    73.458919986544));
#2065 = SURFACE_CURVE('',#2066,(#2071,#2078),.PCURVE_S1.);
#2066 = CIRCLE('',#2067,1.6);
#2067 = AXIS2_PLACEMENT_3D('',#2068,#2069,#2070);
#2068 = CARTESIAN_POINT('',(-39.85343933105,135.2536321362,
    72.911687757223));
#2069 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2070 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2071 = PCURVE('',#426,#2072);
#2072 = DEFINITIONAL_REPRESENTATION('',(#2073),#2077);
#2073 = CIRCLE('',#2074,1.6);
#2074 = AXIS2_PLACEMENT_2D('',#2075,#2076);
#2075 = CARTESIAN_POINT('',(5.,-2.5));
#2076 = DIRECTION('',(-1.,0.));
#2077 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2078 = PCURVE('',#2079,#2084);
#2079 = CYLINDRICAL_SURFACE('',#2080,1.6);
#2080 = AXIS2_PLACEMENT_3D('',#2081,#2082,#2083);
#2081 = CARTESIAN_POINT('',(-39.85343933105,135.2536321362,
    72.911687757223));
#2082 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2083 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2084 = DEFINITIONAL_REPRESENTATION('',(#2085),#2089);
#2085 = LINE('',#2086,#2087);
#2086 = CARTESIAN_POINT('',(0.,0.));
#2087 = VECTOR('',#2088,1.);
#2088 = DIRECTION('',(1.,0.));
#2089 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2090 = FACE_BOUND('',#2091,.T.);
#2091 = EDGE_LOOP('',(#2092));
#2092 = ORIENTED_EDGE('',*,*,#2093,.F.);
#2093 = EDGE_CURVE('',#2094,#2094,#2096,.T.);
#2094 = VERTEX_POINT('',#2095);
#2095 = CARTESIAN_POINT('',(-39.85343933105,190.1316811901,
    52.937711387004));
#2096 = SURFACE_CURVE('',#2097,(#2102,#2109),.PCURVE_S1.);
#2097 = CIRCLE('',#2098,1.6);
#2098 = AXIS2_PLACEMENT_3D('',#2099,#2100,#2101);
#2099 = CARTESIAN_POINT('',(-39.85343933105,191.63518938335,
    52.390479157683));
#2100 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2101 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2102 = PCURVE('',#426,#2103);
#2103 = DEFINITIONAL_REPRESENTATION('',(#2104),#2108);
#2104 = CIRCLE('',#2105,1.6);
#2105 = AXIS2_PLACEMENT_2D('',#2106,#2107);
#2106 = CARTESIAN_POINT('',(65.,-2.5));
#2107 = DIRECTION('',(-1.,0.));
#2108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2109 = PCURVE('',#2110,#2115);
#2110 = CYLINDRICAL_SURFACE('',#2111,1.6);
#2111 = AXIS2_PLACEMENT_3D('',#2112,#2113,#2114);
#2112 = CARTESIAN_POINT('',(-39.85343933105,191.63518938335,
    52.390479157683));
#2113 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2114 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2115 = DEFINITIONAL_REPRESENTATION('',(#2116),#2120);
#2116 = LINE('',#2117,#2118);
#2117 = CARTESIAN_POINT('',(0.,0.));
#2118 = VECTOR('',#2119,1.);
#2119 = DIRECTION('',(1.,0.));
#2120 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2121 = ADVANCED_FACE('',(#2122),#487,.F.);
#2122 = FACE_BOUND('',#2123,.F.);
#2123 = EDGE_LOOP('',(#2124,#2152,#2173,#2174));
#2124 = ORIENTED_EDGE('',*,*,#2125,.F.);
#2125 = EDGE_CURVE('',#2126,#2126,#2128,.T.);
#2126 = VERTEX_POINT('',#2127);
#2127 = CARTESIAN_POINT('',(-34.85343933105,174.88008816843,
    21.432767705577));
#2128 = SURFACE_CURVE('',#2129,(#2134,#2141),.PCURVE_S1.);
#2129 = CIRCLE('',#2130,0.95);
#2130 = AXIS2_PLACEMENT_3D('',#2131,#2132,#2133);
#2131 = CARTESIAN_POINT('',(-34.85343933105,174.55516903227,
    20.540059715831));
#2132 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2133 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2134 = PCURVE('',#487,#2135);
#2135 = DEFINITIONAL_REPRESENTATION('',(#2136),#2140);
#2136 = LINE('',#2137,#2138);
#2137 = CARTESIAN_POINT('',(0.,-77.5));
#2138 = VECTOR('',#2139,1.);
#2139 = DIRECTION('',(1.,0.));
#2140 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2141 = PCURVE('',#2027,#2142);
#2142 = DEFINITIONAL_REPRESENTATION('',(#2143),#2151);
#2143 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2144,#2145,#2146,#2147,
#2148,#2149,#2150),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2144 = CARTESIAN_POINT('',(-20.41080678131,-9.475123586229));
#2145 = CARTESIAN_POINT('',(-18.76535851412,-9.475123586229));
#2146 = CARTESIAN_POINT('',(-19.58808264772,-10.90012358622));
#2147 = CARTESIAN_POINT('',(-20.41080678131,-12.32512358622));
#2148 = CARTESIAN_POINT('',(-21.23353091491,-10.90012358622));
#2149 = CARTESIAN_POINT('',(-22.05625504851,-9.475123586229));
#2150 = CARTESIAN_POINT('',(-20.41080678131,-9.475123586229));
#2151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2152 = ORIENTED_EDGE('',*,*,#2153,.T.);
#2153 = EDGE_CURVE('',#2126,#467,#2154,.T.);
#2154 = SEAM_CURVE('',#2155,(#2159,#2166),.PCURVE_S1.);
#2155 = LINE('',#2156,#2157);
#2156 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    21.432767705578));
#2157 = VECTOR('',#2158,1.);
#2158 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2159 = PCURVE('',#487,#2160);
#2160 = DEFINITIONAL_REPRESENTATION('',(#2161),#2165);
#2161 = LINE('',#2162,#2163);
#2162 = CARTESIAN_POINT('',(6.28318530718,0.));
#2163 = VECTOR('',#2164,1.);
#2164 = DIRECTION('',(0.,-1.));
#2165 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2166 = PCURVE('',#487,#2167);
#2167 = DEFINITIONAL_REPRESENTATION('',(#2168),#2172);
#2168 = LINE('',#2169,#2170);
#2169 = CARTESIAN_POINT('',(0.,0.));
#2170 = VECTOR('',#2171,1.);
#2171 = DIRECTION('',(0.,-1.));
#2172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2173 = ORIENTED_EDGE('',*,*,#466,.T.);
#2174 = ORIENTED_EDGE('',*,*,#2153,.F.);
#2175 = ADVANCED_FACE('',(#2176),#522,.F.);
#2176 = FACE_BOUND('',#2177,.F.);
#2177 = EDGE_LOOP('',(#2178,#2206,#2227,#2228));
#2178 = ORIENTED_EDGE('',*,*,#2179,.F.);
#2179 = EDGE_CURVE('',#2180,#2180,#2182,.T.);
#2180 = VERTEX_POINT('',#2181);
#2181 = CARTESIAN_POINT('',(-34.85343933105,140.88008816843,
    21.432767705577));
#2182 = SURFACE_CURVE('',#2183,(#2188,#2195),.PCURVE_S1.);
#2183 = CIRCLE('',#2184,0.95);
#2184 = AXIS2_PLACEMENT_3D('',#2185,#2186,#2187);
#2185 = CARTESIAN_POINT('',(-34.85343933105,140.55516903227,
    20.540059715831));
#2186 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2187 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2188 = PCURVE('',#522,#2189);
#2189 = DEFINITIONAL_REPRESENTATION('',(#2190),#2194);
#2190 = LINE('',#2191,#2192);
#2191 = CARTESIAN_POINT('',(0.,-77.5));
#2192 = VECTOR('',#2193,1.);
#2193 = DIRECTION('',(1.,0.));
#2194 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2195 = PCURVE('',#2027,#2196);
#2196 = DEFINITIONAL_REPRESENTATION('',(#2197),#2205);
#2197 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2198,#2199,#2200,#2201,
#2202,#2203,#2204),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2198 = CARTESIAN_POINT('',(11.538742325402,-21.1038084593));
#2199 = CARTESIAN_POINT('',(13.184190592592,-21.1038084593));
#2200 = CARTESIAN_POINT('',(12.361466458997,-22.5288084593));
#2201 = CARTESIAN_POINT('',(11.538742325402,-23.9538084593));
#2202 = CARTESIAN_POINT('',(10.716018191806,-22.5288084593));
#2203 = CARTESIAN_POINT('',(9.893294058211,-21.1038084593));
#2204 = CARTESIAN_POINT('',(11.538742325402,-21.1038084593));
#2205 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2206 = ORIENTED_EDGE('',*,*,#2207,.T.);
#2207 = EDGE_CURVE('',#2180,#502,#2208,.T.);
#2208 = SEAM_CURVE('',#2209,(#2213,#2220),.PCURVE_S1.);
#2209 = LINE('',#2210,#2211);
#2210 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    21.432767705578));
#2211 = VECTOR('',#2212,1.);
#2212 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2213 = PCURVE('',#522,#2214);
#2214 = DEFINITIONAL_REPRESENTATION('',(#2215),#2219);
#2215 = LINE('',#2216,#2217);
#2216 = CARTESIAN_POINT('',(6.28318530718,0.));
#2217 = VECTOR('',#2218,1.);
#2218 = DIRECTION('',(0.,-1.));
#2219 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2220 = PCURVE('',#522,#2221);
#2221 = DEFINITIONAL_REPRESENTATION('',(#2222),#2226);
#2222 = LINE('',#2223,#2224);
#2223 = CARTESIAN_POINT('',(0.,0.));
#2224 = VECTOR('',#2225,1.);
#2225 = DIRECTION('',(0.,-1.));
#2226 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2227 = ORIENTED_EDGE('',*,*,#501,.T.);
#2228 = ORIENTED_EDGE('',*,*,#2207,.F.);
#2229 = ADVANCED_FACE('',(#2230),#557,.F.);
#2230 = FACE_BOUND('',#2231,.F.);
#2231 = EDGE_LOOP('',(#2232,#2260,#2281,#2282));
#2232 = ORIENTED_EDGE('',*,*,#2233,.F.);
#2233 = EDGE_CURVE('',#2234,#2234,#2236,.T.);
#2234 = VERTEX_POINT('',#2235);
#2235 = CARTESIAN_POINT('',(-34.85343933105,159.76165480551,
    32.066163830596));
#2236 = SURFACE_CURVE('',#2237,(#2242,#2249),.PCURVE_S1.);
#2237 = CIRCLE('',#2238,10.);
#2238 = AXIS2_PLACEMENT_3D('',#2239,#2240,#2241);
#2239 = CARTESIAN_POINT('',(-34.85343933105,156.34145337225,
    22.669237622737));
#2240 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#2241 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2242 = PCURVE('',#557,#2243);
#2243 = DEFINITIONAL_REPRESENTATION('',(#2244),#2248);
#2244 = LINE('',#2245,#2246);
#2245 = CARTESIAN_POINT('',(0.,-77.5));
#2246 = VECTOR('',#2247,1.);
#2247 = DIRECTION('',(1.,0.));
#2248 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2249 = PCURVE('',#2027,#2250);
#2250 = DEFINITIONAL_REPRESENTATION('',(#2251),#2259);
#2251 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2252,#2253,#2254,#2255,
#2256,#2257,#2258),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2252 = CARTESIAN_POINT('',(-2.567290845621,-4.653808459302));
#2253 = CARTESIAN_POINT('',(14.753217230067,-4.653808459302));
#2254 = CARTESIAN_POINT('',(6.092963192223,-19.6538084593));
#2255 = CARTESIAN_POINT('',(-2.567290845621,-34.6538084593));
#2256 = CARTESIAN_POINT('',(-11.22754488346,-19.6538084593));
#2257 = CARTESIAN_POINT('',(-19.88779892131,-4.653808459302));
#2258 = CARTESIAN_POINT('',(-2.567290845621,-4.653808459302));
#2259 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2260 = ORIENTED_EDGE('',*,*,#2261,.T.);
#2261 = EDGE_CURVE('',#2234,#537,#2262,.T.);
#2262 = SEAM_CURVE('',#2263,(#2267,#2274),.PCURVE_S1.);
#2263 = LINE('',#2264,#2265);
#2264 = CARTESIAN_POINT('',(42.646560668945,159.76165480551,
    32.066163830596));
#2265 = VECTOR('',#2266,1.);
#2266 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#2267 = PCURVE('',#557,#2268);
#2268 = DEFINITIONAL_REPRESENTATION('',(#2269),#2273);
#2269 = LINE('',#2270,#2271);
#2270 = CARTESIAN_POINT('',(6.28318530718,0.));
#2271 = VECTOR('',#2272,1.);
#2272 = DIRECTION('',(0.,-1.));
#2273 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2274 = PCURVE('',#557,#2275);
#2275 = DEFINITIONAL_REPRESENTATION('',(#2276),#2280);
#2276 = LINE('',#2277,#2278);
#2277 = CARTESIAN_POINT('',(0.,0.));
#2278 = VECTOR('',#2279,1.);
#2279 = DIRECTION('',(0.,-1.));
#2280 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2281 = ORIENTED_EDGE('',*,*,#536,.T.);
#2282 = ORIENTED_EDGE('',*,*,#2261,.F.);
#2283 = ADVANCED_FACE('',(#2284),#592,.F.);
#2284 = FACE_BOUND('',#2285,.F.);
#2285 = EDGE_LOOP('',(#2286,#2314,#2335,#2336));
#2286 = ORIENTED_EDGE('',*,*,#2287,.F.);
#2287 = EDGE_CURVE('',#2288,#2288,#2290,.T.);
#2288 = VERTEX_POINT('',#2289);
#2289 = CARTESIAN_POINT('',(-34.85343933105,174.88008816843,
    45.432767705578));
#2290 = SURFACE_CURVE('',#2291,(#2296,#2303),.PCURVE_S1.);
#2291 = CIRCLE('',#2292,0.95);
#2292 = AXIS2_PLACEMENT_3D('',#2293,#2294,#2295);
#2293 = CARTESIAN_POINT('',(-34.85343933105,174.55516903227,
    44.540059715831));
#2294 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2295 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2296 = PCURVE('',#592,#2297);
#2297 = DEFINITIONAL_REPRESENTATION('',(#2298),#2302);
#2298 = LINE('',#2299,#2300);
#2299 = CARTESIAN_POINT('',(0.,-77.5));
#2300 = VECTOR('',#2301,1.);
#2301 = DIRECTION('',(1.,0.));
#2302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2303 = PCURVE('',#2027,#2304);
#2304 = DEFINITIONAL_REPRESENTATION('',(#2305),#2313);
#2305 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2306,#2307,#2308,#2309,
#2310,#2311,#2312),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2306 = CARTESIAN_POINT('',(-12.2023233415,13.077499312633));
#2307 = CARTESIAN_POINT('',(-10.55687507431,13.077499312633));
#2308 = CARTESIAN_POINT('',(-11.3795992079,11.652499312633));
#2309 = CARTESIAN_POINT('',(-12.2023233415,10.227499312633));
#2310 = CARTESIAN_POINT('',(-13.02504747509,11.652499312633));
#2311 = CARTESIAN_POINT('',(-13.84777160869,13.077499312633));
#2312 = CARTESIAN_POINT('',(-12.2023233415,13.077499312633));
#2313 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2314 = ORIENTED_EDGE('',*,*,#2315,.T.);
#2315 = EDGE_CURVE('',#2288,#572,#2316,.T.);
#2316 = SEAM_CURVE('',#2317,(#2321,#2328),.PCURVE_S1.);
#2317 = LINE('',#2318,#2319);
#2318 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    45.432767705578));
#2319 = VECTOR('',#2320,1.);
#2320 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2321 = PCURVE('',#592,#2322);
#2322 = DEFINITIONAL_REPRESENTATION('',(#2323),#2327);
#2323 = LINE('',#2324,#2325);
#2324 = CARTESIAN_POINT('',(6.28318530718,0.));
#2325 = VECTOR('',#2326,1.);
#2326 = DIRECTION('',(0.,-1.));
#2327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2328 = PCURVE('',#592,#2329);
#2329 = DEFINITIONAL_REPRESENTATION('',(#2330),#2334);
#2330 = LINE('',#2331,#2332);
#2331 = CARTESIAN_POINT('',(0.,0.));
#2332 = VECTOR('',#2333,1.);
#2333 = DIRECTION('',(0.,-1.));
#2334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2335 = ORIENTED_EDGE('',*,*,#571,.T.);
#2336 = ORIENTED_EDGE('',*,*,#2315,.F.);
#2337 = ADVANCED_FACE('',(#2338),#627,.F.);
#2338 = FACE_BOUND('',#2339,.F.);
#2339 = EDGE_LOOP('',(#2340,#2368,#2389,#2390));
#2340 = ORIENTED_EDGE('',*,*,#2341,.F.);
#2341 = EDGE_CURVE('',#2342,#2342,#2344,.T.);
#2342 = VERTEX_POINT('',#2343);
#2343 = CARTESIAN_POINT('',(-34.85343933105,140.88008816843,
    45.432767705577));
#2344 = SURFACE_CURVE('',#2345,(#2350,#2357),.PCURVE_S1.);
#2345 = CIRCLE('',#2346,0.95);
#2346 = AXIS2_PLACEMENT_3D('',#2347,#2348,#2349);
#2347 = CARTESIAN_POINT('',(-34.85343933105,140.55516903227,
    44.540059715831));
#2348 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2349 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2350 = PCURVE('',#627,#2351);
#2351 = DEFINITIONAL_REPRESENTATION('',(#2352),#2356);
#2352 = LINE('',#2353,#2354);
#2353 = CARTESIAN_POINT('',(0.,-77.5));
#2354 = VECTOR('',#2355,1.);
#2355 = DIRECTION('',(1.,0.));
#2356 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2357 = PCURVE('',#2027,#2358);
#2358 = DEFINITIONAL_REPRESENTATION('',(#2359),#2367);
#2359 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2360,#2361,#2362,#2363,
#2364,#2365,#2366),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2360 = CARTESIAN_POINT('',(19.747225765218,1.44881443956));
#2361 = CARTESIAN_POINT('',(21.392674032408,1.44881443956));
#2362 = CARTESIAN_POINT('',(20.569949898813,2.381443955988E-02));
#2363 = CARTESIAN_POINT('',(19.747225765218,-1.40118556044));
#2364 = CARTESIAN_POINT('',(18.924501631622,2.381443955988E-02));
#2365 = CARTESIAN_POINT('',(18.101777498027,1.44881443956));
#2366 = CARTESIAN_POINT('',(19.747225765218,1.44881443956));
#2367 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2368 = ORIENTED_EDGE('',*,*,#2369,.T.);
#2369 = EDGE_CURVE('',#2342,#607,#2370,.T.);
#2370 = SEAM_CURVE('',#2371,(#2375,#2382),.PCURVE_S1.);
#2371 = LINE('',#2372,#2373);
#2372 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    45.432767705578));
#2373 = VECTOR('',#2374,1.);
#2374 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2375 = PCURVE('',#627,#2376);
#2376 = DEFINITIONAL_REPRESENTATION('',(#2377),#2381);
#2377 = LINE('',#2378,#2379);
#2378 = CARTESIAN_POINT('',(6.28318530718,0.));
#2379 = VECTOR('',#2380,1.);
#2380 = DIRECTION('',(0.,-1.));
#2381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2382 = PCURVE('',#627,#2383);
#2383 = DEFINITIONAL_REPRESENTATION('',(#2384),#2388);
#2384 = LINE('',#2385,#2386);
#2385 = CARTESIAN_POINT('',(0.,0.));
#2386 = VECTOR('',#2387,1.);
#2387 = DIRECTION('',(0.,-1.));
#2388 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2389 = ORIENTED_EDGE('',*,*,#606,.T.);
#2390 = ORIENTED_EDGE('',*,*,#2369,.F.);
#2391 = ADVANCED_FACE('',(#2392),#738,.T.);
#2392 = FACE_BOUND('',#2393,.T.);
#2393 = EDGE_LOOP('',(#2394,#2422,#2443,#2444));
#2394 = ORIENTED_EDGE('',*,*,#2395,.T.);
#2395 = EDGE_CURVE('',#1566,#2396,#2398,.T.);
#2396 = VERTEX_POINT('',#2397);
#2397 = CARTESIAN_POINT('',(-30.35343933105,203.1740553538,
    69.474230856773));
#2398 = SURFACE_CURVE('',#2399,(#2404,#2411),.PCURVE_S1.);
#2399 = CIRCLE('',#2400,12.);
#2400 = AXIS2_PLACEMENT_3D('',#2401,#2402,#2403);
#2401 = CARTESIAN_POINT('',(-30.35343933105,199.06981363389,
    58.197919407342));
#2402 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#2403 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2404 = PCURVE('',#738,#2405);
#2405 = DEFINITIONAL_REPRESENTATION('',(#2406),#2410);
#2406 = LINE('',#2407,#2408);
#2407 = CARTESIAN_POINT('',(-4.712388980385,-2.66E-15));
#2408 = VECTOR('',#2409,1.);
#2409 = DIRECTION('',(1.,0.));
#2410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2411 = PCURVE('',#1581,#2412);
#2412 = DEFINITIONAL_REPRESENTATION('',(#2413),#2421);
#2413 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2414,#2415,#2416,#2417,
#2418,#2419,#2420),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2414 = CARTESIAN_POINT('',(73.,-20.));
#2415 = CARTESIAN_POINT('',(52.215390309173,-20.));
#2416 = CARTESIAN_POINT('',(62.607695154587,-2.));
#2417 = CARTESIAN_POINT('',(73.,16.));
#2418 = CARTESIAN_POINT('',(83.392304845413,-2.));
#2419 = CARTESIAN_POINT('',(93.784609690827,-20.));
#2420 = CARTESIAN_POINT('',(73.,-20.));
#2421 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2422 = ORIENTED_EDGE('',*,*,#2423,.T.);
#2423 = EDGE_CURVE('',#2396,#722,#2424,.T.);
#2424 = SURFACE_CURVE('',#2425,(#2429,#2436),.PCURVE_S1.);
#2425 = LINE('',#2426,#2427);
#2426 = CARTESIAN_POINT('',(-30.35343933105,203.1740553538,
    69.474230856773));
#2427 = VECTOR('',#2428,1.);
#2428 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#2429 = PCURVE('',#738,#2430);
#2430 = DEFINITIONAL_REPRESENTATION('',(#2431),#2435);
#2431 = LINE('',#2432,#2433);
#2432 = CARTESIAN_POINT('',(1.570796326795,0.));
#2433 = VECTOR('',#2434,1.);
#2434 = DIRECTION('',(0.,1.));
#2435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2436 = PCURVE('',#766,#2437);
#2437 = DEFINITIONAL_REPRESENTATION('',(#2438),#2442);
#2438 = LINE('',#2439,#2440);
#2439 = CARTESIAN_POINT('',(-1.,-30.5));
#2440 = VECTOR('',#2441,1.);
#2441 = DIRECTION('',(1.,0.));
#2442 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2443 = ORIENTED_EDGE('',*,*,#721,.F.);
#2444 = ORIENTED_EDGE('',*,*,#1593,.F.);
#2445 = ADVANCED_FACE('',(#2446),#766,.F.);
#2446 = FACE_BOUND('',#2447,.F.);
#2447 = EDGE_LOOP('',(#2448,#2471,#2472,#2473));
#2448 = ORIENTED_EDGE('',*,*,#2449,.T.);
#2449 = EDGE_CURVE('',#2450,#2396,#2452,.T.);
#2450 = VERTEX_POINT('',#2451);
#2451 = CARTESIAN_POINT('',(30.646560668945,203.1740553538,
    69.474230856773));
#2452 = SURFACE_CURVE('',#2453,(#2457,#2464),.PCURVE_S1.);
#2453 = LINE('',#2454,#2455);
#2454 = CARTESIAN_POINT('',(42.646560668945,203.1740553538,
    69.474230856773));
#2455 = VECTOR('',#2456,1.);
#2456 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#2457 = PCURVE('',#766,#2458);
#2458 = DEFINITIONAL_REPRESENTATION('',(#2459),#2463);
#2459 = LINE('',#2460,#2461);
#2460 = CARTESIAN_POINT('',(-1.,42.5));
#2461 = VECTOR('',#2462,1.);
#2462 = DIRECTION('',(0.,-1.));
#2463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2464 = PCURVE('',#1581,#2465);
#2465 = DEFINITIONAL_REPRESENTATION('',(#2466),#2470);
#2466 = LINE('',#2467,#2468);
#2467 = CARTESIAN_POINT('',(0.,-20.));
#2468 = VECTOR('',#2469,1.);
#2469 = DIRECTION('',(1.,0.));
#2470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2471 = ORIENTED_EDGE('',*,*,#2423,.T.);
#2472 = ORIENTED_EDGE('',*,*,#750,.T.);
#2473 = ORIENTED_EDGE('',*,*,#2474,.T.);
#2474 = EDGE_CURVE('',#751,#2450,#2475,.T.);
#2475 = SURFACE_CURVE('',#2476,(#2480,#2487),.PCURVE_S1.);
#2476 = LINE('',#2477,#2478);
#2477 = CARTESIAN_POINT('',(30.646560668945,205.05344059537,
    68.790190570121));
#2478 = VECTOR('',#2479,1.);
#2479 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2480 = PCURVE('',#766,#2481);
#2481 = DEFINITIONAL_REPRESENTATION('',(#2482),#2486);
#2482 = LINE('',#2483,#2484);
#2483 = CARTESIAN_POINT('',(1.,30.5));
#2484 = VECTOR('',#2485,1.);
#2485 = DIRECTION('',(-1.,0.));
#2486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2487 = PCURVE('',#799,#2488);
#2488 = DEFINITIONAL_REPRESENTATION('',(#2489),#2493);
#2489 = LINE('',#2490,#2491);
#2490 = CARTESIAN_POINT('',(1.570796326795,0.));
#2491 = VECTOR('',#2492,1.);
#2492 = DIRECTION('',(0.,1.));
#2493 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2494 = ADVANCED_FACE('',(#2495),#825,.T.);
#2495 = FACE_BOUND('',#2496,.T.);
#2496 = EDGE_LOOP('',(#2497,#2498,#2521,#2542));
#2497 = ORIENTED_EDGE('',*,*,#811,.T.);
#2498 = ORIENTED_EDGE('',*,*,#2499,.T.);
#2499 = EDGE_CURVE('',#779,#2500,#2502,.T.);
#2500 = VERTEX_POINT('',#2501);
#2501 = CARTESIAN_POINT('',(42.646560668945,199.06981363389,
    58.197919407342));
#2502 = SURFACE_CURVE('',#2503,(#2507,#2514),.PCURVE_S1.);
#2503 = LINE('',#2504,#2505);
#2504 = CARTESIAN_POINT('',(42.646560668945,200.94919887546,
    57.513879120691));
#2505 = VECTOR('',#2506,1.);
#2506 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2507 = PCURVE('',#825,#2508);
#2508 = DEFINITIONAL_REPRESENTATION('',(#2509),#2513);
#2509 = LINE('',#2510,#2511);
#2510 = CARTESIAN_POINT('',(-2.68E-15,-8.));
#2511 = VECTOR('',#2512,1.);
#2512 = DIRECTION('',(1.,0.));
#2513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2514 = PCURVE('',#799,#2515);
#2515 = DEFINITIONAL_REPRESENTATION('',(#2516),#2520);
#2516 = LINE('',#2517,#2518);
#2517 = CARTESIAN_POINT('',(0.,0.));
#2518 = VECTOR('',#2519,1.);
#2519 = DIRECTION('',(0.,1.));
#2520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2521 = ORIENTED_EDGE('',*,*,#2522,.F.);
#2522 = EDGE_CURVE('',#1031,#2500,#2523,.T.);
#2523 = SURFACE_CURVE('',#2524,(#2528,#2535),.PCURVE_S1.);
#2524 = LINE('',#2525,#2526);
#2525 = CARTESIAN_POINT('',(42.646560668945,196.33365248728,
    50.680378441055));
#2526 = VECTOR('',#2527,1.);
#2527 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2528 = PCURVE('',#825,#2529);
#2529 = DEFINITIONAL_REPRESENTATION('',(#2530),#2534);
#2530 = LINE('',#2531,#2532);
#2531 = CARTESIAN_POINT('',(2.,4.4E-16));
#2532 = VECTOR('',#2533,1.);
#2533 = DIRECTION('',(0.,-1.));
#2534 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2535 = PCURVE('',#1581,#2536);
#2536 = DEFINITIONAL_REPRESENTATION('',(#2537),#2541);
#2537 = LINE('',#2538,#2539);
#2538 = CARTESIAN_POINT('',(0.,0.));
#2539 = VECTOR('',#2540,1.);
#2540 = DIRECTION('',(0.,-1.));
#2541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2542 = ORIENTED_EDGE('',*,*,#1053,.T.);
#2543 = ADVANCED_FACE('',(#2544),#799,.T.);
#2544 = FACE_BOUND('',#2545,.T.);
#2545 = EDGE_LOOP('',(#2546,#2547,#2548,#2570));
#2546 = ORIENTED_EDGE('',*,*,#778,.T.);
#2547 = ORIENTED_EDGE('',*,*,#2474,.T.);
#2548 = ORIENTED_EDGE('',*,*,#2549,.F.);
#2549 = EDGE_CURVE('',#2500,#2450,#2550,.T.);
#2550 = SURFACE_CURVE('',#2551,(#2556,#2563),.PCURVE_S1.);
#2551 = CIRCLE('',#2552,12.);
#2552 = AXIS2_PLACEMENT_3D('',#2553,#2554,#2555);
#2553 = CARTESIAN_POINT('',(30.646560668945,199.06981363389,
    58.197919407342));
#2554 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2555 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#2556 = PCURVE('',#799,#2557);
#2557 = DEFINITIONAL_REPRESENTATION('',(#2558),#2562);
#2558 = LINE('',#2559,#2560);
#2559 = CARTESIAN_POINT('',(-1.570796326795,2.));
#2560 = VECTOR('',#2561,1.);
#2561 = DIRECTION('',(1.,0.));
#2562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2563 = PCURVE('',#1581,#2564);
#2564 = DEFINITIONAL_REPRESENTATION('',(#2565),#2569);
#2565 = CIRCLE('',#2566,12.);
#2566 = AXIS2_PLACEMENT_2D('',#2567,#2568);
#2567 = CARTESIAN_POINT('',(12.,-8.));
#2568 = DIRECTION('',(-0.,1.));
#2569 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2570 = ORIENTED_EDGE('',*,*,#2499,.F.);
#2571 = ADVANCED_FACE('',(#2572),#906,.F.);
#2572 = FACE_BOUND('',#2573,.F.);
#2573 = EDGE_LOOP('',(#2574,#2575,#2598,#2629));
#2574 = ORIENTED_EDGE('',*,*,#889,.F.);
#2575 = ORIENTED_EDGE('',*,*,#2576,.T.);
#2576 = EDGE_CURVE('',#890,#2577,#2579,.T.);
#2577 = VERTEX_POINT('',#2578);
#2578 = CARTESIAN_POINT('',(22.646560668945,152.32980500071,
    -7.795974953956));
#2579 = SEAM_CURVE('',#2580,(#2584,#2591),.PCURVE_S1.);
#2580 = LINE('',#2581,#2582);
#2581 = CARTESIAN_POINT('',(22.646560668945,150.61970428408,
    -12.49443805788));
#2582 = VECTOR('',#2583,1.);
#2583 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2584 = PCURVE('',#906,#2585);
#2585 = DEFINITIONAL_REPRESENTATION('',(#2586),#2590);
#2586 = LINE('',#2587,#2588);
#2587 = CARTESIAN_POINT('',(6.28318530718,0.));
#2588 = VECTOR('',#2589,1.);
#2589 = DIRECTION('',(0.,-1.));
#2590 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2591 = PCURVE('',#906,#2592);
#2592 = DEFINITIONAL_REPRESENTATION('',(#2593),#2597);
#2593 = LINE('',#2594,#2595);
#2594 = CARTESIAN_POINT('',(0.,0.));
#2595 = VECTOR('',#2596,1.);
#2596 = DIRECTION('',(0.,-1.));
#2597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2598 = ORIENTED_EDGE('',*,*,#2599,.T.);
#2599 = EDGE_CURVE('',#2577,#2577,#2600,.T.);
#2600 = SURFACE_CURVE('',#2601,(#2606,#2613),.PCURVE_S1.);
#2601 = CIRCLE('',#2602,1.65);
#2602 = AXIS2_PLACEMENT_3D('',#2603,#2604,#2605);
#2603 = CARTESIAN_POINT('',(22.646560668945,150.77931217641,
    -7.231641717469));
#2604 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#2605 = DIRECTION('',(-5.132009588019E-15,0.939692620786,-0.342020143326
    ));
#2606 = PCURVE('',#906,#2607);
#2607 = DEFINITIONAL_REPRESENTATION('',(#2608),#2612);
#2608 = LINE('',#2609,#2610);
#2609 = CARTESIAN_POINT('',(0.,-5.));
#2610 = VECTOR('',#2611,1.);
#2611 = DIRECTION('',(1.,0.));
#2612 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2613 = PCURVE('',#2614,#2619);
#2614 = PLANE('',#2615);
#2615 = AXIS2_PLACEMENT_3D('',#2616,#2617,#2618);
#2616 = CARTESIAN_POINT('',(35.146560668945,164.8747014882,
    -12.36194386735));
#2617 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2618 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#2619 = DEFINITIONAL_REPRESENTATION('',(#2620),#2628);
#2620 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2621,#2622,#2623,#2624,
#2625,#2626,#2627),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2621 = CARTESIAN_POINT('',(13.35,-12.5));
#2622 = CARTESIAN_POINT('',(13.35,-9.642116167511));
#2623 = CARTESIAN_POINT('',(15.825,-11.07105808375));
#2624 = CARTESIAN_POINT('',(18.3,-12.5));
#2625 = CARTESIAN_POINT('',(15.825,-13.92894191624));
#2626 = CARTESIAN_POINT('',(13.35,-15.35788383248));
#2627 = CARTESIAN_POINT('',(13.35,-12.5));
#2628 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2629 = ORIENTED_EDGE('',*,*,#2576,.F.);
#2630 = ADVANCED_FACE('',(#2631),#937,.F.);
#2631 = FACE_BOUND('',#2632,.F.);
#2632 = EDGE_LOOP('',(#2633,#2634,#2657,#2683));
#2633 = ORIENTED_EDGE('',*,*,#920,.F.);
#2634 = ORIENTED_EDGE('',*,*,#2635,.T.);
#2635 = EDGE_CURVE('',#921,#2636,#2638,.T.);
#2636 = VERTEX_POINT('',#2637);
#2637 = CARTESIAN_POINT('',(0.146560668945,160.17623838427,
    -10.65184315072));
#2638 = SEAM_CURVE('',#2639,(#2643,#2650),.PCURVE_S1.);
#2639 = LINE('',#2640,#2641);
#2640 = CARTESIAN_POINT('',(0.146560668945,158.46613766764,
    -15.35030625465));
#2641 = VECTOR('',#2642,1.);
#2642 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2643 = PCURVE('',#937,#2644);
#2644 = DEFINITIONAL_REPRESENTATION('',(#2645),#2649);
#2645 = LINE('',#2646,#2647);
#2646 = CARTESIAN_POINT('',(6.28318530718,0.));
#2647 = VECTOR('',#2648,1.);
#2648 = DIRECTION('',(0.,-1.));
#2649 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2650 = PCURVE('',#937,#2651);
#2651 = DEFINITIONAL_REPRESENTATION('',(#2652),#2656);
#2652 = LINE('',#2653,#2654);
#2653 = CARTESIAN_POINT('',(0.,0.));
#2654 = VECTOR('',#2655,1.);
#2655 = DIRECTION('',(0.,-1.));
#2656 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2657 = ORIENTED_EDGE('',*,*,#2658,.T.);
#2658 = EDGE_CURVE('',#2636,#2636,#2659,.T.);
#2659 = SURFACE_CURVE('',#2660,(#2665,#2672),.PCURVE_S1.);
#2660 = CIRCLE('',#2661,10.);
#2661 = AXIS2_PLACEMENT_3D('',#2662,#2663,#2664);
#2662 = CARTESIAN_POINT('',(0.146560668945,150.77931217641,
    -7.231641717469));
#2663 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#2664 = DIRECTION('',(-5.242009588019E-15,0.939692620786,-0.342020143326
    ));
#2665 = PCURVE('',#937,#2666);
#2666 = DEFINITIONAL_REPRESENTATION('',(#2667),#2671);
#2667 = LINE('',#2668,#2669);
#2668 = CARTESIAN_POINT('',(0.,-5.));
#2669 = VECTOR('',#2670,1.);
#2670 = DIRECTION('',(1.,0.));
#2671 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2672 = PCURVE('',#2614,#2673);
#2673 = DEFINITIONAL_REPRESENTATION('',(#2674),#2682);
#2674 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2675,#2676,#2677,#2678,
#2679,#2680,#2681),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2675 = CARTESIAN_POINT('',(5.,-35.));
#2676 = CARTESIAN_POINT('',(5.,-17.67949192431));
#2677 = CARTESIAN_POINT('',(20.,-26.33974596215));
#2678 = CARTESIAN_POINT('',(35.,-35.));
#2679 = CARTESIAN_POINT('',(20.,-43.66025403784));
#2680 = CARTESIAN_POINT('',(5.,-52.32050807568));
#2681 = CARTESIAN_POINT('',(5.,-35.));
#2682 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2683 = ORIENTED_EDGE('',*,*,#2635,.F.);
#2684 = ADVANCED_FACE('',(#2685),#968,.F.);
#2685 = FACE_BOUND('',#2686,.F.);
#2686 = EDGE_LOOP('',(#2687,#2688,#2711,#2737));
#2687 = ORIENTED_EDGE('',*,*,#951,.F.);
#2688 = ORIENTED_EDGE('',*,*,#2689,.T.);
#2689 = EDGE_CURVE('',#952,#2690,#2692,.T.);
#2690 = VERTEX_POINT('',#2691);
#2691 = CARTESIAN_POINT('',(-22.35343933105,152.32980500071,
    -7.795974953956));
#2692 = SEAM_CURVE('',#2693,(#2697,#2704),.PCURVE_S1.);
#2693 = LINE('',#2694,#2695);
#2694 = CARTESIAN_POINT('',(-22.35343933105,150.61970428408,
    -12.49443805788));
#2695 = VECTOR('',#2696,1.);
#2696 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2697 = PCURVE('',#968,#2698);
#2698 = DEFINITIONAL_REPRESENTATION('',(#2699),#2703);
#2699 = LINE('',#2700,#2701);
#2700 = CARTESIAN_POINT('',(6.28318530718,0.));
#2701 = VECTOR('',#2702,1.);
#2702 = DIRECTION('',(0.,-1.));
#2703 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2704 = PCURVE('',#968,#2705);
#2705 = DEFINITIONAL_REPRESENTATION('',(#2706),#2710);
#2706 = LINE('',#2707,#2708);
#2707 = CARTESIAN_POINT('',(0.,0.));
#2708 = VECTOR('',#2709,1.);
#2709 = DIRECTION('',(0.,-1.));
#2710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2711 = ORIENTED_EDGE('',*,*,#2712,.T.);
#2712 = EDGE_CURVE('',#2690,#2690,#2713,.T.);
#2713 = SURFACE_CURVE('',#2714,(#2719,#2726),.PCURVE_S1.);
#2714 = CIRCLE('',#2715,1.65);
#2715 = AXIS2_PLACEMENT_3D('',#2716,#2717,#2718);
#2716 = CARTESIAN_POINT('',(-22.35343933105,150.77931217641,
    -7.231641717469));
#2717 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#2718 = DIRECTION('',(-5.132009588019E-15,0.939692620786,-0.342020143326
    ));
#2719 = PCURVE('',#968,#2720);
#2720 = DEFINITIONAL_REPRESENTATION('',(#2721),#2725);
#2721 = LINE('',#2722,#2723);
#2722 = CARTESIAN_POINT('',(0.,-5.));
#2723 = VECTOR('',#2724,1.);
#2724 = DIRECTION('',(1.,0.));
#2725 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2726 = PCURVE('',#2614,#2727);
#2727 = DEFINITIONAL_REPRESENTATION('',(#2728),#2736);
#2728 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2729,#2730,#2731,#2732,
#2733,#2734,#2735),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2729 = CARTESIAN_POINT('',(13.35,-57.5));
#2730 = CARTESIAN_POINT('',(13.35,-54.64211616751));
#2731 = CARTESIAN_POINT('',(15.825,-56.07105808375));
#2732 = CARTESIAN_POINT('',(18.3,-57.5));
#2733 = CARTESIAN_POINT('',(15.825,-58.92894191624));
#2734 = CARTESIAN_POINT('',(13.35,-60.35788383248));
#2735 = CARTESIAN_POINT('',(13.35,-57.5));
#2736 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2737 = ORIENTED_EDGE('',*,*,#2689,.F.);
#2738 = ADVANCED_FACE('',(#2739),#1097,.F.);
#2739 = FACE_BOUND('',#2740,.F.);
#2740 = EDGE_LOOP('',(#2741,#2742,#2765,#2791));
#2741 = ORIENTED_EDGE('',*,*,#1076,.F.);
#2742 = ORIENTED_EDGE('',*,*,#2743,.T.);
#2743 = EDGE_CURVE('',#1077,#2744,#2746,.T.);
#2744 = VERTEX_POINT('',#2745);
#2745 = CARTESIAN_POINT('',(35.146560668945,174.88008816843,
    21.432767705578));
#2746 = SEAM_CURVE('',#2747,(#2751,#2758),.PCURVE_S1.);
#2747 = LINE('',#2748,#2749);
#2748 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    21.432767705578));
#2749 = VECTOR('',#2750,1.);
#2750 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2751 = PCURVE('',#1097,#2752);
#2752 = DEFINITIONAL_REPRESENTATION('',(#2753),#2757);
#2753 = LINE('',#2754,#2755);
#2754 = CARTESIAN_POINT('',(6.28318530718,0.));
#2755 = VECTOR('',#2756,1.);
#2756 = DIRECTION('',(0.,-1.));
#2757 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2758 = PCURVE('',#1097,#2759);
#2759 = DEFINITIONAL_REPRESENTATION('',(#2760),#2764);
#2760 = LINE('',#2761,#2762);
#2761 = CARTESIAN_POINT('',(0.,0.));
#2762 = VECTOR('',#2763,1.);
#2763 = DIRECTION('',(0.,-1.));
#2764 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2765 = ORIENTED_EDGE('',*,*,#2766,.T.);
#2766 = EDGE_CURVE('',#2744,#2744,#2767,.T.);
#2767 = SURFACE_CURVE('',#2768,(#2773,#2780),.PCURVE_S1.);
#2768 = CIRCLE('',#2769,0.95);
#2769 = AXIS2_PLACEMENT_3D('',#2770,#2771,#2772);
#2770 = CARTESIAN_POINT('',(35.146560668945,174.55516903227,
    20.540059715831));
#2771 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2772 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2773 = PCURVE('',#1097,#2774);
#2774 = DEFINITIONAL_REPRESENTATION('',(#2775),#2779);
#2775 = LINE('',#2776,#2777);
#2776 = CARTESIAN_POINT('',(0.,-7.5));
#2777 = VECTOR('',#2778,1.);
#2778 = DIRECTION('',(1.,0.));
#2779 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2780 = PCURVE('',#1971,#2781);
#2781 = DEFINITIONAL_REPRESENTATION('',(#2782),#2790);
#2782 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2783,#2784,#2785,#2786,
#2787,#2788,#2789),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2783 = CARTESIAN_POINT('',(-20.41080678131,-9.475123586229));
#2784 = CARTESIAN_POINT('',(-18.76535851412,-9.475123586229));
#2785 = CARTESIAN_POINT('',(-19.58808264772,-10.90012358622));
#2786 = CARTESIAN_POINT('',(-20.41080678131,-12.32512358622));
#2787 = CARTESIAN_POINT('',(-21.23353091491,-10.90012358622));
#2788 = CARTESIAN_POINT('',(-22.05625504851,-9.475123586229));
#2789 = CARTESIAN_POINT('',(-20.41080678131,-9.475123586229));
#2790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2791 = ORIENTED_EDGE('',*,*,#2743,.F.);
#2792 = ADVANCED_FACE('',(#2793),#1132,.F.);
#2793 = FACE_BOUND('',#2794,.F.);
#2794 = EDGE_LOOP('',(#2795,#2796,#2819,#2845));
#2795 = ORIENTED_EDGE('',*,*,#1111,.F.);
#2796 = ORIENTED_EDGE('',*,*,#2797,.T.);
#2797 = EDGE_CURVE('',#1112,#2798,#2800,.T.);
#2798 = VERTEX_POINT('',#2799);
#2799 = CARTESIAN_POINT('',(35.146560668945,140.88008816843,
    21.432767705578));
#2800 = SEAM_CURVE('',#2801,(#2805,#2812),.PCURVE_S1.);
#2801 = LINE('',#2802,#2803);
#2802 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    21.432767705578));
#2803 = VECTOR('',#2804,1.);
#2804 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2805 = PCURVE('',#1132,#2806);
#2806 = DEFINITIONAL_REPRESENTATION('',(#2807),#2811);
#2807 = LINE('',#2808,#2809);
#2808 = CARTESIAN_POINT('',(6.28318530718,0.));
#2809 = VECTOR('',#2810,1.);
#2810 = DIRECTION('',(0.,-1.));
#2811 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2812 = PCURVE('',#1132,#2813);
#2813 = DEFINITIONAL_REPRESENTATION('',(#2814),#2818);
#2814 = LINE('',#2815,#2816);
#2815 = CARTESIAN_POINT('',(0.,0.));
#2816 = VECTOR('',#2817,1.);
#2817 = DIRECTION('',(0.,-1.));
#2818 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2819 = ORIENTED_EDGE('',*,*,#2820,.T.);
#2820 = EDGE_CURVE('',#2798,#2798,#2821,.T.);
#2821 = SURFACE_CURVE('',#2822,(#2827,#2834),.PCURVE_S1.);
#2822 = CIRCLE('',#2823,0.95);
#2823 = AXIS2_PLACEMENT_3D('',#2824,#2825,#2826);
#2824 = CARTESIAN_POINT('',(35.146560668945,140.55516903227,
    20.540059715831));
#2825 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2826 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2827 = PCURVE('',#1132,#2828);
#2828 = DEFINITIONAL_REPRESENTATION('',(#2829),#2833);
#2829 = LINE('',#2830,#2831);
#2830 = CARTESIAN_POINT('',(0.,-7.5));
#2831 = VECTOR('',#2832,1.);
#2832 = DIRECTION('',(1.,0.));
#2833 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2834 = PCURVE('',#1971,#2835);
#2835 = DEFINITIONAL_REPRESENTATION('',(#2836),#2844);
#2836 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2837,#2838,#2839,#2840,
#2841,#2842,#2843),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2837 = CARTESIAN_POINT('',(11.538742325402,-21.1038084593));
#2838 = CARTESIAN_POINT('',(13.184190592592,-21.1038084593));
#2839 = CARTESIAN_POINT('',(12.361466458997,-22.5288084593));
#2840 = CARTESIAN_POINT('',(11.538742325402,-23.9538084593));
#2841 = CARTESIAN_POINT('',(10.716018191806,-22.5288084593));
#2842 = CARTESIAN_POINT('',(9.893294058211,-21.1038084593));
#2843 = CARTESIAN_POINT('',(11.538742325402,-21.1038084593));
#2844 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2845 = ORIENTED_EDGE('',*,*,#2797,.F.);
#2846 = ADVANCED_FACE('',(#2847),#1167,.F.);
#2847 = FACE_BOUND('',#2848,.F.);
#2848 = EDGE_LOOP('',(#2849,#2850,#2873,#2899));
#2849 = ORIENTED_EDGE('',*,*,#1146,.F.);
#2850 = ORIENTED_EDGE('',*,*,#2851,.T.);
#2851 = EDGE_CURVE('',#1147,#2852,#2854,.T.);
#2852 = VERTEX_POINT('',#2853);
#2853 = CARTESIAN_POINT('',(35.146560668945,159.76165480551,
    32.066163830596));
#2854 = SEAM_CURVE('',#2855,(#2859,#2866),.PCURVE_S1.);
#2855 = LINE('',#2856,#2857);
#2856 = CARTESIAN_POINT('',(42.646560668945,159.76165480551,
    32.066163830596));
#2857 = VECTOR('',#2858,1.);
#2858 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#2859 = PCURVE('',#1167,#2860);
#2860 = DEFINITIONAL_REPRESENTATION('',(#2861),#2865);
#2861 = LINE('',#2862,#2863);
#2862 = CARTESIAN_POINT('',(6.28318530718,0.));
#2863 = VECTOR('',#2864,1.);
#2864 = DIRECTION('',(0.,-1.));
#2865 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2866 = PCURVE('',#1167,#2867);
#2867 = DEFINITIONAL_REPRESENTATION('',(#2868),#2872);
#2868 = LINE('',#2869,#2870);
#2869 = CARTESIAN_POINT('',(0.,0.));
#2870 = VECTOR('',#2871,1.);
#2871 = DIRECTION('',(0.,-1.));
#2872 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2873 = ORIENTED_EDGE('',*,*,#2874,.T.);
#2874 = EDGE_CURVE('',#2852,#2852,#2875,.T.);
#2875 = SURFACE_CURVE('',#2876,(#2881,#2888),.PCURVE_S1.);
#2876 = CIRCLE('',#2877,10.);
#2877 = AXIS2_PLACEMENT_3D('',#2878,#2879,#2880);
#2878 = CARTESIAN_POINT('',(35.146560668945,156.34145337225,
    22.669237622737));
#2879 = DIRECTION('',(1.,6.976358373182E-15,3.840776401192E-15));
#2880 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2881 = PCURVE('',#1167,#2882);
#2882 = DEFINITIONAL_REPRESENTATION('',(#2883),#2887);
#2883 = LINE('',#2884,#2885);
#2884 = CARTESIAN_POINT('',(0.,-7.5));
#2885 = VECTOR('',#2886,1.);
#2886 = DIRECTION('',(1.,0.));
#2887 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2888 = PCURVE('',#1971,#2889);
#2889 = DEFINITIONAL_REPRESENTATION('',(#2890),#2898);
#2890 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2891,#2892,#2893,#2894,
#2895,#2896,#2897),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2891 = CARTESIAN_POINT('',(-2.567290845621,-4.653808459302));
#2892 = CARTESIAN_POINT('',(14.753217230067,-4.653808459302));
#2893 = CARTESIAN_POINT('',(6.092963192223,-19.6538084593));
#2894 = CARTESIAN_POINT('',(-2.567290845621,-34.6538084593));
#2895 = CARTESIAN_POINT('',(-11.22754488346,-19.6538084593));
#2896 = CARTESIAN_POINT('',(-19.88779892131,-4.653808459302));
#2897 = CARTESIAN_POINT('',(-2.567290845621,-4.653808459302));
#2898 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2899 = ORIENTED_EDGE('',*,*,#2851,.F.);
#2900 = ADVANCED_FACE('',(#2901),#1202,.F.);
#2901 = FACE_BOUND('',#2902,.F.);
#2902 = EDGE_LOOP('',(#2903,#2904,#2927,#2953));
#2903 = ORIENTED_EDGE('',*,*,#1181,.F.);
#2904 = ORIENTED_EDGE('',*,*,#2905,.T.);
#2905 = EDGE_CURVE('',#1182,#2906,#2908,.T.);
#2906 = VERTEX_POINT('',#2907);
#2907 = CARTESIAN_POINT('',(35.146560668945,174.88008816843,
    45.432767705578));
#2908 = SEAM_CURVE('',#2909,(#2913,#2920),.PCURVE_S1.);
#2909 = LINE('',#2910,#2911);
#2910 = CARTESIAN_POINT('',(42.646560668945,174.88008816843,
    45.432767705578));
#2911 = VECTOR('',#2912,1.);
#2912 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2913 = PCURVE('',#1202,#2914);
#2914 = DEFINITIONAL_REPRESENTATION('',(#2915),#2919);
#2915 = LINE('',#2916,#2917);
#2916 = CARTESIAN_POINT('',(6.28318530718,0.));
#2917 = VECTOR('',#2918,1.);
#2918 = DIRECTION('',(0.,-1.));
#2919 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2920 = PCURVE('',#1202,#2921);
#2921 = DEFINITIONAL_REPRESENTATION('',(#2922),#2926);
#2922 = LINE('',#2923,#2924);
#2923 = CARTESIAN_POINT('',(0.,0.));
#2924 = VECTOR('',#2925,1.);
#2925 = DIRECTION('',(0.,-1.));
#2926 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2927 = ORIENTED_EDGE('',*,*,#2928,.T.);
#2928 = EDGE_CURVE('',#2906,#2906,#2929,.T.);
#2929 = SURFACE_CURVE('',#2930,(#2935,#2942),.PCURVE_S1.);
#2930 = CIRCLE('',#2931,0.95);
#2931 = AXIS2_PLACEMENT_3D('',#2932,#2933,#2934);
#2932 = CARTESIAN_POINT('',(35.146560668945,174.55516903227,
    44.540059715831));
#2933 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2934 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2935 = PCURVE('',#1202,#2936);
#2936 = DEFINITIONAL_REPRESENTATION('',(#2937),#2941);
#2937 = LINE('',#2938,#2939);
#2938 = CARTESIAN_POINT('',(0.,-7.5));
#2939 = VECTOR('',#2940,1.);
#2940 = DIRECTION('',(1.,0.));
#2941 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2942 = PCURVE('',#1971,#2943);
#2943 = DEFINITIONAL_REPRESENTATION('',(#2944),#2952);
#2944 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2945,#2946,#2947,#2948,
#2949,#2950,#2951),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2945 = CARTESIAN_POINT('',(-12.2023233415,13.077499312633));
#2946 = CARTESIAN_POINT('',(-10.55687507431,13.077499312633));
#2947 = CARTESIAN_POINT('',(-11.3795992079,11.652499312633));
#2948 = CARTESIAN_POINT('',(-12.2023233415,10.227499312633));
#2949 = CARTESIAN_POINT('',(-13.02504747509,11.652499312633));
#2950 = CARTESIAN_POINT('',(-13.84777160869,13.077499312633));
#2951 = CARTESIAN_POINT('',(-12.2023233415,13.077499312633));
#2952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2953 = ORIENTED_EDGE('',*,*,#2905,.F.);
#2954 = ADVANCED_FACE('',(#2955),#1237,.F.);
#2955 = FACE_BOUND('',#2956,.F.);
#2956 = EDGE_LOOP('',(#2957,#2958,#2981,#3007));
#2957 = ORIENTED_EDGE('',*,*,#1216,.F.);
#2958 = ORIENTED_EDGE('',*,*,#2959,.T.);
#2959 = EDGE_CURVE('',#1217,#2960,#2962,.T.);
#2960 = VERTEX_POINT('',#2961);
#2961 = CARTESIAN_POINT('',(35.146560668945,140.88008816843,
    45.432767705578));
#2962 = SEAM_CURVE('',#2963,(#2967,#2974),.PCURVE_S1.);
#2963 = LINE('',#2964,#2965);
#2964 = CARTESIAN_POINT('',(42.646560668945,140.88008816843,
    45.432767705578));
#2965 = VECTOR('',#2966,1.);
#2966 = DIRECTION('',(-1.,-6.872992184895E-15,-3.878398616958E-15));
#2967 = PCURVE('',#1237,#2968);
#2968 = DEFINITIONAL_REPRESENTATION('',(#2969),#2973);
#2969 = LINE('',#2970,#2971);
#2970 = CARTESIAN_POINT('',(6.28318530718,0.));
#2971 = VECTOR('',#2972,1.);
#2972 = DIRECTION('',(0.,-1.));
#2973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2974 = PCURVE('',#1237,#2975);
#2975 = DEFINITIONAL_REPRESENTATION('',(#2976),#2980);
#2976 = LINE('',#2977,#2978);
#2977 = CARTESIAN_POINT('',(0.,0.));
#2978 = VECTOR('',#2979,1.);
#2979 = DIRECTION('',(0.,-1.));
#2980 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2981 = ORIENTED_EDGE('',*,*,#2982,.T.);
#2982 = EDGE_CURVE('',#2960,#2960,#2983,.T.);
#2983 = SURFACE_CURVE('',#2984,(#2989,#2996),.PCURVE_S1.);
#2984 = CIRCLE('',#2985,0.95);
#2985 = AXIS2_PLACEMENT_3D('',#2986,#2987,#2988);
#2986 = CARTESIAN_POINT('',(35.146560668945,140.55516903227,
    44.540059715831));
#2987 = DIRECTION('',(1.,6.872992184895E-15,3.878398616958E-15));
#2988 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#2989 = PCURVE('',#1237,#2990);
#2990 = DEFINITIONAL_REPRESENTATION('',(#2991),#2995);
#2991 = LINE('',#2992,#2993);
#2992 = CARTESIAN_POINT('',(0.,-7.5));
#2993 = VECTOR('',#2994,1.);
#2994 = DIRECTION('',(1.,0.));
#2995 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2996 = PCURVE('',#1971,#2997);
#2997 = DEFINITIONAL_REPRESENTATION('',(#2998),#3006);
#2998 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2999,#3000,#3001,#3002,
#3003,#3004,#3005),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2999 = CARTESIAN_POINT('',(19.747225765218,1.44881443956));
#3000 = CARTESIAN_POINT('',(21.392674032408,1.44881443956));
#3001 = CARTESIAN_POINT('',(20.569949898813,2.381443955988E-02));
#3002 = CARTESIAN_POINT('',(19.747225765218,-1.40118556044));
#3003 = CARTESIAN_POINT('',(18.924501631622,2.381443955988E-02));
#3004 = CARTESIAN_POINT('',(18.101777498027,1.44881443956));
#3005 = CARTESIAN_POINT('',(19.747225765218,1.44881443956));
#3006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3007 = ORIENTED_EDGE('',*,*,#2959,.F.);
#3008 = ADVANCED_FACE('',(#3009,#3079,#3082,#3085,#3088,#3091),#1329,.T.
  );
#3009 = FACE_BOUND('',#3010,.T.);
#3010 = EDGE_LOOP('',(#3011,#3034,#3035,#3058));
#3011 = ORIENTED_EDGE('',*,*,#3012,.F.);
#3012 = EDGE_CURVE('',#1954,#3013,#3015,.T.);
#3013 = VERTEX_POINT('',#3014);
#3014 = CARTESIAN_POINT('',(35.146560668945,164.8747014882,
    -12.36194386735));
#3015 = SURFACE_CURVE('',#3016,(#3020,#3027),.PCURVE_S1.);
#3016 = LINE('',#3017,#3018);
#3017 = CARTESIAN_POINT('',(35.146560668945,192.23631295425,
    62.813465795519));
#3018 = VECTOR('',#3019,1.);
#3019 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3020 = PCURVE('',#1329,#3021);
#3021 = DEFINITIONAL_REPRESENTATION('',(#3022),#3026);
#3022 = LINE('',#3023,#3024);
#3023 = CARTESIAN_POINT('',(0.,0.));
#3024 = VECTOR('',#3025,1.);
#3025 = DIRECTION('',(1.,0.));
#3026 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3027 = PCURVE('',#1971,#3028);
#3028 = DEFINITIONAL_REPRESENTATION('',(#3029),#3033);
#3029 = LINE('',#3030,#3031);
#3030 = CARTESIAN_POINT('',(-22.56729084562,35.346191540698));
#3031 = VECTOR('',#3032,1.);
#3032 = DIRECTION('',(0.,-1.));
#3033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3034 = ORIENTED_EDGE('',*,*,#2039,.F.);
#3035 = ORIENTED_EDGE('',*,*,#3036,.T.);
#3036 = EDGE_CURVE('',#2012,#3037,#3039,.T.);
#3037 = VERTEX_POINT('',#3038);
#3038 = CARTESIAN_POINT('',(-34.85343933105,164.8747014882,
    -12.36194386735));
#3039 = SURFACE_CURVE('',#3040,(#3044,#3051),.PCURVE_S1.);
#3040 = LINE('',#3041,#3042);
#3041 = CARTESIAN_POINT('',(-34.85343933105,192.23631295425,
    62.813465795519));
#3042 = VECTOR('',#3043,1.);
#3043 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3044 = PCURVE('',#1329,#3045);
#3045 = DEFINITIONAL_REPRESENTATION('',(#3046),#3050);
#3046 = LINE('',#3047,#3048);
#3047 = CARTESIAN_POINT('',(0.,-70.));
#3048 = VECTOR('',#3049,1.);
#3049 = DIRECTION('',(1.,0.));
#3050 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3051 = PCURVE('',#2027,#3052);
#3052 = DEFINITIONAL_REPRESENTATION('',(#3053),#3057);
#3053 = LINE('',#3054,#3055);
#3054 = CARTESIAN_POINT('',(-22.56729084562,35.346191540698));
#3055 = VECTOR('',#3056,1.);
#3056 = DIRECTION('',(0.,-1.));
#3057 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3058 = ORIENTED_EDGE('',*,*,#3059,.F.);
#3059 = EDGE_CURVE('',#3013,#3037,#3060,.T.);
#3060 = SURFACE_CURVE('',#3061,(#3065,#3072),.PCURVE_S1.);
#3061 = LINE('',#3062,#3063);
#3062 = CARTESIAN_POINT('',(35.146560668945,164.8747014882,
    -12.36194386735));
#3063 = VECTOR('',#3064,1.);
#3064 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#3065 = PCURVE('',#1329,#3066);
#3066 = DEFINITIONAL_REPRESENTATION('',(#3067),#3071);
#3067 = LINE('',#3068,#3069);
#3068 = CARTESIAN_POINT('',(80.,0.));
#3069 = VECTOR('',#3070,1.);
#3070 = DIRECTION('',(0.,-1.));
#3071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3072 = PCURVE('',#2614,#3073);
#3073 = DEFINITIONAL_REPRESENTATION('',(#3074),#3078);
#3074 = LINE('',#3075,#3076);
#3075 = CARTESIAN_POINT('',(0.,0.));
#3076 = VECTOR('',#3077,1.);
#3077 = DIRECTION('',(0.,-1.));
#3078 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3079 = FACE_BOUND('',#3080,.T.);
#3080 = EDGE_LOOP('',(#3081));
#3081 = ORIENTED_EDGE('',*,*,#1314,.T.);
#3082 = FACE_BOUND('',#3083,.T.);
#3083 = EDGE_LOOP('',(#3084));
#3084 = ORIENTED_EDGE('',*,*,#1373,.T.);
#3085 = FACE_BOUND('',#3086,.T.);
#3086 = EDGE_LOOP('',(#3087));
#3087 = ORIENTED_EDGE('',*,*,#1427,.T.);
#3088 = FACE_BOUND('',#3089,.T.);
#3089 = EDGE_LOOP('',(#3090));
#3090 = ORIENTED_EDGE('',*,*,#1481,.T.);
#3091 = FACE_BOUND('',#3092,.T.);
#3092 = EDGE_LOOP('',(#3093));
#3093 = ORIENTED_EDGE('',*,*,#1535,.T.);
#3094 = ADVANCED_FACE('',(#3095),#1581,.T.);
#3095 = FACE_BOUND('',#3096,.T.);
#3096 = EDGE_LOOP('',(#3097,#3098,#3099,#3100,#3101,#3102));
#3097 = ORIENTED_EDGE('',*,*,#2522,.T.);
#3098 = ORIENTED_EDGE('',*,*,#2549,.T.);
#3099 = ORIENTED_EDGE('',*,*,#2449,.T.);
#3100 = ORIENTED_EDGE('',*,*,#2395,.F.);
#3101 = ORIENTED_EDGE('',*,*,#1565,.F.);
#3102 = ORIENTED_EDGE('',*,*,#1868,.F.);
#3103 = ADVANCED_FACE('',(#3104),#1664,.F.);
#3104 = FACE_BOUND('',#3105,.F.);
#3105 = EDGE_LOOP('',(#3106,#3107,#3130,#3156));
#3106 = ORIENTED_EDGE('',*,*,#1647,.F.);
#3107 = ORIENTED_EDGE('',*,*,#3108,.T.);
#3108 = EDGE_CURVE('',#1648,#3109,#3111,.T.);
#3109 = VERTEX_POINT('',#3110);
#3110 = CARTESIAN_POINT('',(0.146560668945,138.55516903227,
    59.49439159546));
#3111 = SEAM_CURVE('',#3112,(#3116,#3123),.PCURVE_S1.);
#3112 = LINE('',#3113,#3114);
#3113 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    59.49439159546));
#3114 = VECTOR('',#3115,1.);
#3115 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3116 = PCURVE('',#1664,#3117);
#3117 = DEFINITIONAL_REPRESENTATION('',(#3118),#3122);
#3118 = LINE('',#3119,#3120);
#3119 = CARTESIAN_POINT('',(6.28318530718,0.));
#3120 = VECTOR('',#3121,1.);
#3121 = DIRECTION('',(0.,-1.));
#3122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3123 = PCURVE('',#1664,#3124);
#3124 = DEFINITIONAL_REPRESENTATION('',(#3125),#3129);
#3125 = LINE('',#3126,#3127);
#3126 = CARTESIAN_POINT('',(0.,0.));
#3127 = VECTOR('',#3128,1.);
#3128 = DIRECTION('',(0.,-1.));
#3129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3130 = ORIENTED_EDGE('',*,*,#3131,.T.);
#3131 = EDGE_CURVE('',#3109,#3109,#3132,.T.);
#3132 = SURFACE_CURVE('',#3133,(#3138,#3145),.PCURVE_S1.);
#3133 = CIRCLE('',#3134,2.025);
#3134 = AXIS2_PLACEMENT_3D('',#3135,#3136,#3137);
#3135 = CARTESIAN_POINT('',(0.146560668945,138.55516903227,
    61.51939159546));
#3136 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3137 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3138 = PCURVE('',#1664,#3139);
#3139 = DEFINITIONAL_REPRESENTATION('',(#3140),#3144);
#3140 = LINE('',#3141,#3142);
#3141 = CARTESIAN_POINT('',(0.,-8.));
#3142 = VECTOR('',#3143,1.);
#3143 = DIRECTION('',(1.,0.));
#3144 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3145 = PCURVE('',#1999,#3146);
#3146 = DEFINITIONAL_REPRESENTATION('',(#3147),#3155);
#3147 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3148,#3149,#3150,#3151,
#3152,#3153,#3154),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3148 = CARTESIAN_POINT('',(62.276809069052,-35.));
#3149 = CARTESIAN_POINT('',(62.276809069052,-31.49259711467));
#3150 = CARTESIAN_POINT('',(65.314309069052,-33.24629855733));
#3151 = CARTESIAN_POINT('',(68.351809069052,-35.));
#3152 = CARTESIAN_POINT('',(65.314309069052,-36.75370144266));
#3153 = CARTESIAN_POINT('',(62.276809069052,-38.50740288532));
#3154 = CARTESIAN_POINT('',(62.276809069052,-35.));
#3155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3156 = ORIENTED_EDGE('',*,*,#3108,.F.);
#3157 = ADVANCED_FACE('',(#3158),#1695,.F.);
#3158 = FACE_BOUND('',#3159,.F.);
#3159 = EDGE_LOOP('',(#3160,#3161,#3184,#3210));
#3160 = ORIENTED_EDGE('',*,*,#1678,.F.);
#3161 = ORIENTED_EDGE('',*,*,#3162,.T.);
#3162 = EDGE_CURVE('',#1679,#3163,#3165,.T.);
#3163 = VERTEX_POINT('',#3164);
#3164 = CARTESIAN_POINT('',(22.646560668945,138.55516903227,
    54.01939159546));
#3165 = SEAM_CURVE('',#3166,(#3170,#3177),.PCURVE_S1.);
#3166 = LINE('',#3167,#3168);
#3167 = CARTESIAN_POINT('',(22.646560668945,130.55516903227,
    54.01939159546));
#3168 = VECTOR('',#3169,1.);
#3169 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3170 = PCURVE('',#1695,#3171);
#3171 = DEFINITIONAL_REPRESENTATION('',(#3172),#3176);
#3172 = LINE('',#3173,#3174);
#3173 = CARTESIAN_POINT('',(6.28318530718,0.));
#3174 = VECTOR('',#3175,1.);
#3175 = DIRECTION('',(0.,-1.));
#3176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3177 = PCURVE('',#1695,#3178);
#3178 = DEFINITIONAL_REPRESENTATION('',(#3179),#3183);
#3179 = LINE('',#3180,#3181);
#3180 = CARTESIAN_POINT('',(0.,0.));
#3181 = VECTOR('',#3182,1.);
#3182 = DIRECTION('',(0.,-1.));
#3183 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3184 = ORIENTED_EDGE('',*,*,#3185,.T.);
#3185 = EDGE_CURVE('',#3163,#3163,#3186,.T.);
#3186 = SURFACE_CURVE('',#3187,(#3192,#3199),.PCURVE_S1.);
#3187 = CIRCLE('',#3188,7.5);
#3188 = AXIS2_PLACEMENT_3D('',#3189,#3190,#3191);
#3189 = CARTESIAN_POINT('',(22.646560668945,138.55516903227,
    61.51939159546));
#3190 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3191 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3192 = PCURVE('',#1695,#3193);
#3193 = DEFINITIONAL_REPRESENTATION('',(#3194),#3198);
#3194 = LINE('',#3195,#3196);
#3195 = CARTESIAN_POINT('',(0.,-8.));
#3196 = VECTOR('',#3197,1.);
#3197 = DIRECTION('',(1.,0.));
#3198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3199 = PCURVE('',#1999,#3200);
#3200 = DEFINITIONAL_REPRESENTATION('',(#3201),#3209);
#3201 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3202,#3203,#3204,#3205,
#3206,#3207,#3208),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3202 = CARTESIAN_POINT('',(56.801809069052,-12.5));
#3203 = CARTESIAN_POINT('',(56.801809069052,0.490381056767));
#3204 = CARTESIAN_POINT('',(68.051809069052,-6.004809471617));
#3205 = CARTESIAN_POINT('',(79.301809069052,-12.5));
#3206 = CARTESIAN_POINT('',(68.051809069052,-18.99519052838));
#3207 = CARTESIAN_POINT('',(56.801809069052,-25.49038105676));
#3208 = CARTESIAN_POINT('',(56.801809069052,-12.5));
#3209 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3210 = ORIENTED_EDGE('',*,*,#3162,.F.);
#3211 = ADVANCED_FACE('',(#3212),#1726,.F.);
#3212 = FACE_BOUND('',#3213,.F.);
#3213 = EDGE_LOOP('',(#3214,#3215,#3238,#3264));
#3214 = ORIENTED_EDGE('',*,*,#1709,.F.);
#3215 = ORIENTED_EDGE('',*,*,#3216,.T.);
#3216 = EDGE_CURVE('',#1710,#3217,#3219,.T.);
#3217 = VERTEX_POINT('',#3218);
#3218 = CARTESIAN_POINT('',(25.261297378395,138.55516903227,
    15.994391594875));
#3219 = SEAM_CURVE('',#3220,(#3224,#3231),.PCURVE_S1.);
#3220 = LINE('',#3221,#3222);
#3221 = CARTESIAN_POINT('',(25.261297378395,130.55516903227,
    15.994391594875));
#3222 = VECTOR('',#3223,1.);
#3223 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3224 = PCURVE('',#1726,#3225);
#3225 = DEFINITIONAL_REPRESENTATION('',(#3226),#3230);
#3226 = LINE('',#3227,#3228);
#3227 = CARTESIAN_POINT('',(6.28318530718,0.));
#3228 = VECTOR('',#3229,1.);
#3229 = DIRECTION('',(0.,-1.));
#3230 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3231 = PCURVE('',#1726,#3232);
#3232 = DEFINITIONAL_REPRESENTATION('',(#3233),#3237);
#3233 = LINE('',#3234,#3235);
#3234 = CARTESIAN_POINT('',(0.,0.));
#3235 = VECTOR('',#3236,1.);
#3236 = DIRECTION('',(0.,-1.));
#3237 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3238 = ORIENTED_EDGE('',*,*,#3239,.T.);
#3239 = EDGE_CURVE('',#3217,#3217,#3240,.T.);
#3240 = SURFACE_CURVE('',#3241,(#3246,#3253),.PCURVE_S1.);
#3241 = CIRCLE('',#3242,2.025);
#3242 = AXIS2_PLACEMENT_3D('',#3243,#3244,#3245);
#3243 = CARTESIAN_POINT('',(25.261297378395,138.55516903227,
    18.019391594875));
#3244 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3245 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3246 = PCURVE('',#1726,#3247);
#3247 = DEFINITIONAL_REPRESENTATION('',(#3248),#3252);
#3248 = LINE('',#3249,#3250);
#3249 = CARTESIAN_POINT('',(0.,-8.));
#3250 = VECTOR('',#3251,1.);
#3251 = DIRECTION('',(1.,0.));
#3252 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3253 = PCURVE('',#1999,#3254);
#3254 = DEFINITIONAL_REPRESENTATION('',(#3255),#3263);
#3255 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3256,#3257,#3258,#3259,
#3260,#3261,#3262),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3256 = CARTESIAN_POINT('',(18.776809068467,-9.88526329055));
#3257 = CARTESIAN_POINT('',(18.776809068467,-6.377860405223));
#3258 = CARTESIAN_POINT('',(21.814309068467,-8.131561847887));
#3259 = CARTESIAN_POINT('',(24.851809068467,-9.88526329055));
#3260 = CARTESIAN_POINT('',(21.814309068467,-11.63896473321));
#3261 = CARTESIAN_POINT('',(18.776809068467,-13.39266617587));
#3262 = CARTESIAN_POINT('',(18.776809068467,-9.88526329055));
#3263 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3264 = ORIENTED_EDGE('',*,*,#3216,.F.);
#3265 = ADVANCED_FACE('',(#3266),#1757,.F.);
#3266 = FACE_BOUND('',#3267,.F.);
#3267 = EDGE_LOOP('',(#3268,#3291,#3318,#3319));
#3268 = ORIENTED_EDGE('',*,*,#3269,.T.);
#3269 = EDGE_CURVE('',#1741,#3270,#3272,.T.);
#3270 = VERTEX_POINT('',#3271);
#3271 = CARTESIAN_POINT('',(16.817549691578,133.05516903227,
    18.644391595083));
#3272 = SEAM_CURVE('',#3273,(#3277,#3284),.PCURVE_S1.);
#3273 = LINE('',#3274,#3275);
#3274 = CARTESIAN_POINT('',(16.817549691578,130.55516903227,
    18.644391595083));
#3275 = VECTOR('',#3276,1.);
#3276 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3277 = PCURVE('',#1757,#3278);
#3278 = DEFINITIONAL_REPRESENTATION('',(#3279),#3283);
#3279 = LINE('',#3280,#3281);
#3280 = CARTESIAN_POINT('',(0.,0.));
#3281 = VECTOR('',#3282,1.);
#3282 = DIRECTION('',(0.,-1.));
#3283 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3284 = PCURVE('',#1757,#3285);
#3285 = DEFINITIONAL_REPRESENTATION('',(#3286),#3290);
#3286 = LINE('',#3287,#3288);
#3287 = CARTESIAN_POINT('',(6.28318530718,0.));
#3288 = VECTOR('',#3289,1.);
#3289 = DIRECTION('',(0.,-1.));
#3290 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3291 = ORIENTED_EDGE('',*,*,#3292,.T.);
#3292 = EDGE_CURVE('',#3270,#3270,#3293,.T.);
#3293 = SURFACE_CURVE('',#3294,(#3299,#3306),.PCURVE_S1.);
#3294 = CIRCLE('',#3295,4.25);
#3295 = AXIS2_PLACEMENT_3D('',#3296,#3297,#3298);
#3296 = CARTESIAN_POINT('',(16.817549691578,133.05516903227,
    22.894391595083));
#3297 = DIRECTION('',(6.980527267331E-15,-1.,-7.42461647718E-16));
#3298 = DIRECTION('',(3.774758283726E-15,7.424616477181E-16,-1.));
#3299 = PCURVE('',#1757,#3300);
#3300 = DEFINITIONAL_REPRESENTATION('',(#3301),#3305);
#3301 = LINE('',#3302,#3303);
#3302 = CARTESIAN_POINT('',(0.,-2.5));
#3303 = VECTOR('',#3304,1.);
#3304 = DIRECTION('',(1.,0.));
#3305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3306 = PCURVE('',#3307,#3312);
#3307 = PLANE('',#3308);
#3308 = AXIS2_PLACEMENT_3D('',#3309,#3310,#3311);
#3309 = CARTESIAN_POINT('',(16.817549691578,133.05516903227,
    22.894391595083));
#3310 = DIRECTION('',(6.963090749755E-15,-1.,-7.771561172376E-16));
#3311 = DIRECTION('',(-3.845531969661E-15,-7.771561172376E-16,1.));
#3312 = DEFINITIONAL_REPRESENTATION('',(#3313),#3317);
#3313 = CIRCLE('',#3314,4.25);
#3314 = AXIS2_PLACEMENT_2D('',#3315,#3316);
#3315 = CARTESIAN_POINT('',(1.912E-14,-1.599E-14));
#3316 = DIRECTION('',(-1.,-3.E-17));
#3317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3318 = ORIENTED_EDGE('',*,*,#3269,.F.);
#3319 = ORIENTED_EDGE('',*,*,#1740,.F.);
#3320 = ADVANCED_FACE('',(#3321),#1788,.F.);
#3321 = FACE_BOUND('',#3322,.F.);
#3322 = EDGE_LOOP('',(#3323,#3324,#3347,#3373));
#3323 = ORIENTED_EDGE('',*,*,#1771,.F.);
#3324 = ORIENTED_EDGE('',*,*,#3325,.T.);
#3325 = EDGE_CURVE('',#1772,#3326,#3328,.T.);
#3326 = VERTEX_POINT('',#3327);
#3327 = CARTESIAN_POINT('',(0.146560668945,138.55516903227,
    21.01939159546));
#3328 = SEAM_CURVE('',#3329,(#3333,#3340),.PCURVE_S1.);
#3329 = LINE('',#3330,#3331);
#3330 = CARTESIAN_POINT('',(0.146560668945,130.55516903227,
    21.01939159546));
#3331 = VECTOR('',#3332,1.);
#3332 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3333 = PCURVE('',#1788,#3334);
#3334 = DEFINITIONAL_REPRESENTATION('',(#3335),#3339);
#3335 = LINE('',#3336,#3337);
#3336 = CARTESIAN_POINT('',(6.28318530718,0.));
#3337 = VECTOR('',#3338,1.);
#3338 = DIRECTION('',(0.,-1.));
#3339 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3340 = PCURVE('',#1788,#3341);
#3341 = DEFINITIONAL_REPRESENTATION('',(#3342),#3346);
#3342 = LINE('',#3343,#3344);
#3343 = CARTESIAN_POINT('',(0.,0.));
#3344 = VECTOR('',#3345,1.);
#3345 = DIRECTION('',(0.,-1.));
#3346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3347 = ORIENTED_EDGE('',*,*,#3348,.T.);
#3348 = EDGE_CURVE('',#3326,#3326,#3349,.T.);
#3349 = SURFACE_CURVE('',#3350,(#3355,#3362),.PCURVE_S1.);
#3350 = CIRCLE('',#3351,11.5);
#3351 = AXIS2_PLACEMENT_3D('',#3352,#3353,#3354);
#3352 = CARTESIAN_POINT('',(0.146560668945,138.55516903227,
    32.51939159546));
#3353 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3354 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3355 = PCURVE('',#1788,#3356);
#3356 = DEFINITIONAL_REPRESENTATION('',(#3357),#3361);
#3357 = LINE('',#3358,#3359);
#3358 = CARTESIAN_POINT('',(0.,-8.));
#3359 = VECTOR('',#3360,1.);
#3360 = DIRECTION('',(1.,0.));
#3361 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3362 = PCURVE('',#1999,#3363);
#3363 = DEFINITIONAL_REPRESENTATION('',(#3364),#3372);
#3364 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3365,#3366,#3367,#3368,
#3369,#3370,#3371),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3365 = CARTESIAN_POINT('',(23.801809069052,-35.));
#3366 = CARTESIAN_POINT('',(23.801809069052,-15.08141571295));
#3367 = CARTESIAN_POINT('',(41.051809069052,-25.04070785647));
#3368 = CARTESIAN_POINT('',(58.301809069052,-35.));
#3369 = CARTESIAN_POINT('',(41.051809069052,-44.95929214352));
#3370 = CARTESIAN_POINT('',(23.801809069052,-54.91858428704));
#3371 = CARTESIAN_POINT('',(23.801809069052,-35.));
#3372 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3373 = ORIENTED_EDGE('',*,*,#3325,.F.);
#3374 = ADVANCED_FACE('',(#3375),#1819,.F.);
#3375 = FACE_BOUND('',#3376,.F.);
#3376 = EDGE_LOOP('',(#3377,#3378,#3401,#3427));
#3377 = ORIENTED_EDGE('',*,*,#1802,.F.);
#3378 = ORIENTED_EDGE('',*,*,#3379,.T.);
#3379 = EDGE_CURVE('',#1803,#3380,#3382,.T.);
#3380 = VERTEX_POINT('',#3381);
#3381 = CARTESIAN_POINT('',(-24.96817604051,138.55516903227,
    15.994391594895));
#3382 = SEAM_CURVE('',#3383,(#3387,#3394),.PCURVE_S1.);
#3383 = LINE('',#3384,#3385);
#3384 = CARTESIAN_POINT('',(-24.96817604051,130.55516903227,
    15.994391594895));
#3385 = VECTOR('',#3386,1.);
#3386 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3387 = PCURVE('',#1819,#3388);
#3388 = DEFINITIONAL_REPRESENTATION('',(#3389),#3393);
#3389 = LINE('',#3390,#3391);
#3390 = CARTESIAN_POINT('',(6.28318530718,0.));
#3391 = VECTOR('',#3392,1.);
#3392 = DIRECTION('',(0.,-1.));
#3393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3394 = PCURVE('',#1819,#3395);
#3395 = DEFINITIONAL_REPRESENTATION('',(#3396),#3400);
#3396 = LINE('',#3397,#3398);
#3397 = CARTESIAN_POINT('',(0.,0.));
#3398 = VECTOR('',#3399,1.);
#3399 = DIRECTION('',(0.,-1.));
#3400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3401 = ORIENTED_EDGE('',*,*,#3402,.T.);
#3402 = EDGE_CURVE('',#3380,#3380,#3403,.T.);
#3403 = SURFACE_CURVE('',#3404,(#3409,#3416),.PCURVE_S1.);
#3404 = CIRCLE('',#3405,2.025);
#3405 = AXIS2_PLACEMENT_3D('',#3406,#3407,#3408);
#3406 = CARTESIAN_POINT('',(-24.96817604051,138.55516903227,
    18.019391594895));
#3407 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3408 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3409 = PCURVE('',#1819,#3410);
#3410 = DEFINITIONAL_REPRESENTATION('',(#3411),#3415);
#3411 = LINE('',#3412,#3413);
#3412 = CARTESIAN_POINT('',(0.,-8.));
#3413 = VECTOR('',#3414,1.);
#3414 = DIRECTION('',(1.,0.));
#3415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3416 = PCURVE('',#1999,#3417);
#3417 = DEFINITIONAL_REPRESENTATION('',(#3418),#3426);
#3418 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3419,#3420,#3421,#3422,
#3423,#3424,#3425),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3419 = CARTESIAN_POINT('',(18.776809068487,-60.11473670945));
#3420 = CARTESIAN_POINT('',(18.776809068487,-56.60733382413));
#3421 = CARTESIAN_POINT('',(21.814309068487,-58.36103526679));
#3422 = CARTESIAN_POINT('',(24.851809068487,-60.11473670945));
#3423 = CARTESIAN_POINT('',(21.814309068487,-61.86843815212));
#3424 = CARTESIAN_POINT('',(18.776809068487,-63.62213959478));
#3425 = CARTESIAN_POINT('',(18.776809068487,-60.11473670945));
#3426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3427 = ORIENTED_EDGE('',*,*,#3379,.F.);
#3428 = ADVANCED_FACE('',(#3429),#1850,.F.);
#3429 = FACE_BOUND('',#3430,.F.);
#3430 = EDGE_LOOP('',(#3431,#3432,#3455,#3481));
#3431 = ORIENTED_EDGE('',*,*,#1833,.F.);
#3432 = ORIENTED_EDGE('',*,*,#3433,.T.);
#3433 = EDGE_CURVE('',#1834,#3434,#3436,.T.);
#3434 = VERTEX_POINT('',#3435);
#3435 = CARTESIAN_POINT('',(-22.35343933105,138.55516903227,
    54.01939159546));
#3436 = SEAM_CURVE('',#3437,(#3441,#3448),.PCURVE_S1.);
#3437 = LINE('',#3438,#3439);
#3438 = CARTESIAN_POINT('',(-22.35343933105,130.55516903227,
    54.01939159546));
#3439 = VECTOR('',#3440,1.);
#3440 = DIRECTION('',(-6.973090749755E-15,1.,7.216449660064E-16));
#3441 = PCURVE('',#1850,#3442);
#3442 = DEFINITIONAL_REPRESENTATION('',(#3443),#3447);
#3443 = LINE('',#3444,#3445);
#3444 = CARTESIAN_POINT('',(6.28318530718,0.));
#3445 = VECTOR('',#3446,1.);
#3446 = DIRECTION('',(0.,-1.));
#3447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3448 = PCURVE('',#1850,#3449);
#3449 = DEFINITIONAL_REPRESENTATION('',(#3450),#3454);
#3450 = LINE('',#3451,#3452);
#3451 = CARTESIAN_POINT('',(0.,0.));
#3452 = VECTOR('',#3453,1.);
#3453 = DIRECTION('',(0.,-1.));
#3454 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3455 = ORIENTED_EDGE('',*,*,#3456,.T.);
#3456 = EDGE_CURVE('',#3434,#3434,#3457,.T.);
#3457 = SURFACE_CURVE('',#3458,(#3463,#3470),.PCURVE_S1.);
#3458 = CIRCLE('',#3459,7.5);
#3459 = AXIS2_PLACEMENT_3D('',#3460,#3461,#3462);
#3460 = CARTESIAN_POINT('',(-22.35343933105,138.55516903227,
    61.51939159546));
#3461 = DIRECTION('',(6.973090749755E-15,-1.,-7.216449660063E-16));
#3462 = DIRECTION('',(3.845531969661E-15,7.216449660064E-16,-1.));
#3463 = PCURVE('',#1850,#3464);
#3464 = DEFINITIONAL_REPRESENTATION('',(#3465),#3469);
#3465 = LINE('',#3466,#3467);
#3466 = CARTESIAN_POINT('',(0.,-8.));
#3467 = VECTOR('',#3468,1.);
#3468 = DIRECTION('',(1.,0.));
#3469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3470 = PCURVE('',#1999,#3471);
#3471 = DEFINITIONAL_REPRESENTATION('',(#3472),#3480);
#3472 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3473,#3474,#3475,#3476,
#3477,#3478,#3479),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3473 = CARTESIAN_POINT('',(56.801809069052,-57.5));
#3474 = CARTESIAN_POINT('',(56.801809069052,-44.50961894323));
#3475 = CARTESIAN_POINT('',(68.051809069052,-51.00480947161));
#3476 = CARTESIAN_POINT('',(79.301809069052,-57.5));
#3477 = CARTESIAN_POINT('',(68.051809069052,-63.99519052838));
#3478 = CARTESIAN_POINT('',(56.801809069052,-70.49038105676));
#3479 = CARTESIAN_POINT('',(56.801809069052,-57.5));
#3480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3481 = ORIENTED_EDGE('',*,*,#3433,.F.);
#3482 = ADVANCED_FACE('',(#3483),#1908,.F.);
#3483 = FACE_BOUND('',#3484,.F.);
#3484 = EDGE_LOOP('',(#3485,#3508,#3535,#3536));
#3485 = ORIENTED_EDGE('',*,*,#3486,.T.);
#3486 = EDGE_CURVE('',#1892,#3487,#3489,.T.);
#3487 = VERTEX_POINT('',#3488);
#3488 = CARTESIAN_POINT('',(40.146560668945,188.07956033014,
    47.299555662289));
#3489 = SEAM_CURVE('',#3490,(#3494,#3501),.PCURVE_S1.);
#3490 = LINE('',#3491,#3492);
#3491 = CARTESIAN_POINT('',(40.146560668945,190.1316811901,
    52.937711387004));
#3492 = VECTOR('',#3493,1.);
#3493 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3494 = PCURVE('',#1908,#3495);
#3495 = DEFINITIONAL_REPRESENTATION('',(#3496),#3500);
#3496 = LINE('',#3497,#3498);
#3497 = CARTESIAN_POINT('',(0.,0.));
#3498 = VECTOR('',#3499,1.);
#3499 = DIRECTION('',(0.,-1.));
#3500 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3501 = PCURVE('',#1908,#3502);
#3502 = DEFINITIONAL_REPRESENTATION('',(#3503),#3507);
#3503 = LINE('',#3504,#3505);
#3504 = CARTESIAN_POINT('',(6.28318530718,0.));
#3505 = VECTOR('',#3506,1.);
#3506 = DIRECTION('',(0.,-1.));
#3507 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3508 = ORIENTED_EDGE('',*,*,#3509,.T.);
#3509 = EDGE_CURVE('',#3487,#3487,#3510,.T.);
#3510 = SURFACE_CURVE('',#3511,(#3516,#3523),.PCURVE_S1.);
#3511 = CIRCLE('',#3512,1.6);
#3512 = AXIS2_PLACEMENT_3D('',#3513,#3514,#3515);
#3513 = CARTESIAN_POINT('',(40.146560668945,189.5830685234,
    46.752323432968));
#3514 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3515 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3516 = PCURVE('',#1908,#3517);
#3517 = DEFINITIONAL_REPRESENTATION('',(#3518),#3522);
#3518 = LINE('',#3519,#3520);
#3519 = CARTESIAN_POINT('',(0.,-6.));
#3520 = VECTOR('',#3521,1.);
#3521 = DIRECTION('',(1.,0.));
#3522 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3523 = PCURVE('',#3524,#3529);
#3524 = PLANE('',#3525);
#3525 = AXIS2_PLACEMENT_3D('',#3526,#3527,#3528);
#3526 = CARTESIAN_POINT('',(40.146560668945,189.5830685234,
    46.752323432968));
#3527 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3528 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#3529 = DEFINITIONAL_REPRESENTATION('',(#3530),#3534);
#3530 = CIRCLE('',#3531,1.6);
#3531 = AXIS2_PLACEMENT_2D('',#3532,#3533);
#3532 = CARTESIAN_POINT('',(7.11E-15,8.88E-15));
#3533 = DIRECTION('',(0.,1.));
#3534 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3535 = ORIENTED_EDGE('',*,*,#3486,.F.);
#3536 = ORIENTED_EDGE('',*,*,#1891,.F.);
#3537 = ADVANCED_FACE('',(#3538),#1939,.F.);
#3538 = FACE_BOUND('',#3539,.F.);
#3539 = EDGE_LOOP('',(#3540,#3563,#3590,#3591));
#3540 = ORIENTED_EDGE('',*,*,#3541,.T.);
#3541 = EDGE_CURVE('',#1923,#3542,#3544,.T.);
#3542 = VERTEX_POINT('',#3543);
#3543 = CARTESIAN_POINT('',(40.146560668945,131.69800308299,
    67.820764261829));
#3544 = SEAM_CURVE('',#3545,(#3549,#3556),.PCURVE_S1.);
#3545 = LINE('',#3546,#3547);
#3546 = CARTESIAN_POINT('',(40.146560668945,133.75012394294,
    73.458919986544));
#3547 = VECTOR('',#3548,1.);
#3548 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3549 = PCURVE('',#1939,#3550);
#3550 = DEFINITIONAL_REPRESENTATION('',(#3551),#3555);
#3551 = LINE('',#3552,#3553);
#3552 = CARTESIAN_POINT('',(0.,0.));
#3553 = VECTOR('',#3554,1.);
#3554 = DIRECTION('',(0.,-1.));
#3555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3556 = PCURVE('',#1939,#3557);
#3557 = DEFINITIONAL_REPRESENTATION('',(#3558),#3562);
#3558 = LINE('',#3559,#3560);
#3559 = CARTESIAN_POINT('',(6.28318530718,0.));
#3560 = VECTOR('',#3561,1.);
#3561 = DIRECTION('',(0.,-1.));
#3562 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3563 = ORIENTED_EDGE('',*,*,#3564,.T.);
#3564 = EDGE_CURVE('',#3542,#3542,#3565,.T.);
#3565 = SURFACE_CURVE('',#3566,(#3571,#3578),.PCURVE_S1.);
#3566 = CIRCLE('',#3567,1.6);
#3567 = AXIS2_PLACEMENT_3D('',#3568,#3569,#3570);
#3568 = CARTESIAN_POINT('',(40.146560668945,133.20151127625,
    67.273532032508));
#3569 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3570 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3571 = PCURVE('',#1939,#3572);
#3572 = DEFINITIONAL_REPRESENTATION('',(#3573),#3577);
#3573 = LINE('',#3574,#3575);
#3574 = CARTESIAN_POINT('',(0.,-6.));
#3575 = VECTOR('',#3576,1.);
#3576 = DIRECTION('',(1.,0.));
#3577 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3578 = PCURVE('',#3579,#3584);
#3579 = PLANE('',#3580);
#3580 = AXIS2_PLACEMENT_3D('',#3581,#3582,#3583);
#3581 = CARTESIAN_POINT('',(40.146560668945,133.20151127625,
    67.273532032508));
#3582 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3583 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3584 = DEFINITIONAL_REPRESENTATION('',(#3585),#3589);
#3585 = CIRCLE('',#3586,1.6);
#3586 = AXIS2_PLACEMENT_2D('',#3587,#3588);
#3587 = CARTESIAN_POINT('',(1.421E-14,-2.132E-14));
#3588 = DIRECTION('',(1.,0.));
#3589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3590 = ORIENTED_EDGE('',*,*,#3541,.F.);
#3591 = ORIENTED_EDGE('',*,*,#1922,.F.);
#3592 = ADVANCED_FACE('',(#3593,#3641,#3644,#3647,#3650,#3653),#1971,.T.
  );
#3593 = FACE_BOUND('',#3594,.T.);
#3594 = EDGE_LOOP('',(#3595,#3618,#3639,#3640));
#3595 = ORIENTED_EDGE('',*,*,#3596,.T.);
#3596 = EDGE_CURVE('',#3013,#3597,#3599,.T.);
#3597 = VERTEX_POINT('',#3598);
#3598 = CARTESIAN_POINT('',(35.146560668945,138.55516903227,
    -2.782417473592));
#3599 = SURFACE_CURVE('',#3600,(#3604,#3611),.PCURVE_S1.);
#3600 = LINE('',#3601,#3602);
#3601 = CARTESIAN_POINT('',(35.146560668945,164.8747014882,
    -12.36194386735));
#3602 = VECTOR('',#3603,1.);
#3603 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3604 = PCURVE('',#1971,#3605);
#3605 = DEFINITIONAL_REPRESENTATION('',(#3606),#3610);
#3606 = LINE('',#3607,#3608);
#3607 = CARTESIAN_POINT('',(-22.56729084562,-44.6538084593));
#3608 = VECTOR('',#3609,1.);
#3609 = DIRECTION('',(1.,0.));
#3610 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3611 = PCURVE('',#2614,#3612);
#3612 = DEFINITIONAL_REPRESENTATION('',(#3613),#3617);
#3613 = LINE('',#3614,#3615);
#3614 = CARTESIAN_POINT('',(0.,0.));
#3615 = VECTOR('',#3616,1.);
#3616 = DIRECTION('',(1.,0.));
#3617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3618 = ORIENTED_EDGE('',*,*,#3619,.T.);
#3619 = EDGE_CURVE('',#3597,#1956,#3620,.T.);
#3620 = SURFACE_CURVE('',#3621,(#3625,#3632),.PCURVE_S1.);
#3621 = LINE('',#3622,#3623);
#3622 = CARTESIAN_POINT('',(35.146560668945,138.55516903227,
    -2.782417473592));
#3623 = VECTOR('',#3624,1.);
#3624 = DIRECTION('',(-3.845531969661E-15,3.774758283726E-15,1.));
#3625 = PCURVE('',#1971,#3626);
#3626 = DEFINITIONAL_REPRESENTATION('',(#3627),#3631);
#3627 = LINE('',#3628,#3629);
#3628 = CARTESIAN_POINT('',(5.441370575937,-44.6538084593));
#3629 = VECTOR('',#3630,1.);
#3630 = DIRECTION('',(0.342020143326,0.939692620786));
#3631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3632 = PCURVE('',#1999,#3633);
#3633 = DEFINITIONAL_REPRESENTATION('',(#3634),#3638);
#3634 = LINE('',#3635,#3636);
#3635 = CARTESIAN_POINT('',(0.,0.));
#3636 = VECTOR('',#3637,1.);
#3637 = DIRECTION('',(1.,0.));
#3638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3639 = ORIENTED_EDGE('',*,*,#1953,.F.);
#3640 = ORIENTED_EDGE('',*,*,#3012,.T.);
#3641 = FACE_BOUND('',#3642,.T.);
#3642 = EDGE_LOOP('',(#3643));
#3643 = ORIENTED_EDGE('',*,*,#2766,.T.);
#3644 = FACE_BOUND('',#3645,.T.);
#3645 = EDGE_LOOP('',(#3646));
#3646 = ORIENTED_EDGE('',*,*,#2820,.T.);
#3647 = FACE_BOUND('',#3648,.T.);
#3648 = EDGE_LOOP('',(#3649));
#3649 = ORIENTED_EDGE('',*,*,#2874,.T.);
#3650 = FACE_BOUND('',#3651,.T.);
#3651 = EDGE_LOOP('',(#3652));
#3652 = ORIENTED_EDGE('',*,*,#2928,.T.);
#3653 = FACE_BOUND('',#3654,.T.);
#3654 = EDGE_LOOP('',(#3655));
#3655 = ORIENTED_EDGE('',*,*,#2982,.T.);
#3656 = ADVANCED_FACE('',(#3657,#3705,#3708,#3711,#3714,#3717,#3720),
  #1999,.T.);
#3657 = FACE_BOUND('',#3658,.T.);
#3658 = EDGE_LOOP('',(#3659,#3660,#3683,#3704));
#3659 = ORIENTED_EDGE('',*,*,#3619,.F.);
#3660 = ORIENTED_EDGE('',*,*,#3661,.T.);
#3661 = EDGE_CURVE('',#3597,#3662,#3664,.T.);
#3662 = VERTEX_POINT('',#3663);
#3663 = CARTESIAN_POINT('',(-34.85343933105,138.55516903227,
    -2.782417473592));
#3664 = SURFACE_CURVE('',#3665,(#3669,#3676),.PCURVE_S1.);
#3665 = LINE('',#3666,#3667);
#3666 = CARTESIAN_POINT('',(35.146560668945,138.55516903227,
    -2.782417473592));
#3667 = VECTOR('',#3668,1.);
#3668 = DIRECTION('',(-1.,-6.976358373182E-15,-3.840776401192E-15));
#3669 = PCURVE('',#1999,#3670);
#3670 = DEFINITIONAL_REPRESENTATION('',(#3671),#3675);
#3671 = LINE('',#3672,#3673);
#3672 = CARTESIAN_POINT('',(0.,0.));
#3673 = VECTOR('',#3674,1.);
#3674 = DIRECTION('',(0.,-1.));
#3675 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3676 = PCURVE('',#2614,#3677);
#3677 = DEFINITIONAL_REPRESENTATION('',(#3678),#3682);
#3678 = LINE('',#3679,#3680);
#3679 = CARTESIAN_POINT('',(28.008661421559,-9.4E-16));
#3680 = VECTOR('',#3681,1.);
#3681 = DIRECTION('',(0.,-1.));
#3682 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3683 = ORIENTED_EDGE('',*,*,#3684,.T.);
#3684 = EDGE_CURVE('',#3662,#1984,#3685,.T.);
#3685 = SURFACE_CURVE('',#3686,(#3690,#3697),.PCURVE_S1.);
#3686 = LINE('',#3687,#3688);
#3687 = CARTESIAN_POINT('',(-34.85343933105,138.55516903227,
    -2.782417473592));
#3688 = VECTOR('',#3689,1.);
#3689 = DIRECTION('',(-3.845531969661E-15,3.774758283726E-15,1.));
#3690 = PCURVE('',#1999,#3691);
#3691 = DEFINITIONAL_REPRESENTATION('',(#3692),#3696);
#3692 = LINE('',#3693,#3694);
#3693 = CARTESIAN_POINT('',(1.57E-15,-70.));
#3694 = VECTOR('',#3695,1.);
#3695 = DIRECTION('',(1.,0.));
#3696 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3697 = PCURVE('',#2027,#3698);
#3698 = DEFINITIONAL_REPRESENTATION('',(#3699),#3703);
#3699 = LINE('',#3700,#3701);
#3700 = CARTESIAN_POINT('',(5.441370575937,-44.6538084593));
#3701 = VECTOR('',#3702,1.);
#3702 = DIRECTION('',(0.342020143326,0.939692620786));
#3703 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3704 = ORIENTED_EDGE('',*,*,#1983,.F.);
#3705 = FACE_BOUND('',#3706,.T.);
#3706 = EDGE_LOOP('',(#3707));
#3707 = ORIENTED_EDGE('',*,*,#3185,.T.);
#3708 = FACE_BOUND('',#3709,.T.);
#3709 = EDGE_LOOP('',(#3710));
#3710 = ORIENTED_EDGE('',*,*,#3131,.T.);
#3711 = FACE_BOUND('',#3712,.T.);
#3712 = EDGE_LOOP('',(#3713));
#3713 = ORIENTED_EDGE('',*,*,#3456,.T.);
#3714 = FACE_BOUND('',#3715,.T.);
#3715 = EDGE_LOOP('',(#3716));
#3716 = ORIENTED_EDGE('',*,*,#3402,.T.);
#3717 = FACE_BOUND('',#3718,.T.);
#3718 = EDGE_LOOP('',(#3719));
#3719 = ORIENTED_EDGE('',*,*,#3239,.T.);
#3720 = FACE_BOUND('',#3721,.T.);
#3721 = EDGE_LOOP('',(#3722));
#3722 = ORIENTED_EDGE('',*,*,#3348,.T.);
#3723 = ADVANCED_FACE('',(#3724,#3750,#3753,#3756,#3759,#3762),#2027,.F.
  );
#3724 = FACE_BOUND('',#3725,.F.);
#3725 = EDGE_LOOP('',(#3726,#3747,#3748,#3749));
#3726 = ORIENTED_EDGE('',*,*,#3727,.T.);
#3727 = EDGE_CURVE('',#3037,#3662,#3728,.T.);
#3728 = SURFACE_CURVE('',#3729,(#3733,#3740),.PCURVE_S1.);
#3729 = LINE('',#3730,#3731);
#3730 = CARTESIAN_POINT('',(-34.85343933105,164.8747014882,
    -12.36194386735));
#3731 = VECTOR('',#3732,1.);
#3732 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3733 = PCURVE('',#2027,#3734);
#3734 = DEFINITIONAL_REPRESENTATION('',(#3735),#3739);
#3735 = LINE('',#3736,#3737);
#3736 = CARTESIAN_POINT('',(-22.56729084562,-44.6538084593));
#3737 = VECTOR('',#3738,1.);
#3738 = DIRECTION('',(1.,0.));
#3739 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3740 = PCURVE('',#2614,#3741);
#3741 = DEFINITIONAL_REPRESENTATION('',(#3742),#3746);
#3742 = LINE('',#3743,#3744);
#3743 = CARTESIAN_POINT('',(1.19E-15,-70.));
#3744 = VECTOR('',#3745,1.);
#3745 = DIRECTION('',(1.,0.));
#3746 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3747 = ORIENTED_EDGE('',*,*,#3684,.T.);
#3748 = ORIENTED_EDGE('',*,*,#2011,.F.);
#3749 = ORIENTED_EDGE('',*,*,#3036,.T.);
#3750 = FACE_BOUND('',#3751,.F.);
#3751 = EDGE_LOOP('',(#3752));
#3752 = ORIENTED_EDGE('',*,*,#2125,.T.);
#3753 = FACE_BOUND('',#3754,.F.);
#3754 = EDGE_LOOP('',(#3755));
#3755 = ORIENTED_EDGE('',*,*,#2179,.T.);
#3756 = FACE_BOUND('',#3757,.F.);
#3757 = EDGE_LOOP('',(#3758));
#3758 = ORIENTED_EDGE('',*,*,#2233,.T.);
#3759 = FACE_BOUND('',#3760,.F.);
#3760 = EDGE_LOOP('',(#3761));
#3761 = ORIENTED_EDGE('',*,*,#2287,.T.);
#3762 = FACE_BOUND('',#3763,.F.);
#3763 = EDGE_LOOP('',(#3764));
#3764 = ORIENTED_EDGE('',*,*,#2341,.T.);
#3765 = ADVANCED_FACE('',(#3766),#2079,.F.);
#3766 = FACE_BOUND('',#3767,.F.);
#3767 = EDGE_LOOP('',(#3768,#3791,#3818,#3819));
#3768 = ORIENTED_EDGE('',*,*,#3769,.T.);
#3769 = EDGE_CURVE('',#2063,#3770,#3772,.T.);
#3770 = VERTEX_POINT('',#3771);
#3771 = CARTESIAN_POINT('',(-39.85343933105,131.69800308299,
    67.820764261828));
#3772 = SEAM_CURVE('',#3773,(#3777,#3784),.PCURVE_S1.);
#3773 = LINE('',#3774,#3775);
#3774 = CARTESIAN_POINT('',(-39.85343933105,133.75012394294,
    73.458919986544));
#3775 = VECTOR('',#3776,1.);
#3776 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3777 = PCURVE('',#2079,#3778);
#3778 = DEFINITIONAL_REPRESENTATION('',(#3779),#3783);
#3779 = LINE('',#3780,#3781);
#3780 = CARTESIAN_POINT('',(0.,0.));
#3781 = VECTOR('',#3782,1.);
#3782 = DIRECTION('',(0.,-1.));
#3783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3784 = PCURVE('',#2079,#3785);
#3785 = DEFINITIONAL_REPRESENTATION('',(#3786),#3790);
#3786 = LINE('',#3787,#3788);
#3787 = CARTESIAN_POINT('',(6.28318530718,0.));
#3788 = VECTOR('',#3789,1.);
#3789 = DIRECTION('',(0.,-1.));
#3790 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3791 = ORIENTED_EDGE('',*,*,#3792,.T.);
#3792 = EDGE_CURVE('',#3770,#3770,#3793,.T.);
#3793 = SURFACE_CURVE('',#3794,(#3799,#3806),.PCURVE_S1.);
#3794 = CIRCLE('',#3795,1.6);
#3795 = AXIS2_PLACEMENT_3D('',#3796,#3797,#3798);
#3796 = CARTESIAN_POINT('',(-39.85343933105,133.20151127625,
    67.273532032507));
#3797 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3798 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3799 = PCURVE('',#2079,#3800);
#3800 = DEFINITIONAL_REPRESENTATION('',(#3801),#3805);
#3801 = LINE('',#3802,#3803);
#3802 = CARTESIAN_POINT('',(0.,-6.));
#3803 = VECTOR('',#3804,1.);
#3804 = DIRECTION('',(1.,0.));
#3805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3806 = PCURVE('',#3807,#3812);
#3807 = PLANE('',#3808);
#3808 = AXIS2_PLACEMENT_3D('',#3809,#3810,#3811);
#3809 = CARTESIAN_POINT('',(-39.85343933105,133.20151127625,
    67.273532032507));
#3810 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3811 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3812 = DEFINITIONAL_REPRESENTATION('',(#3813),#3817);
#3813 = CIRCLE('',#3814,1.6);
#3814 = AXIS2_PLACEMENT_2D('',#3815,#3816);
#3815 = CARTESIAN_POINT('',(-1.421E-14,-1.421E-14));
#3816 = DIRECTION('',(1.,0.));
#3817 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3818 = ORIENTED_EDGE('',*,*,#3769,.F.);
#3819 = ORIENTED_EDGE('',*,*,#2062,.F.);
#3820 = ADVANCED_FACE('',(#3821),#2110,.F.);
#3821 = FACE_BOUND('',#3822,.F.);
#3822 = EDGE_LOOP('',(#3823,#3846,#3873,#3874));
#3823 = ORIENTED_EDGE('',*,*,#3824,.T.);
#3824 = EDGE_CURVE('',#2094,#3825,#3827,.T.);
#3825 = VERTEX_POINT('',#3826);
#3826 = CARTESIAN_POINT('',(-39.85343933105,188.07956033014,
    47.299555662288));
#3827 = SEAM_CURVE('',#3828,(#3832,#3839),.PCURVE_S1.);
#3828 = LINE('',#3829,#3830);
#3829 = CARTESIAN_POINT('',(-39.85343933105,190.1316811901,
    52.937711387004));
#3830 = VECTOR('',#3831,1.);
#3831 = DIRECTION('',(5.995204332976E-15,-0.342020143326,-0.939692620786
    ));
#3832 = PCURVE('',#2110,#3833);
#3833 = DEFINITIONAL_REPRESENTATION('',(#3834),#3838);
#3834 = LINE('',#3835,#3836);
#3835 = CARTESIAN_POINT('',(0.,0.));
#3836 = VECTOR('',#3837,1.);
#3837 = DIRECTION('',(0.,-1.));
#3838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3839 = PCURVE('',#2110,#3840);
#3840 = DEFINITIONAL_REPRESENTATION('',(#3841),#3845);
#3841 = LINE('',#3842,#3843);
#3842 = CARTESIAN_POINT('',(6.28318530718,0.));
#3843 = VECTOR('',#3844,1.);
#3844 = DIRECTION('',(0.,-1.));
#3845 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3846 = ORIENTED_EDGE('',*,*,#3847,.T.);
#3847 = EDGE_CURVE('',#3825,#3825,#3848,.T.);
#3848 = SURFACE_CURVE('',#3849,(#3854,#3861),.PCURVE_S1.);
#3849 = CIRCLE('',#3850,1.6);
#3850 = AXIS2_PLACEMENT_3D('',#3851,#3852,#3853);
#3851 = CARTESIAN_POINT('',(-39.85343933105,189.5830685234,
    46.752323432967));
#3852 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3853 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3854 = PCURVE('',#2110,#3855);
#3855 = DEFINITIONAL_REPRESENTATION('',(#3856),#3860);
#3856 = LINE('',#3857,#3858);
#3857 = CARTESIAN_POINT('',(0.,-6.));
#3858 = VECTOR('',#3859,1.);
#3859 = DIRECTION('',(1.,0.));
#3860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3861 = PCURVE('',#3862,#3867);
#3862 = PLANE('',#3863);
#3863 = AXIS2_PLACEMENT_3D('',#3864,#3865,#3866);
#3864 = CARTESIAN_POINT('',(-39.85343933105,189.5830685234,
    46.752323432967));
#3865 = DIRECTION('',(-5.995204332976E-15,0.342020143326,0.939692620786)
  );
#3866 = DIRECTION('',(5.242009588019E-15,-0.939692620786,0.342020143326)
  );
#3867 = DEFINITIONAL_REPRESENTATION('',(#3868),#3872);
#3868 = CIRCLE('',#3869,1.6);
#3869 = AXIS2_PLACEMENT_2D('',#3870,#3871);
#3870 = CARTESIAN_POINT('',(-8.88E-15,0.));
#3871 = DIRECTION('',(1.,0.));
#3872 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3873 = ORIENTED_EDGE('',*,*,#3824,.F.);
#3874 = ORIENTED_EDGE('',*,*,#2093,.F.);
#3875 = ADVANCED_FACE('',(#3876,#3882,#3885,#3888),#2614,.T.);
#3876 = FACE_BOUND('',#3877,.T.);
#3877 = EDGE_LOOP('',(#3878,#3879,#3880,#3881));
#3878 = ORIENTED_EDGE('',*,*,#3059,.T.);
#3879 = ORIENTED_EDGE('',*,*,#3727,.T.);
#3880 = ORIENTED_EDGE('',*,*,#3661,.F.);
#3881 = ORIENTED_EDGE('',*,*,#3596,.F.);
#3882 = FACE_BOUND('',#3883,.T.);
#3883 = EDGE_LOOP('',(#3884));
#3884 = ORIENTED_EDGE('',*,*,#2599,.T.);
#3885 = FACE_BOUND('',#3886,.T.);
#3886 = EDGE_LOOP('',(#3887));
#3887 = ORIENTED_EDGE('',*,*,#2658,.T.);
#3888 = FACE_BOUND('',#3889,.T.);
#3889 = EDGE_LOOP('',(#3890));
#3890 = ORIENTED_EDGE('',*,*,#2712,.T.);
#3891 = ADVANCED_FACE('',(#3892),#3307,.T.);
#3892 = FACE_BOUND('',#3893,.T.);
#3893 = EDGE_LOOP('',(#3894));
#3894 = ORIENTED_EDGE('',*,*,#3292,.T.);
#3895 = ADVANCED_FACE('',(#3896),#3524,.T.);
#3896 = FACE_BOUND('',#3897,.T.);
#3897 = EDGE_LOOP('',(#3898));
#3898 = ORIENTED_EDGE('',*,*,#3509,.T.);
#3899 = ADVANCED_FACE('',(#3900),#3579,.T.);
#3900 = FACE_BOUND('',#3901,.T.);
#3901 = EDGE_LOOP('',(#3902));
#3902 = ORIENTED_EDGE('',*,*,#3564,.T.);
#3903 = ADVANCED_FACE('',(#3904),#3807,.T.);
#3904 = FACE_BOUND('',#3905,.T.);
#3905 = EDGE_LOOP('',(#3906));
#3906 = ORIENTED_EDGE('',*,*,#3792,.T.);
#3907 = ADVANCED_FACE('',(#3908),#3862,.T.);
#3908 = FACE_BOUND('',#3909,.T.);
#3909 = EDGE_LOOP('',(#3910));
#3910 = ORIENTED_EDGE('',*,*,#3847,.T.);
#3911 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3915)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3912,#3913,#3914)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3912 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3913 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3914 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3915 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#3912,
  'distance_accuracy_value','confusion accuracy');
#3916 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#3917 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #3918),#3911);
#3918 = STYLED_ITEM('color',(#3919),#15);
#3919 = PRESENTATION_STYLE_ASSIGNMENT((#3920,#3926));
#3920 = SURFACE_STYLE_USAGE(.BOTH.,#3921);
#3921 = SURFACE_SIDE_STYLE('',(#3922));
#3922 = SURFACE_STYLE_FILL_AREA(#3923);
#3923 = FILL_AREA_STYLE('',(#3924));
#3924 = FILL_AREA_STYLE_COLOUR('',#3925);
#3925 = COLOUR_RGB('',0.800000011921,0.800000011921,0.800000011921);
#3926 = CURVE_STYLE('',#3927,POSITIVE_LENGTH_MEASURE(0.1),#3925);
#3927 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
