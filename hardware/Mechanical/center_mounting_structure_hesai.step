ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME(
'/home/<USER>/Code/alpha_lidar/hardware/Mechanical/center_mounting_
structure_hesai.step','2024-09-29T18:21:31',('Author'),(''),
  'Open CASCADE STEP processor 7.3','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('Body','Body','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#5943);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#194,#456,#538,#578,#661,#2164,#2191,#2218,
    #2245,#2301,#2356,#2411,#2466,#2521,#2556,#3472,#3677,#3755,#3810,
    #3860,#3910,#3960,#3987,#3994,#4033,#4040,#4047,#4191,#4240,#4268,
    #4317,#4344,#4394,#4445,#4569,#4618,#4667,#4716,#4765,#4814,#4863,
    #4915,#4942,#4969,#5019,#5074,#5124,#5174,#5229,#5327,#5425,#5480,
    #5535,#5590,#5734,#5786,#5816,#5843,#5876,#5888,#5892,#5896,#5900,
    #5904,#5908,#5912,#5916));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#84,#112,#140,#168));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(32.5,64.5,10.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(32.5,64.5,40.));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(32.5,64.5,0.));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(32.5,43.749285708455,0.));
#35 = DIRECTION('',(-1.,0.,0.));
#36 = DIRECTION('',(0.,1.,0.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(20.750714291545,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.,-1.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(32.5,64.5,0.));
#47 = DIRECTION('',(-0.,-1.,-0.));
#48 = DIRECTION('',(-1.,0.,0.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(0.,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#22,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(32.5,54.5,-2.E-15));
#59 = SURFACE_CURVE('',#60,(#65,#72),.PCURVE_S1.);
#60 = CIRCLE('',#61,10.);
#61 = AXIS2_PLACEMENT_3D('',#62,#63,#64);
#62 = CARTESIAN_POINT('',(32.5,54.5,10.));
#63 = DIRECTION('',(-1.,0.,0.));
#64 = DIRECTION('',(0.,0.,-1.));
#65 = PCURVE('',#32,#66);
#66 = DEFINITIONAL_REPRESENTATION('',(#67),#71);
#67 = CIRCLE('',#68,10.);
#68 = AXIS2_PLACEMENT_2D('',#69,#70);
#69 = CARTESIAN_POINT('',(10.750714291545,-10.));
#70 = DIRECTION('',(0.,1.));
#71 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#72 = PCURVE('',#73,#78);
#73 = CYLINDRICAL_SURFACE('',#74,10.);
#74 = AXIS2_PLACEMENT_3D('',#75,#76,#77);
#75 = CARTESIAN_POINT('',(32.5,54.5,10.));
#76 = DIRECTION('',(-1.,0.,0.));
#77 = DIRECTION('',(0.,1.,0.));
#78 = DEFINITIONAL_REPRESENTATION('',(#79),#83);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(-4.712388980385,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(1.,0.));
#83 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#84 = ORIENTED_EDGE('',*,*,#85,.F.);
#85 = EDGE_CURVE('',#86,#57,#88,.T.);
#86 = VERTEX_POINT('',#87);
#87 = CARTESIAN_POINT('',(32.5,43.749285708455,0.));
#88 = SURFACE_CURVE('',#89,(#93,#100),.PCURVE_S1.);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(32.5,43.749285708455,0.));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.,1.,0.));
#93 = PCURVE('',#32,#94);
#94 = DEFINITIONAL_REPRESENTATION('',(#95),#99);
#95 = LINE('',#96,#97);
#96 = CARTESIAN_POINT('',(0.,0.));
#97 = VECTOR('',#98,1.);
#98 = DIRECTION('',(1.,0.));
#99 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#100 = PCURVE('',#101,#106);
#101 = PLANE('',#102);
#102 = AXIS2_PLACEMENT_3D('',#103,#104,#105);
#103 = CARTESIAN_POINT('',(1.457E-14,56.379060296882,0.));
#104 = DIRECTION('',(0.,0.,1.));
#105 = DIRECTION('',(1.,0.,0.));
#106 = DEFINITIONAL_REPRESENTATION('',(#107),#111);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(32.5,-12.62977458842));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(0.,1.));
#111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#112 = ORIENTED_EDGE('',*,*,#113,.T.);
#113 = EDGE_CURVE('',#86,#114,#116,.T.);
#114 = VERTEX_POINT('',#115);
#115 = CARTESIAN_POINT('',(32.5,43.749285708454,15.));
#116 = SURFACE_CURVE('',#117,(#121,#128),.PCURVE_S1.);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(32.5,43.749285708455,0.));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.,0.,1.));
#121 = PCURVE('',#32,#122);
#122 = DEFINITIONAL_REPRESENTATION('',(#123),#127);
#123 = LINE('',#124,#125);
#124 = CARTESIAN_POINT('',(0.,0.));
#125 = VECTOR('',#126,1.);
#126 = DIRECTION('',(0.,-1.));
#127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#128 = PCURVE('',#129,#134);
#129 = CYLINDRICAL_SURFACE('',#130,54.5);
#130 = AXIS2_PLACEMENT_3D('',#131,#132,#133);
#131 = CARTESIAN_POINT('',(0.,0.,0.));
#132 = DIRECTION('',(-0.,-0.,-1.));
#133 = DIRECTION('',(1.,0.,0.));
#134 = DEFINITIONAL_REPRESENTATION('',(#135),#139);
#135 = LINE('',#136,#137);
#136 = CARTESIAN_POINT('',(-0.931874526209,0.));
#137 = VECTOR('',#138,1.);
#138 = DIRECTION('',(-0.,-1.));
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#140 = ORIENTED_EDGE('',*,*,#141,.T.);
#141 = EDGE_CURVE('',#114,#142,#144,.T.);
#142 = VERTEX_POINT('',#143);
#143 = CARTESIAN_POINT('',(32.5,43.749285708455,40.));
#144 = SURFACE_CURVE('',#145,(#149,#156),.PCURVE_S1.);
#145 = LINE('',#146,#147);
#146 = CARTESIAN_POINT('',(32.5,43.749285708455,0.));
#147 = VECTOR('',#148,1.);
#148 = DIRECTION('',(0.,0.,1.));
#149 = PCURVE('',#32,#150);
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(0.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,-1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = PCURVE('',#157,#162);
#157 = CYLINDRICAL_SURFACE('',#158,54.5);
#158 = AXIS2_PLACEMENT_3D('',#159,#160,#161);
#159 = CARTESIAN_POINT('',(0.,0.,0.));
#160 = DIRECTION('',(0.,0.,-1.));
#161 = DIRECTION('',(1.,0.,0.));
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(5.35131078097,0.));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(0.,-1.));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = ORIENTED_EDGE('',*,*,#169,.T.);
#169 = EDGE_CURVE('',#142,#24,#170,.T.);
#170 = SURFACE_CURVE('',#171,(#175,#182),.PCURVE_S1.);
#171 = LINE('',#172,#173);
#172 = CARTESIAN_POINT('',(32.5,43.749285708455,40.));
#173 = VECTOR('',#174,1.);
#174 = DIRECTION('',(0.,1.,0.));
#175 = PCURVE('',#32,#176);
#176 = DEFINITIONAL_REPRESENTATION('',(#177),#181);
#177 = LINE('',#178,#179);
#178 = CARTESIAN_POINT('',(0.,-40.));
#179 = VECTOR('',#180,1.);
#180 = DIRECTION('',(1.,0.));
#181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#182 = PCURVE('',#183,#188);
#183 = CYLINDRICAL_SURFACE('',#184,30.);
#184 = AXIS2_PLACEMENT_3D('',#185,#186,#187);
#185 = CARTESIAN_POINT('',(2.5,43.749285708455,40.));
#186 = DIRECTION('',(0.,1.,0.));
#187 = DIRECTION('',(1.,0.,0.));
#188 = DEFINITIONAL_REPRESENTATION('',(#189),#193);
#189 = LINE('',#190,#191);
#190 = CARTESIAN_POINT('',(-0.,0.));
#191 = VECTOR('',#192,1.);
#192 = DIRECTION('',(-0.,1.));
#193 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#194 = ADVANCED_FACE('',(#195,#328,#359,#394,#425),#44,.F.);
#195 = FACE_BOUND('',#196,.F.);
#196 = EDGE_LOOP('',(#197,#198,#222,#250,#279,#307));
#197 = ORIENTED_EDGE('',*,*,#21,.T.);
#198 = ORIENTED_EDGE('',*,*,#199,.T.);
#199 = EDGE_CURVE('',#24,#200,#202,.T.);
#200 = VERTEX_POINT('',#201);
#201 = CARTESIAN_POINT('',(2.5,64.5,70.));
#202 = SURFACE_CURVE('',#203,(#208,#215),.PCURVE_S1.);
#203 = CIRCLE('',#204,30.);
#204 = AXIS2_PLACEMENT_3D('',#205,#206,#207);
#205 = CARTESIAN_POINT('',(2.5,64.5,40.));
#206 = DIRECTION('',(0.,-1.,0.));
#207 = DIRECTION('',(0.,0.,1.));
#208 = PCURVE('',#44,#209);
#209 = DEFINITIONAL_REPRESENTATION('',(#210),#214);
#210 = CIRCLE('',#211,30.);
#211 = AXIS2_PLACEMENT_2D('',#212,#213);
#212 = CARTESIAN_POINT('',(30.,-40.));
#213 = DIRECTION('',(0.,-1.));
#214 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#215 = PCURVE('',#183,#216);
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = LINE('',#218,#219);
#218 = CARTESIAN_POINT('',(4.712388980385,20.750714291545));
#219 = VECTOR('',#220,1.);
#220 = DIRECTION('',(-1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.T.);
#223 = EDGE_CURVE('',#200,#224,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(-2.5,64.5,70.));
#226 = SURFACE_CURVE('',#227,(#231,#238),.PCURVE_S1.);
#227 = LINE('',#228,#229);
#228 = CARTESIAN_POINT('',(32.5,64.5,70.));
#229 = VECTOR('',#230,1.);
#230 = DIRECTION('',(-1.,0.,0.));
#231 = PCURVE('',#44,#232);
#232 = DEFINITIONAL_REPRESENTATION('',(#233),#237);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(0.,-70.));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(1.,0.));
#237 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#238 = PCURVE('',#239,#244);
#239 = PLANE('',#240);
#240 = AXIS2_PLACEMENT_3D('',#241,#242,#243);
#241 = CARTESIAN_POINT('',(1.457E-14,56.379060296882,70.));
#242 = DIRECTION('',(0.,0.,1.));
#243 = DIRECTION('',(1.,0.,0.));
#244 = DEFINITIONAL_REPRESENTATION('',(#245),#249);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(32.5,8.120939703118));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(-1.,0.));
#249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#250 = ORIENTED_EDGE('',*,*,#251,.T.);
#251 = EDGE_CURVE('',#224,#252,#254,.T.);
#252 = VERTEX_POINT('',#253);
#253 = CARTESIAN_POINT('',(-32.5,64.5,40.));
#254 = SURFACE_CURVE('',#255,(#260,#267),.PCURVE_S1.);
#255 = CIRCLE('',#256,30.);
#256 = AXIS2_PLACEMENT_3D('',#257,#258,#259);
#257 = CARTESIAN_POINT('',(-2.5,64.5,40.));
#258 = DIRECTION('',(0.,-1.,0.));
#259 = DIRECTION('',(0.,0.,-1.));
#260 = PCURVE('',#44,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = CIRCLE('',#263,30.);
#263 = AXIS2_PLACEMENT_2D('',#264,#265);
#264 = CARTESIAN_POINT('',(35.,-40.));
#265 = DIRECTION('',(0.,1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#268,#273);
#268 = CYLINDRICAL_SURFACE('',#269,30.);
#269 = AXIS2_PLACEMENT_3D('',#270,#271,#272);
#270 = CARTESIAN_POINT('',(-2.5,64.5,40.));
#271 = DIRECTION('',(0.,-1.,0.));
#272 = DIRECTION('',(0.,0.,1.));
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#278);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(-3.14159265359,0.));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(1.,0.));
#278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#279 = ORIENTED_EDGE('',*,*,#280,.F.);
#280 = EDGE_CURVE('',#281,#252,#283,.T.);
#281 = VERTEX_POINT('',#282);
#282 = CARTESIAN_POINT('',(-32.5,64.5,10.));
#283 = SURFACE_CURVE('',#284,(#288,#295),.PCURVE_S1.);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(-32.5,64.5,0.));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(0.,0.,1.));
#288 = PCURVE('',#44,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(65.,0.));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = PCURVE('',#296,#301);
#296 = PLANE('',#297);
#297 = AXIS2_PLACEMENT_3D('',#298,#299,#300);
#298 = CARTESIAN_POINT('',(-32.5,64.5,0.));
#299 = DIRECTION('',(1.,0.,0.));
#300 = DIRECTION('',(0.,-1.,0.));
#301 = DEFINITIONAL_REPRESENTATION('',(#302),#306);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(0.,0.));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(0.,-1.));
#306 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#307 = ORIENTED_EDGE('',*,*,#308,.F.);
#308 = EDGE_CURVE('',#22,#281,#309,.T.);
#309 = SURFACE_CURVE('',#310,(#314,#321),.PCURVE_S1.);
#310 = LINE('',#311,#312);
#311 = CARTESIAN_POINT('',(32.5,64.5,10.));
#312 = VECTOR('',#313,1.);
#313 = DIRECTION('',(-1.,0.,0.));
#314 = PCURVE('',#44,#315);
#315 = DEFINITIONAL_REPRESENTATION('',(#316),#320);
#316 = LINE('',#317,#318);
#317 = CARTESIAN_POINT('',(0.,-10.));
#318 = VECTOR('',#319,1.);
#319 = DIRECTION('',(1.,0.));
#320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#321 = PCURVE('',#73,#322);
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#327);
#323 = LINE('',#324,#325);
#324 = CARTESIAN_POINT('',(0.,0.));
#325 = VECTOR('',#326,1.);
#326 = DIRECTION('',(0.,1.));
#327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#328 = FACE_BOUND('',#329,.F.);
#329 = EDGE_LOOP('',(#330));
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#332,#332,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(18.845508075689,64.5,31.));
#334 = SURFACE_CURVE('',#335,(#340,#347),.PCURVE_S1.);
#335 = CIRCLE('',#336,1.525);
#336 = AXIS2_PLACEMENT_3D('',#337,#338,#339);
#337 = CARTESIAN_POINT('',(17.320508075689,64.5,31.));
#338 = DIRECTION('',(0.,-1.,-2.2E-16));
#339 = DIRECTION('',(1.,0.,0.));
#340 = PCURVE('',#44,#341);
#341 = DEFINITIONAL_REPRESENTATION('',(#342),#346);
#342 = CIRCLE('',#343,1.525);
#343 = AXIS2_PLACEMENT_2D('',#344,#345);
#344 = CARTESIAN_POINT('',(15.179491924311,-31.));
#345 = DIRECTION('',(-1.,0.));
#346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#347 = PCURVE('',#348,#353);
#348 = CYLINDRICAL_SURFACE('',#349,1.525);
#349 = AXIS2_PLACEMENT_3D('',#350,#351,#352);
#350 = CARTESIAN_POINT('',(17.320508075689,-6.88E-15,31.));
#351 = DIRECTION('',(0.,-1.,-2.2E-16));
#352 = DIRECTION('',(1.,0.,0.));
#353 = DEFINITIONAL_REPRESENTATION('',(#354),#358);
#354 = LINE('',#355,#356);
#355 = CARTESIAN_POINT('',(0.,-64.5));
#356 = VECTOR('',#357,1.);
#357 = DIRECTION('',(1.,0.));
#358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#359 = FACE_BOUND('',#360,.F.);
#360 = EDGE_LOOP('',(#361));
#361 = ORIENTED_EDGE('',*,*,#362,.T.);
#362 = EDGE_CURVE('',#363,#363,#365,.T.);
#363 = VERTEX_POINT('',#364);
#364 = CARTESIAN_POINT('',(-11.5,64.5,41.));
#365 = SURFACE_CURVE('',#366,(#371,#382),.PCURVE_S1.);
#366 = CIRCLE('',#367,11.5);
#367 = AXIS2_PLACEMENT_3D('',#368,#369,#370);
#368 = CARTESIAN_POINT('',(0.,64.5,41.));
#369 = DIRECTION('',(0.,1.,-0.));
#370 = DIRECTION('',(-1.,0.,0.));
#371 = PCURVE('',#44,#372);
#372 = DEFINITIONAL_REPRESENTATION('',(#373),#381);
#373 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#374,#375,#376,#377,#378,#379
,#380),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#374 = CARTESIAN_POINT('',(44.,-41.));
#375 = CARTESIAN_POINT('',(44.,-60.91858428704));
#376 = CARTESIAN_POINT('',(26.75,-50.95929214352));
#377 = CARTESIAN_POINT('',(9.5,-41.));
#378 = CARTESIAN_POINT('',(26.75,-31.04070785647));
#379 = CARTESIAN_POINT('',(44.,-21.08141571295));
#380 = CARTESIAN_POINT('',(44.,-41.));
#381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#382 = PCURVE('',#383,#388);
#383 = CYLINDRICAL_SURFACE('',#384,11.5);
#384 = AXIS2_PLACEMENT_3D('',#385,#386,#387);
#385 = CARTESIAN_POINT('',(0.,64.5,41.));
#386 = DIRECTION('',(0.,1.,-2.2E-16));
#387 = DIRECTION('',(-1.,0.,0.));
#388 = DEFINITIONAL_REPRESENTATION('',(#389),#393);
#389 = LINE('',#390,#391);
#390 = CARTESIAN_POINT('',(0.,0.));
#391 = VECTOR('',#392,1.);
#392 = DIRECTION('',(1.,0.));
#393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#394 = FACE_BOUND('',#395,.F.);
#395 = EDGE_LOOP('',(#396));
#396 = ORIENTED_EDGE('',*,*,#397,.F.);
#397 = EDGE_CURVE('',#398,#398,#400,.T.);
#398 = VERTEX_POINT('',#399);
#399 = CARTESIAN_POINT('',(1.525,64.5,61.));
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.525);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(0.,64.5,61.));
#404 = DIRECTION('',(0.,-1.,-2.2E-16));
#405 = DIRECTION('',(1.,0.,0.));
#406 = PCURVE('',#44,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = CIRCLE('',#409,1.525);
#409 = AXIS2_PLACEMENT_2D('',#410,#411);
#410 = CARTESIAN_POINT('',(32.5,-61.));
#411 = DIRECTION('',(-1.,0.));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#414,#419);
#414 = CYLINDRICAL_SURFACE('',#415,1.525);
#415 = AXIS2_PLACEMENT_3D('',#416,#417,#418);
#416 = CARTESIAN_POINT('',(0.,-1.354E-14,61.));
#417 = DIRECTION('',(0.,-1.,-2.2E-16));
#418 = DIRECTION('',(1.,0.,0.));
#419 = DEFINITIONAL_REPRESENTATION('',(#420),#424);
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(0.,-64.5));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(1.,0.));
#424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#425 = FACE_BOUND('',#426,.F.);
#426 = EDGE_LOOP('',(#427));
#427 = ORIENTED_EDGE('',*,*,#428,.F.);
#428 = EDGE_CURVE('',#429,#429,#431,.T.);
#429 = VERTEX_POINT('',#430);
#430 = CARTESIAN_POINT('',(-15.79550807568,64.5,31.));
#431 = SURFACE_CURVE('',#432,(#437,#444),.PCURVE_S1.);
#432 = CIRCLE('',#433,1.525);
#433 = AXIS2_PLACEMENT_3D('',#434,#435,#436);
#434 = CARTESIAN_POINT('',(-17.32050807568,64.5,31.));
#435 = DIRECTION('',(0.,-1.,-2.2E-16));
#436 = DIRECTION('',(1.,0.,0.));
#437 = PCURVE('',#44,#438);
#438 = DEFINITIONAL_REPRESENTATION('',(#439),#443);
#439 = CIRCLE('',#440,1.525);
#440 = AXIS2_PLACEMENT_2D('',#441,#442);
#441 = CARTESIAN_POINT('',(49.820508075689,-31.));
#442 = DIRECTION('',(-1.,0.));
#443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#444 = PCURVE('',#445,#450);
#445 = CYLINDRICAL_SURFACE('',#446,1.525);
#446 = AXIS2_PLACEMENT_3D('',#447,#448,#449);
#447 = CARTESIAN_POINT('',(-17.32050807568,-6.88E-15,31.));
#448 = DIRECTION('',(0.,-1.,-2.2E-16));
#449 = DIRECTION('',(1.,0.,0.));
#450 = DEFINITIONAL_REPRESENTATION('',(#451),#455);
#451 = LINE('',#452,#453);
#452 = CARTESIAN_POINT('',(0.,-64.5));
#453 = VECTOR('',#454,1.);
#454 = DIRECTION('',(1.,0.));
#455 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#456 = ADVANCED_FACE('',(#457),#73,.T.);
#457 = FACE_BOUND('',#458,.T.);
#458 = EDGE_LOOP('',(#459,#460,#483,#511,#537));
#459 = ORIENTED_EDGE('',*,*,#56,.T.);
#460 = ORIENTED_EDGE('',*,*,#461,.T.);
#461 = EDGE_CURVE('',#57,#462,#464,.T.);
#462 = VERTEX_POINT('',#463);
#463 = CARTESIAN_POINT('',(-9.E-15,54.5,-1.E-15));
#464 = SURFACE_CURVE('',#465,(#469,#476),.PCURVE_S1.);
#465 = LINE('',#466,#467);
#466 = CARTESIAN_POINT('',(32.5,54.5,-1.78E-15));
#467 = VECTOR('',#468,1.);
#468 = DIRECTION('',(-1.,0.,0.));
#469 = PCURVE('',#73,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#475);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(1.570796326795,0.));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(0.,1.));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#101,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#482);
#478 = LINE('',#479,#480);
#479 = CARTESIAN_POINT('',(32.5,-1.879060296882));
#480 = VECTOR('',#481,1.);
#481 = DIRECTION('',(-1.,0.));
#482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#483 = ORIENTED_EDGE('',*,*,#484,.T.);
#484 = EDGE_CURVE('',#462,#485,#487,.T.);
#485 = VERTEX_POINT('',#486);
#486 = CARTESIAN_POINT('',(-32.5,54.5,-2.E-15));
#487 = SURFACE_CURVE('',#488,(#492,#499),.PCURVE_S1.);
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(32.5,54.5,-1.78E-15));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(-1.,0.,0.));
#492 = PCURVE('',#73,#493);
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#498);
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(1.570796326795,0.));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(0.,1.));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = PCURVE('',#500,#505);
#500 = PLANE('',#501);
#501 = AXIS2_PLACEMENT_3D('',#502,#503,#504);
#502 = CARTESIAN_POINT('',(1.457E-14,56.379060296882,0.));
#503 = DIRECTION('',(0.,0.,1.));
#504 = DIRECTION('',(1.,0.,0.));
#505 = DEFINITIONAL_REPRESENTATION('',(#506),#510);
#506 = LINE('',#507,#508);
#507 = CARTESIAN_POINT('',(32.5,-1.879060296882));
#508 = VECTOR('',#509,1.);
#509 = DIRECTION('',(-1.,0.));
#510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#511 = ORIENTED_EDGE('',*,*,#512,.F.);
#512 = EDGE_CURVE('',#281,#485,#513,.T.);
#513 = SURFACE_CURVE('',#514,(#519,#526),.PCURVE_S1.);
#514 = CIRCLE('',#515,10.);
#515 = AXIS2_PLACEMENT_3D('',#516,#517,#518);
#516 = CARTESIAN_POINT('',(-32.5,54.5,10.));
#517 = DIRECTION('',(-1.,0.,0.));
#518 = DIRECTION('',(0.,0.,-1.));
#519 = PCURVE('',#73,#520);
#520 = DEFINITIONAL_REPRESENTATION('',(#521),#525);
#521 = LINE('',#522,#523);
#522 = CARTESIAN_POINT('',(-4.712388980385,65.));
#523 = VECTOR('',#524,1.);
#524 = DIRECTION('',(1.,0.));
#525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#526 = PCURVE('',#296,#527);
#527 = DEFINITIONAL_REPRESENTATION('',(#528),#536);
#528 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#529,#530,#531,#532,#533,#534
,#535),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#529 = CARTESIAN_POINT('',(10.,1.7763568394E-15));
#530 = CARTESIAN_POINT('',(27.320508075689,1.7763568394E-15));
#531 = CARTESIAN_POINT('',(18.660254037844,-15.));
#532 = CARTESIAN_POINT('',(10.,-30.));
#533 = CARTESIAN_POINT('',(1.339745962156,-15.));
#534 = CARTESIAN_POINT('',(-7.320508075689,-1.42108547152E-14));
#535 = CARTESIAN_POINT('',(10.,1.7763568394E-15));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = ORIENTED_EDGE('',*,*,#308,.F.);
#538 = ADVANCED_FACE('',(#539),#101,.F.);
#539 = FACE_BOUND('',#540,.F.);
#540 = EDGE_LOOP('',(#541,#542,#577));
#541 = ORIENTED_EDGE('',*,*,#461,.T.);
#542 = ORIENTED_EDGE('',*,*,#543,.T.);
#543 = EDGE_CURVE('',#462,#86,#544,.T.);
#544 = SURFACE_CURVE('',#545,(#550,#561),.PCURVE_S1.);
#545 = CIRCLE('',#546,54.5);
#546 = AXIS2_PLACEMENT_3D('',#547,#548,#549);
#547 = CARTESIAN_POINT('',(0.,0.,0.));
#548 = DIRECTION('',(0.,0.,-1.));
#549 = DIRECTION('',(1.,0.,0.));
#550 = PCURVE('',#101,#551);
#551 = DEFINITIONAL_REPRESENTATION('',(#552),#560);
#552 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#553,#554,#555,#556,#557,#558
,#559),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#553 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#554 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#555 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#556 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#557 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#558 = CARTESIAN_POINT('',(54.5,38.017708715621));
#559 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = PCURVE('',#562,#567);
#562 = PLANE('',#563);
#563 = AXIS2_PLACEMENT_3D('',#564,#565,#566);
#564 = CARTESIAN_POINT('',(-3.21E-15,4.2E-15,0.));
#565 = DIRECTION('',(0.,0.,1.));
#566 = DIRECTION('',(1.,0.,0.));
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#576);
#568 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#569,#570,#571,#572,#573,#574
,#575),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#569 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#570 = CARTESIAN_POINT('',(54.5,-94.3967690125));
#571 = CARTESIAN_POINT('',(-27.25,-47.19838450625));
#572 = CARTESIAN_POINT('',(-109.,-1.754865011071E-14));
#573 = CARTESIAN_POINT('',(-27.25,47.198384506252));
#574 = CARTESIAN_POINT('',(54.5,94.396769012504));
#575 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#577 = ORIENTED_EDGE('',*,*,#85,.T.);
#578 = ADVANCED_FACE('',(#579),#129,.T.);
#579 = FACE_BOUND('',#580,.F.);
#580 = EDGE_LOOP('',(#581,#605,#633,#660));
#581 = ORIENTED_EDGE('',*,*,#582,.F.);
#582 = EDGE_CURVE('',#583,#86,#585,.T.);
#583 = VERTEX_POINT('',#584);
#584 = CARTESIAN_POINT('',(51.,19.215878850576,0.));
#585 = SURFACE_CURVE('',#586,(#591,#598),.PCURVE_S1.);
#586 = CIRCLE('',#587,54.5);
#587 = AXIS2_PLACEMENT_3D('',#588,#589,#590);
#588 = CARTESIAN_POINT('',(0.,0.,0.));
#589 = DIRECTION('',(0.,0.,1.));
#590 = DIRECTION('',(1.,0.,0.));
#591 = PCURVE('',#129,#592);
#592 = DEFINITIONAL_REPRESENTATION('',(#593),#597);
#593 = LINE('',#594,#595);
#594 = CARTESIAN_POINT('',(-0.,0.));
#595 = VECTOR('',#596,1.);
#596 = DIRECTION('',(-1.,0.));
#597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#598 = PCURVE('',#562,#599);
#599 = DEFINITIONAL_REPRESENTATION('',(#600),#604);
#600 = CIRCLE('',#601,54.5);
#601 = AXIS2_PLACEMENT_2D('',#602,#603);
#602 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#603 = DIRECTION('',(1.,0.));
#604 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#605 = ORIENTED_EDGE('',*,*,#606,.F.);
#606 = EDGE_CURVE('',#607,#583,#609,.T.);
#607 = VERTEX_POINT('',#608);
#608 = CARTESIAN_POINT('',(51.000000000001,19.215878850572,15.));
#609 = SURFACE_CURVE('',#610,(#614,#621),.PCURVE_S1.);
#610 = LINE('',#611,#612);
#611 = CARTESIAN_POINT('',(51.,19.215878850576,0.));
#612 = VECTOR('',#613,1.);
#613 = DIRECTION('',(-0.,-0.,-1.));
#614 = PCURVE('',#129,#615);
#615 = DEFINITIONAL_REPRESENTATION('',(#616),#620);
#616 = LINE('',#617,#618);
#617 = CARTESIAN_POINT('',(-0.360332001536,0.));
#618 = VECTOR('',#619,1.);
#619 = DIRECTION('',(-0.,1.));
#620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#621 = PCURVE('',#622,#627);
#622 = PLANE('',#623);
#623 = AXIS2_PLACEMENT_3D('',#624,#625,#626);
#624 = CARTESIAN_POINT('',(51.,-50.,15.));
#625 = DIRECTION('',(1.,0.,0.));
#626 = DIRECTION('',(0.,1.,0.));
#627 = DEFINITIONAL_REPRESENTATION('',(#628),#632);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(69.215878850576,-15.));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(0.,-1.));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = ORIENTED_EDGE('',*,*,#634,.T.);
#634 = EDGE_CURVE('',#607,#114,#635,.T.);
#635 = SURFACE_CURVE('',#636,(#641,#648),.PCURVE_S1.);
#636 = CIRCLE('',#637,54.5);
#637 = AXIS2_PLACEMENT_3D('',#638,#639,#640);
#638 = CARTESIAN_POINT('',(0.,0.,15.));
#639 = DIRECTION('',(0.,0.,1.));
#640 = DIRECTION('',(1.,0.,0.));
#641 = PCURVE('',#129,#642);
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(-0.,-15.));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(-1.,0.));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = PCURVE('',#649,#654);
#649 = PLANE('',#650);
#650 = AXIS2_PLACEMENT_3D('',#651,#652,#653);
#651 = CARTESIAN_POINT('',(-3.21E-15,4.2E-15,15.));
#652 = DIRECTION('',(0.,0.,1.));
#653 = DIRECTION('',(1.,0.,0.));
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#659);
#655 = CIRCLE('',#656,54.5);
#656 = AXIS2_PLACEMENT_2D('',#657,#658);
#657 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#658 = DIRECTION('',(1.,0.));
#659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#660 = ORIENTED_EDGE('',*,*,#113,.F.);
#661 = ADVANCED_FACE('',(#662,#1072,#1435,#1678,#1921),#157,.F.);
#662 = FACE_BOUND('',#663,.F.);
#663 = EDGE_LOOP('',(#664,#665,#689,#712,#879,#907));
#664 = ORIENTED_EDGE('',*,*,#141,.F.);
#665 = ORIENTED_EDGE('',*,*,#666,.T.);
#666 = EDGE_CURVE('',#114,#667,#669,.T.);
#667 = VERTEX_POINT('',#668);
#668 = CARTESIAN_POINT('',(-32.5,43.749285708455,15.));
#669 = SURFACE_CURVE('',#670,(#675,#682),.PCURVE_S1.);
#670 = CIRCLE('',#671,54.5);
#671 = AXIS2_PLACEMENT_3D('',#672,#673,#674);
#672 = CARTESIAN_POINT('',(0.,0.,15.));
#673 = DIRECTION('',(0.,0.,1.));
#674 = DIRECTION('',(1.,0.,0.));
#675 = PCURVE('',#157,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(6.28318530718,-15.));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(-1.,0.));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = PCURVE('',#649,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = CIRCLE('',#685,54.5);
#685 = AXIS2_PLACEMENT_2D('',#686,#687);
#686 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#687 = DIRECTION('',(1.,0.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = ORIENTED_EDGE('',*,*,#690,.T.);
#690 = EDGE_CURVE('',#667,#691,#693,.T.);
#691 = VERTEX_POINT('',#692);
#692 = CARTESIAN_POINT('',(-32.5,43.749285708455,40.));
#693 = SURFACE_CURVE('',#694,(#698,#705),.PCURVE_S1.);
#694 = LINE('',#695,#696);
#695 = CARTESIAN_POINT('',(-32.5,43.749285708455,0.));
#696 = VECTOR('',#697,1.);
#697 = DIRECTION('',(0.,0.,1.));
#698 = PCURVE('',#157,#699);
#699 = DEFINITIONAL_REPRESENTATION('',(#700),#704);
#700 = LINE('',#701,#702);
#701 = CARTESIAN_POINT('',(4.073467179799,0.));
#702 = VECTOR('',#703,1.);
#703 = DIRECTION('',(0.,-1.));
#704 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#705 = PCURVE('',#296,#706);
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#711);
#707 = LINE('',#708,#709);
#708 = CARTESIAN_POINT('',(20.750714291545,0.));
#709 = VECTOR('',#710,1.);
#710 = DIRECTION('',(0.,-1.));
#711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#712 = ORIENTED_EDGE('',*,*,#713,.F.);
#713 = EDGE_CURVE('',#714,#691,#716,.T.);
#714 = VERTEX_POINT('',#715);
#715 = CARTESIAN_POINT('',(-2.5,54.442630355265,70.));
#716 = SURFACE_CURVE('',#717,(#769,#824),.PCURVE_S1.);
#717 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#718,#719,#720,#721,#722,#723,
    #724,#725,#726,#727,#728,#729,#730,#731,#732,#733,#734,#735,#736,
    #737,#738,#739,#740,#741,#742,#743,#744,#745,#746,#747,#748,#749,
    #750,#751,#752,#753,#754,#755,#756,#757,#758,#759,#760,#761,#762,
    #763,#764,#765,#766,#767,#768),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#718 = CARTESIAN_POINT('',(-2.5,54.442630355265,70.));
#719 = CARTESIAN_POINT('',(-4.030660929134,54.372342562448,70.));
#720 = CARTESIAN_POINT('',(-5.557001109984,54.252966991495,
    69.910745745415));
#721 = CARTESIAN_POINT('',(-7.061308492277,54.085548970166,
    69.733119403804));
#722 = CARTESIAN_POINT('',(-8.530578302969,53.873865905042,
    69.47229489662));
#723 = CARTESIAN_POINT('',(-9.955307741387,53.62317088821,
    69.135341716942));
#724 = CARTESIAN_POINT('',(-11.32888259906,53.339345971167,
    68.730168330638));
#725 = CARTESIAN_POINT('',(-12.64711777331,53.028301102494,
    68.264718067734));
#726 = CARTESIAN_POINT('',(-14.54049072935,52.528584338047,
    67.486258331184));
#727 = CARTESIAN_POINT('',(-15.15901865798,52.35605325131,
    67.212681704278));
#728 = CARTESIAN_POINT('',(-15.76339422341,52.178578625138,
    66.926431468466));
#729 = CARTESIAN_POINT('',(-16.35360572471,51.996763435836,
    66.628274282646));
#730 = CARTESIAN_POINT('',(-16.92971056446,51.811178897908,
    66.318921578342));
#731 = CARTESIAN_POINT('',(-17.49182561213,51.622360176385,
    65.999022767967));
#732 = CARTESIAN_POINT('',(-18.04011907114,51.430803657293,
    65.669159043424));
#733 = CARTESIAN_POINT('',(-18.97425151859,51.092155052369,
    65.076340565811));
#734 = CARTESIAN_POINT('',(-19.36609077284,50.946075842001,
    64.817573365186));
#735 = CARTESIAN_POINT('',(-19.75040477387,50.798920908426,
    64.553752177765));
#736 = CARTESIAN_POINT('',(-20.12727864309,50.650875160368,
    64.28507860003));
#737 = CARTESIAN_POINT('',(-20.4967993623,50.502115765052,
    64.011739694144));
#738 = CARTESIAN_POINT('',(-20.8590556745,50.352812261259,
    63.733907871742));
#739 = CARTESIAN_POINT('',(-21.21413798467,50.203126672376,
    63.451740777716));
#740 = CARTESIAN_POINT('',(-21.82388569338,49.940456936537,
    62.949996600047));
#741 = CARTESIAN_POINT('',(-22.08162473526,49.827572483329,
    62.732241863336));
#742 = CARTESIAN_POINT('',(-22.33539342004,49.714626379478,
    62.512178445189));
#743 = CARTESIAN_POINT('',(-22.58522878304,49.601683252659,
    62.289864578195));
#744 = CARTESIAN_POINT('',(-22.831166861,49.48880623856,62.065355246206)
  );
#745 = CARTESIAN_POINT('',(-23.07324269206,49.376056980889,
    61.838702184339));
#746 = CARTESIAN_POINT('',(-23.31149031578,49.263495631372,
    61.609953878974));
#747 = CARTESIAN_POINT('',(-24.18004184364,48.847414792274,
    60.754939484556));
#748 = CARTESIAN_POINT('',(-24.78634408163,48.545470316416,
    60.115764259527));
#749 = CARTESIAN_POINT('',(-25.36555195311,48.246489605366,
    59.462487557475));
#750 = CARTESIAN_POINT('',(-25.91823216873,47.951580748165,
    58.795870883169));
#751 = CARTESIAN_POINT('',(-26.44484953777,47.661822523982,
    58.116591843667));
#752 = CARTESIAN_POINT('',(-26.94578937857,47.378267897387,
    57.425252118847));
#753 = CARTESIAN_POINT('',(-27.42136848529,47.101946224631,
    56.722381140143));
#754 = CARTESIAN_POINT('',(-28.43471008533,46.498884956433,
    55.11633190706));
#755 = CARTESIAN_POINT('',(-28.95831680645,46.176806186355,
    54.207031462939));
#756 = CARTESIAN_POINT('',(-29.44312870504,45.869600974068,
    53.281467669563));
#757 = CARTESIAN_POINT('',(-29.88935297581,45.579255505053,
    52.340594035556));
#758 = CARTESIAN_POINT('',(-30.29700155261,45.307745956593,
    51.385407925692));
#759 = CARTESIAN_POINT('',(-30.66594883901,45.057000776149,
    50.416975081429));
#760 = CARTESIAN_POINT('',(-30.99597573493,44.828848318441,
    49.436454790012));
#761 = CARTESIAN_POINT('',(-31.58818385939,44.413647916196,
    47.417801970103));
#762 = CARTESIAN_POINT('',(-31.84747921698,44.228385458529,
    46.378829835292));
#763 = CARTESIAN_POINT('',(-32.06430990485,44.071053362168,
    45.329828156404));
#764 = CARTESIAN_POINT('',(-32.23829162124,43.943322574764,
    44.272534310477));
#765 = CARTESIAN_POINT('',(-32.3690692971,43.846551136218,
    43.208816373613));
#766 = CARTESIAN_POINT('',(-32.45635980941,43.781704010172,
    42.140684763867));
#767 = CARTESIAN_POINT('',(-32.5,43.749285708455,41.070303346272));
#768 = CARTESIAN_POINT('',(-32.5,43.749285708455,40.));
#769 = PCURVE('',#157,#770);
#770 = DEFINITIONAL_REPRESENTATION('',(#771),#823);
#771 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#772,#773,#774,#775,#776,#777,
    #778,#779,#780,#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,
    #791,#792,#793,#794,#795,#796,#797,#798,#799,#800,#801,#802,#803,
    #804,#805,#806,#807,#808,#809,#810,#811,#812,#813,#814,#815,#816,
    #817,#818,#819,#820,#821,#822),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#772 = CARTESIAN_POINT('',(4.666501318344,-70.));
#773 = CARTESIAN_POINT('',(4.638386201217,-70.));
#774 = CARTESIAN_POINT('',(4.610308963863,-69.91074574541));
#775 = CARTESIAN_POINT('',(4.58256127389,-69.7331194038));
#776 = CARTESIAN_POINT('',(4.555351183766,-69.47229489662));
#777 = CARTESIAN_POINT('',(4.528831703011,-69.13534171694));
#778 = CARTESIAN_POINT('',(4.503113104397,-68.73016833063));
#779 = CARTESIAN_POINT('',(4.478269707071,-68.26471806773));
#780 = CARTESIAN_POINT('',(4.442339287751,-67.48625833118));
#781 = CARTESIAN_POINT('',(4.430558053857,-67.21268170427));
#782 = CARTESIAN_POINT('',(4.419002114107,-66.92643146846));
#783 = CARTESIAN_POINT('',(4.407672276975,-66.62827428264));
#784 = CARTESIAN_POINT('',(4.396568362091,-66.31892157834));
#785 = CARTESIAN_POINT('',(4.385689316432,-65.99902276796));
#786 = CARTESIAN_POINT('',(4.375033305211,-65.66915904342));
#787 = CARTESIAN_POINT('',(4.356801693849,-65.07634056581));
#788 = CARTESIAN_POINT('',(4.349128923035,-64.81757336518));
#789 = CARTESIAN_POINT('',(4.341578514307,-64.55375217776));
#790 = CARTESIAN_POINT('',(4.334149529238,-64.28507860003));
#791 = CARTESIAN_POINT('',(4.326841036624,-64.01173969414));
#792 = CARTESIAN_POINT('',(4.319652107431,-63.73390787174));
#793 = CARTESIAN_POINT('',(4.31258180974,-63.45174077771));
#794 = CARTESIAN_POINT('',(4.300399820521,-62.94999660004));
#795 = CARTESIAN_POINT('',(4.295237055849,-62.73224186333));
#796 = CARTESIAN_POINT('',(4.290140533724,-62.51217844518));
#797 = CARTESIAN_POINT('',(4.285109904533,-62.28986457819));
#798 = CARTESIAN_POINT('',(4.280144844993,-62.0653552462));
#799 = CARTESIAN_POINT('',(4.275245058153,-61.83870218433));
#800 = CARTESIAN_POINT('',(4.2704102734,-61.60995387897));
#801 = CARTESIAN_POINT('',(4.252739252829,-60.75493948455));
#802 = CARTESIAN_POINT('',(4.240312675742,-60.11576425952));
#803 = CARTESIAN_POINT('',(4.228354677077,-59.46248755747));
#804 = CARTESIAN_POINT('',(4.216862395042,-58.79587088316));
#805 = CARTESIAN_POINT('',(4.205835252749,-58.11659184366));
#806 = CARTESIAN_POINT('',(4.195274472552,-57.42525211884));
#807 = CARTESIAN_POINT('',(4.185182796164,-56.72238114014));
#808 = CARTESIAN_POINT('',(4.163545854221,-55.11633190706));
#809 = CARTESIAN_POINT('',(4.15226752564,-54.20703146293));
#810 = CARTESIAN_POINT('',(4.14173782768,-53.28146766956));
#811 = CARTESIAN_POINT('',(4.131970843424,-52.34059403555));
#812 = CARTESIAN_POINT('',(4.122984702534,-51.38540792569));
#813 = CARTESIAN_POINT('',(4.114800033086,-50.41697508142));
#814 = CARTESIAN_POINT('',(4.107438498859,-49.43645479001));
#815 = CARTESIAN_POINT('',(4.094167713647,-47.4178019701));
#816 = CARTESIAN_POINT('',(4.088320628189,-46.37882983529));
#817 = CARTESIAN_POINT('',(4.083405255123,-45.3298281564));
#818 = CARTESIAN_POINT('',(4.079445045127,-44.27253431047));
#819 = CARTESIAN_POINT('',(4.076459944281,-43.20881637361));
#820 = CARTESIAN_POINT('',(4.07446467729,-42.14068476386));
#821 = CARTESIAN_POINT('',(4.073467179799,-41.07030334627));
#822 = CARTESIAN_POINT('',(4.073467179799,-40.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = PCURVE('',#268,#825);
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#878);
#826 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#827,#828,#829,#830,#831,#832,
    #833,#834,#835,#836,#837,#838,#839,#840,#841,#842,#843,#844,#845,
    #846,#847,#848,#849,#850,#851,#852,#853,#854,#855,#856,#857,#858,
    #859,#860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870,#871,
    #872,#873,#874,#875,#876,#877),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#827 = CARTESIAN_POINT('',(0.,10.057369644735));
#828 = CARTESIAN_POINT('',(5.102203097114E-02,10.127657437552));
#829 = CARTESIAN_POINT('',(0.10190003412,10.247033008506));
#830 = CARTESIAN_POINT('',(0.152246027461,10.414451029829));
#831 = CARTESIAN_POINT('',(0.201824029114,10.626134094965));
#832 = CARTESIAN_POINT('',(0.250476345728,10.876829111785));
#833 = CARTESIAN_POINT('',(0.298101406377,11.160654028835));
#834 = CARTESIAN_POINT('',(0.344641426314,11.471698897506));
#835 = CARTESIAN_POINT('',(0.412880065679,11.971415661953));
#836 = CARTESIAN_POINT('',(0.435416538587,12.14394674869));
#837 = CARTESIAN_POINT('',(0.457695407563,12.321421374862));
#838 = CARTESIAN_POINT('',(0.479722902974,12.503236564164));
#839 = CARTESIAN_POINT('',(0.501506484055,12.688821102092));
#840 = CARTESIAN_POINT('',(0.523054810611,12.877639823614));
#841 = CARTESIAN_POINT('',(0.544377786891,13.069196342707));
#842 = CARTESIAN_POINT('',(0.58125652477,13.407844947631));
#843 = CARTESIAN_POINT('',(0.596906366042,13.553924157999));
#844 = CARTESIAN_POINT('',(0.612440658259,13.701079091574));
#845 = CARTESIAN_POINT('',(0.627863791904,13.849124839632));
#846 = CARTESIAN_POINT('',(0.643180129723,13.997884234948));
#847 = CARTESIAN_POINT('',(0.65839404531,14.147187738741));
#848 = CARTESIAN_POINT('',(0.673509961693,14.296873327624));
#849 = CARTESIAN_POINT('',(0.699831467224,14.559543063463));
#850 = CARTESIAN_POINT('',(0.71107757939,14.672427516671));
#851 = CARTESIAN_POINT('',(0.722272585838,14.785373620522));
#852 = CARTESIAN_POINT('',(0.733418318419,14.898316747341));
#853 = CARTESIAN_POINT('',(0.744516581402,15.01119376144));
#854 = CARTESIAN_POINT('',(0.755569151477,15.123943019111));
#855 = CARTESIAN_POINT('',(0.766577777754,15.236504368628));
#856 = CARTESIAN_POINT('',(0.80720386914,15.652585207726));
#857 = CARTESIAN_POINT('',(0.836553069178,15.954529683584));
#858 = CARTESIAN_POINT('',(0.865628145104,16.253510394634));
#859 = CARTESIAN_POINT('',(0.894461061752,16.548419251835));
#860 = CARTESIAN_POINT('',(0.923080817859,16.838177476018));
#861 = CARTESIAN_POINT('',(0.951514555006,17.121732102613));
#862 = CARTESIAN_POINT('',(0.979788343189,17.398053775369));
#863 = CARTESIAN_POINT('',(1.043088820083,18.001115043567));
#864 = CARTESIAN_POINT('',(1.078036321246,18.323193813645));
#865 = CARTESIAN_POINT('',(1.112818465381,18.630399025932));
#866 = CARTESIAN_POINT('',(1.14747498225,18.920744494947));
#867 = CARTESIAN_POINT('',(1.182039122671,19.192254043407));
#868 = CARTESIAN_POINT('',(1.216538985523,19.442999223851));
#869 = CARTESIAN_POINT('',(1.250998061565,19.671151681559));
#870 = CARTESIAN_POINT('',(1.32112231375,20.086352083804));
#871 = CARTESIAN_POINT('',(1.356787271832,20.271614541471));
#872 = CARTESIAN_POINT('',(1.392443830184,20.428946637832));
#873 = CARTESIAN_POINT('',(1.428101688253,20.556677425236));
#874 = CARTESIAN_POINT('',(1.463766579988,20.653448863782));
#875 = CARTESIAN_POINT('',(1.499440169945,20.718295989828));
#876 = CARTESIAN_POINT('',(1.535119548586,20.750714291545));
#877 = CARTESIAN_POINT('',(1.570796326795,20.750714291545));
#878 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#879 = ORIENTED_EDGE('',*,*,#880,.T.);
#880 = EDGE_CURVE('',#714,#881,#883,.T.);
#881 = VERTEX_POINT('',#882);
#882 = CARTESIAN_POINT('',(2.5,54.442630355265,70.));
#883 = SURFACE_CURVE('',#884,(#889,#896),.PCURVE_S1.);
#884 = CIRCLE('',#885,54.5);
#885 = AXIS2_PLACEMENT_3D('',#886,#887,#888);
#886 = CARTESIAN_POINT('',(0.,0.,70.));
#887 = DIRECTION('',(0.,0.,-1.));
#888 = DIRECTION('',(1.,0.,0.));
#889 = PCURVE('',#157,#890);
#890 = DEFINITIONAL_REPRESENTATION('',(#891),#895);
#891 = LINE('',#892,#893);
#892 = CARTESIAN_POINT('',(0.,-70.));
#893 = VECTOR('',#894,1.);
#894 = DIRECTION('',(1.,0.));
#895 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#896 = PCURVE('',#239,#897);
#897 = DEFINITIONAL_REPRESENTATION('',(#898),#906);
#898 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#899,#900,#901,#902,#903,#904
,#905),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#899 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#900 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#901 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#902 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#903 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#904 = CARTESIAN_POINT('',(54.5,38.017708715621));
#905 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#907 = ORIENTED_EDGE('',*,*,#908,.F.);
#908 = EDGE_CURVE('',#142,#881,#909,.T.);
#909 = SURFACE_CURVE('',#910,(#962,#1017),.PCURVE_S1.);
#910 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#911,#912,#913,#914,#915,#916,
    #917,#918,#919,#920,#921,#922,#923,#924,#925,#926,#927,#928,#929,
    #930,#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,#942,
    #943,#944,#945,#946,#947,#948,#949,#950,#951,#952,#953,#954,#955,
    #956,#957,#958,#959,#960,#961),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.203115675711,0.399114727677,0.555971708952,0.613968174207,
    0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#911 = CARTESIAN_POINT('',(32.5,43.749285708455,40.));
#912 = CARTESIAN_POINT('',(32.5,43.749285708455,41.070303346272));
#913 = CARTESIAN_POINT('',(32.456359809417,43.781704010172,
    42.140684763866));
#914 = CARTESIAN_POINT('',(32.369069297108,43.846551136218,
    43.208816373612));
#915 = CARTESIAN_POINT('',(32.238291621244,43.943322574764,
    44.272534310476));
#916 = CARTESIAN_POINT('',(32.064309904859,44.071053362167,
    45.329828156402));
#917 = CARTESIAN_POINT('',(31.847479216987,44.228385458529,
    46.378829835289));
#918 = CARTESIAN_POINT('',(31.588183859399,44.413647916195,
    47.417801970101));
#919 = CARTESIAN_POINT('',(30.995975734933,44.828848318441,
    49.43645479001));
#920 = CARTESIAN_POINT('',(30.665948839013,45.057000776149,
    50.416975081426));
#921 = CARTESIAN_POINT('',(30.297001552611,45.307745956592,
    51.385407925689));
#922 = CARTESIAN_POINT('',(29.88935297582,45.579255505052,
    52.340594035554));
#923 = CARTESIAN_POINT('',(29.443128705041,45.869600974067,
    53.28146766956));
#924 = CARTESIAN_POINT('',(28.958316806459,46.176806186354,
    54.207031462936));
#925 = CARTESIAN_POINT('',(28.434710085336,46.498884956432,
    55.116331907058));
#926 = CARTESIAN_POINT('',(27.421368485292,47.10194622463,56.72238114014
    ));
#927 = CARTESIAN_POINT('',(26.945789378574,47.378267897386,
    57.425252118845));
#928 = CARTESIAN_POINT('',(26.444849537776,47.661822523981,
    58.116591843665));
#929 = CARTESIAN_POINT('',(25.918232168734,47.951580748164,
    58.795870883167));
#930 = CARTESIAN_POINT('',(25.365551953118,48.246489605365,
    59.462487557473));
#931 = CARTESIAN_POINT('',(24.786344081639,48.545470316415,
    60.115764259524));
#932 = CARTESIAN_POINT('',(24.180041843647,48.847414792273,
    60.754939484554));
#933 = CARTESIAN_POINT('',(23.311490315783,49.263495631371,
    61.609953878972));
#934 = CARTESIAN_POINT('',(23.073242692068,49.376056980888,
    61.838702184337));
#935 = CARTESIAN_POINT('',(22.831166861009,49.488806238559,
    62.065355246204));
#936 = CARTESIAN_POINT('',(22.585228783049,49.601683252658,
    62.289864578193));
#937 = CARTESIAN_POINT('',(22.335393420043,49.714626379477,
    62.512178445187));
#938 = CARTESIAN_POINT('',(22.081624735262,49.827572483328,
    62.732241863334));
#939 = CARTESIAN_POINT('',(21.82388569339,49.940456936536,
    62.949996600045));
#940 = CARTESIAN_POINT('',(21.214137984677,50.203126672375,
    63.451740777714));
#941 = CARTESIAN_POINT('',(20.859055674512,50.352812261258,
    63.733907871741));
#942 = CARTESIAN_POINT('',(20.496799362309,50.502115765051,
    64.011739694142));
#943 = CARTESIAN_POINT('',(20.127278643099,50.650875160367,
    64.285078600028));
#944 = CARTESIAN_POINT('',(19.750404773881,50.798920908425,
    64.553752177764));
#945 = CARTESIAN_POINT('',(19.366090772846,50.946075842,64.817573365184)
  );
#946 = CARTESIAN_POINT('',(18.974251518598,51.092155052368,
    65.076340565809));
#947 = CARTESIAN_POINT('',(18.040119071149,51.430803657292,
    65.669159043423));
#948 = CARTESIAN_POINT('',(17.49182561214,51.622360176385,
    65.999022767965));
#949 = CARTESIAN_POINT('',(16.929710564467,51.811178897907,
    66.318921578341));
#950 = CARTESIAN_POINT('',(16.35360572472,51.996763435835,
    66.628274282645));
#951 = CARTESIAN_POINT('',(15.763394223414,52.178578625137,
    66.926431468465));
#952 = CARTESIAN_POINT('',(15.159018657988,52.356053251309,
    67.212681704276));
#953 = CARTESIAN_POINT('',(14.540490729356,52.528584338046,
    67.486258331183));
#954 = CARTESIAN_POINT('',(12.647117773316,53.028301102494,
    68.264718067734));
#955 = CARTESIAN_POINT('',(11.328882599073,53.339345971163,
    68.730168330636));
#956 = CARTESIAN_POINT('',(9.955307741368,53.623170888217,
    69.135341716939));
#957 = CARTESIAN_POINT('',(8.530578302995,53.873865905034,
    69.472294896628));
#958 = CARTESIAN_POINT('',(7.061308492262,54.085548970171,
    69.733119403796));
#959 = CARTESIAN_POINT('',(5.557001109992,54.252966991493,
    69.910745745418));
#960 = CARTESIAN_POINT('',(4.030660929134,54.372342562448,70.));
#961 = CARTESIAN_POINT('',(2.5,54.442630355265,70.));
#962 = PCURVE('',#157,#963);
#963 = DEFINITIONAL_REPRESENTATION('',(#964),#1016);
#964 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#965,#966,#967,#968,#969,#970,
    #971,#972,#973,#974,#975,#976,#977,#978,#979,#980,#981,#982,#983,
    #984,#985,#986,#987,#988,#989,#990,#991,#992,#993,#994,#995,#996,
    #997,#998,#999,#1000,#1001,#1002,#1003,#1004,#1005,#1006,#1007,#1008
    ,#1009,#1010,#1011,#1012,#1013,#1014,#1015),.UNSPECIFIED.,.F.,.F.,(9
    ,7,7,7,7,7,7,9),(0.,0.203115675711,0.399114727677,0.555971708952,
    0.613968174207,0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#965 = CARTESIAN_POINT('',(5.35131078097,-40.));
#966 = CARTESIAN_POINT('',(5.35131078097,-41.07030334627));
#967 = CARTESIAN_POINT('',(5.350313283479,-42.14068476386));
#968 = CARTESIAN_POINT('',(5.348318016488,-43.20881637361));
#969 = CARTESIAN_POINT('',(5.345332915642,-44.27253431047));
#970 = CARTESIAN_POINT('',(5.341372705646,-45.3298281564));
#971 = CARTESIAN_POINT('',(5.33645733258,-46.37882983528));
#972 = CARTESIAN_POINT('',(5.330610247123,-47.4178019701));
#973 = CARTESIAN_POINT('',(5.31733946191,-49.43645479001));
#974 = CARTESIAN_POINT('',(5.309977927683,-50.41697508142));
#975 = CARTESIAN_POINT('',(5.301793258235,-51.38540792568));
#976 = CARTESIAN_POINT('',(5.292807117345,-52.34059403555));
#977 = CARTESIAN_POINT('',(5.283040133089,-53.28146766956));
#978 = CARTESIAN_POINT('',(5.272510435129,-54.20703146293));
#979 = CARTESIAN_POINT('',(5.261232106549,-55.11633190705));
#980 = CARTESIAN_POINT('',(5.239595164605,-56.72238114014));
#981 = CARTESIAN_POINT('',(5.229503488218,-57.42525211884));
#982 = CARTESIAN_POINT('',(5.21894270802,-58.11659184366));
#983 = CARTESIAN_POINT('',(5.207915565727,-58.79587088316));
#984 = CARTESIAN_POINT('',(5.196423283693,-59.46248755747));
#985 = CARTESIAN_POINT('',(5.184465285027,-60.11576425952));
#986 = CARTESIAN_POINT('',(5.17203870794,-60.75493948455));
#987 = CARTESIAN_POINT('',(5.15436768737,-61.60995387897));
#988 = CARTESIAN_POINT('',(5.149532902616,-61.83870218433));
#989 = CARTESIAN_POINT('',(5.144633115777,-62.0653552462));
#990 = CARTESIAN_POINT('',(5.139668056236,-62.28986457819));
#991 = CARTESIAN_POINT('',(5.134637427045,-62.51217844518));
#992 = CARTESIAN_POINT('',(5.129540904921,-62.73224186333));
#993 = CARTESIAN_POINT('',(5.124378140249,-62.94999660004));
#994 = CARTESIAN_POINT('',(5.112196151029,-63.45174077771));
#995 = CARTESIAN_POINT('',(5.105125853338,-63.73390787174));
#996 = CARTESIAN_POINT('',(5.097936924145,-64.01173969414));
#997 = CARTESIAN_POINT('',(5.090628431531,-64.28507860002));
#998 = CARTESIAN_POINT('',(5.083199446462,-64.55375217776));
#999 = CARTESIAN_POINT('',(5.075649037735,-64.81757336518));
#1000 = CARTESIAN_POINT('',(5.067976266921,-65.0763405658));
#1001 = CARTESIAN_POINT('',(5.049744655558,-65.66915904342));
#1002 = CARTESIAN_POINT('',(5.039088644337,-65.99902276796));
#1003 = CARTESIAN_POINT('',(5.028209598679,-66.31892157834));
#1004 = CARTESIAN_POINT('',(5.017105683795,-66.62827428264));
#1005 = CARTESIAN_POINT('',(5.005775846662,-66.92643146846));
#1006 = CARTESIAN_POINT('',(4.994219906912,-67.21268170427));
#1007 = CARTESIAN_POINT('',(4.982438673019,-67.48625833118));
#1008 = CARTESIAN_POINT('',(4.946508253699,-68.26471806773));
#1009 = CARTESIAN_POINT('',(4.921664856372,-68.73016833063));
#1010 = CARTESIAN_POINT('',(4.895946257758,-69.13534171694));
#1011 = CARTESIAN_POINT('',(4.869426777003,-69.47229489661));
#1012 = CARTESIAN_POINT('',(4.842216686879,-69.7331194038));
#1013 = CARTESIAN_POINT('',(4.814468996906,-69.91074574541));
#1014 = CARTESIAN_POINT('',(4.786391759552,-70.));
#1015 = CARTESIAN_POINT('',(4.758276642425,-70.));
#1016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1017 = PCURVE('',#183,#1018);
#1018 = DEFINITIONAL_REPRESENTATION('',(#1019),#1071);
#1019 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1020,#1021,#1022,#1023,#1024,
    #1025,#1026,#1027,#1028,#1029,#1030,#1031,#1032,#1033,#1034,#1035,
    #1036,#1037,#1038,#1039,#1040,#1041,#1042,#1043,#1044,#1045,#1046,
    #1047,#1048,#1049,#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,
    #1058,#1059,#1060,#1061,#1062,#1063,#1064,#1065,#1066,#1067,#1068,
    #1069,#1070),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,9),(0.,
    0.203115675711,0.399114727677,0.555971708952,0.613968174207,
    0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#1020 = CARTESIAN_POINT('',(0.,0.));
#1021 = CARTESIAN_POINT('',(-3.567677820905E-02,-3.2E-16));
#1022 = CARTESIAN_POINT('',(-7.135615685022E-02,3.241830171733E-02));
#1023 = CARTESIAN_POINT('',(-0.107029746806,9.726542776283E-02));
#1024 = CARTESIAN_POINT('',(-0.142694638542,0.194036866309));
#1025 = CARTESIAN_POINT('',(-0.178352496611,0.321767653713));
#1026 = CARTESIAN_POINT('',(-0.214009054963,0.479099750074));
#1027 = CARTESIAN_POINT('',(-0.249674013045,0.664362207741));
#1028 = CARTESIAN_POINT('',(-0.319798265229,1.079562609986));
#1029 = CARTESIAN_POINT('',(-0.354257341271,1.307715067694));
#1030 = CARTESIAN_POINT('',(-0.388757204124,1.558460248137));
#1031 = CARTESIAN_POINT('',(-0.423321344545,1.829969796598));
#1032 = CARTESIAN_POINT('',(-0.457977861413,2.120315265612));
#1033 = CARTESIAN_POINT('',(-0.492760005549,2.427520477899));
#1034 = CARTESIAN_POINT('',(-0.527707506712,2.749599247977));
#1035 = CARTESIAN_POINT('',(-0.591007983606,3.352660516175));
#1036 = CARTESIAN_POINT('',(-0.619281771788,3.628982188931));
#1037 = CARTESIAN_POINT('',(-0.647715508936,3.912536815526));
#1038 = CARTESIAN_POINT('',(-0.676335265043,4.202295039709));
#1039 = CARTESIAN_POINT('',(-0.70516818169,4.49720389691));
#1040 = CARTESIAN_POINT('',(-0.734243257617,4.79618460796));
#1041 = CARTESIAN_POINT('',(-0.763592457655,5.098129083819));
#1042 = CARTESIAN_POINT('',(-0.804218549041,5.514209922916));
#1043 = CARTESIAN_POINT('',(-0.815227175318,5.626771272434));
#1044 = CARTESIAN_POINT('',(-0.826279745393,5.739520530104));
#1045 = CARTESIAN_POINT('',(-0.837378008376,5.852397544203));
#1046 = CARTESIAN_POINT('',(-0.848523740956,5.965340671023));
#1047 = CARTESIAN_POINT('',(-0.859718747405,6.078286774873));
#1048 = CARTESIAN_POINT('',(-0.87096485957,6.191171228081));
#1049 = CARTESIAN_POINT('',(-0.897286365102,6.45384096392));
#1050 = CARTESIAN_POINT('',(-0.912402281485,6.603526552803));
#1051 = CARTESIAN_POINT('',(-0.927616197072,6.752830056596));
#1052 = CARTESIAN_POINT('',(-0.942932534891,6.901589451912));
#1053 = CARTESIAN_POINT('',(-0.958355668536,7.04963519997));
#1054 = CARTESIAN_POINT('',(-0.973889960753,7.196790133545));
#1055 = CARTESIAN_POINT('',(-0.989539802025,7.342869343913));
#1056 = CARTESIAN_POINT('',(-1.026418539904,7.681517948838));
#1057 = CARTESIAN_POINT('',(-1.047741516184,7.87307446793));
#1058 = CARTESIAN_POINT('',(-1.069289842739,8.061893189453));
#1059 = CARTESIAN_POINT('',(-1.091073423821,8.24747772738));
#1060 = CARTESIAN_POINT('',(-1.113100919231,8.429292916682));
#1061 = CARTESIAN_POINT('',(-1.135379788208,8.606767542854));
#1062 = CARTESIAN_POINT('',(-1.157916261116,8.779298629591));
#1063 = CARTESIAN_POINT('',(-1.226154900481,9.279015394039));
#1064 = CARTESIAN_POINT('',(-1.272694920417,9.590060262708));
#1065 = CARTESIAN_POINT('',(-1.320319981067,9.873885179762));
#1066 = CARTESIAN_POINT('',(-1.368972297682,10.124580196579));
#1067 = CARTESIAN_POINT('',(-1.418550299333,10.336263261716));
#1068 = CARTESIAN_POINT('',(-1.468896292674,10.503681283039));
#1069 = CARTESIAN_POINT('',(-1.519774295824,10.623056853993));
#1070 = CARTESIAN_POINT('',(-1.570796326795,10.69334464681));
#1071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1072 = FACE_BOUND('',#1073,.F.);
#1073 = EDGE_LOOP('',(#1074));
#1074 = ORIENTED_EDGE('',*,*,#1075,.T.);
#1075 = EDGE_CURVE('',#1076,#1076,#1078,.T.);
#1076 = VERTEX_POINT('',#1077);
#1077 = CARTESIAN_POINT('',(12.5,53.047148839499,41.));
#1078 = SURFACE_CURVE('',#1079,(#1194,#1312),.PCURVE_S1.);
#1079 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1080,#1081,#1082,#1083,#1084,
    #1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093,#1094,#1095,
    #1096,#1097,#1098,#1099,#1100,#1101,#1102,#1103,#1104,#1105,#1106,
    #1107,#1108,#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,
    #1118,#1119,#1120,#1121,#1122,#1123,#1124,#1125,#1126,#1127,#1128,
    #1129,#1130,#1131,#1132,#1133,#1134,#1135,#1136,#1137,#1138,#1139,
    #1140,#1141,#1142,#1143,#1144,#1145,#1146,#1147,#1148,#1149,#1150,
    #1151,#1152,#1153,#1154,#1155,#1156,#1157,#1158,#1159,#1160,#1161,
    #1162,#1163,#1164,#1165,#1166,#1167,#1168,#1169,#1170,#1171,#1172,
    #1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,#1181,#1182,#1183,
    #1184,#1185,#1186,#1187,#1188,#1189,#1190,#1191,#1192,#1193),
  .UNSPECIFIED.,.T.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1080 = CARTESIAN_POINT('',(12.5,53.047148839499,41.));
#1081 = CARTESIAN_POINT('',(12.499999909589,53.047148860804,
    41.904114140039));
#1082 = CARTESIAN_POINT('',(12.425265160332,53.064759331422,
    42.807650674079));
#1083 = CARTESIAN_POINT('',(12.275957423428,53.0999420529,
    43.707065824205));
#1084 = CARTESIAN_POINT('',(12.051394838466,53.152235619718,
    44.598655100542));
#1085 = CARTESIAN_POINT('',(11.75014137157,53.220739970932,
    45.478298145331));
#1086 = CARTESIAN_POINT('',(11.370093454465,53.304031165636,
    46.341133058411));
#1087 = CARTESIAN_POINT('',(10.908574634951,53.400022842147,
    47.181148664497));
#1088 = CARTESIAN_POINT('',(10.195135818988,53.538194303771,
    48.238695277594));
#1089 = CARTESIAN_POINT('',(10.019855898132,53.571520356488,
    48.483884718713));
#1090 = CARTESIAN_POINT('',(9.83650434716,53.605687383827,
    48.726043723121));
#1091 = CARTESIAN_POINT('',(9.644975121383,53.640605539296,
    48.964942032543));
#1092 = CARTESIAN_POINT('',(9.445160948855,53.676176312596,
    49.200326897825));
#1093 = CARTESIAN_POINT('',(9.236953288338,53.712292117175,
    49.431923063419));
#1094 = CARTESIAN_POINT('',(9.020242287253,53.74883587778,
    49.659432751863));
#1095 = CARTESIAN_POINT('',(8.34397675117,53.859417337667,
    50.329027484635));
#1096 = CARTESIAN_POINT('',(7.857656986928,53.93450319675,
    50.758737191655));
#1097 = CARTESIAN_POINT('',(7.335538636076,54.009811703661,
    51.168268783632));
#1098 = CARTESIAN_POINT('',(6.777439745624,54.084037441303,
    51.553983239712));
#1099 = CARTESIAN_POINT('',(6.183441718545,54.155701495626,
    51.911983228553));
#1100 = CARTESIAN_POINT('',(5.553934595573,54.223174872901,
    52.238084753802));
#1101 = CARTESIAN_POINT('',(4.889681120648,54.284719905734,
    52.527775720381));
#1102 = CARTESIAN_POINT('',(3.229545799466,54.412789584026,
    53.118725548863));
#1103 = CARTESIAN_POINT('',(2.199228013877,54.472678224634,
    53.384215148611));
#1104 = CARTESIAN_POINT('',(1.121083616872,54.511925778123,
    53.552211970852));
#1105 = CARTESIAN_POINT('',(1.805267304125E-02,54.525866962114,
    53.610625188365));
#1106 = CARTESIAN_POINT('',(-1.085285401569,54.512681170804,
    53.555418985537));
#1107 = CARTESIAN_POINT('',(-2.164326438081,54.474119848101,
    53.390458988252));
#1108 = CARTESIAN_POINT('',(-3.196059927325,54.414798550425,
    53.127681408691));
#1109 = CARTESIAN_POINT('',(-4.885244290277,54.285477744666,
    52.531499847059));
#1110 = CARTESIAN_POINT('',(-5.574179247914,54.221614458625,
    52.231036236934));
#1111 = CARTESIAN_POINT('',(-6.225707543517,54.151387506907,
    51.891465203811));
#1112 = CARTESIAN_POINT('',(-6.839014087289,54.076733237373,
    51.517805324824));
#1113 = CARTESIAN_POINT('',(-7.413709152059,53.99945923602,
    51.114686539006));
#1114 = CARTESIAN_POINT('',(-7.94975049928,53.9211963351,50.686401019982
    ));
#1115 = CARTESIAN_POINT('',(-8.447388814502,53.843372765871,
    50.236937801062));
#1116 = CARTESIAN_POINT('',(-9.129113280445,53.730437525424,
    49.54455330358));
#1117 = CARTESIAN_POINT('',(-9.342482970575,53.694014382285,
    49.31480294644));
#1118 = CARTESIAN_POINT('',(-9.547358007748,53.658065463015,
    49.0810638975));
#1119 = CARTESIAN_POINT('',(-9.743849072063,53.62270539084,
    48.843627807166));
#1120 = CARTESIAN_POINT('',(-9.932065087349,53.588039401933,
    48.602764175395));
#1121 = CARTESIAN_POINT('',(-10.11211326979,53.554163771572,
    48.358720367014));
#1122 = CARTESIAN_POINT('',(-10.28409917658,53.521166240296,
    48.111721627029));
#1123 = CARTESIAN_POINT('',(-10.5842116175,53.462544740338,
    47.654766507357));
#1124 = CARTESIAN_POINT('',(-10.71481732761,53.43662251874,
    47.44566970031));
#1125 = CARTESIAN_POINT('',(-10.8399996858,53.41140593209,
    47.234801535187));
#1126 = CARTESIAN_POINT('',(-10.95981192223,53.386937713825,
    47.02227612743));
#1127 = CARTESIAN_POINT('',(-11.07430473845,53.363257328599,
    46.808200850911));
#1128 = CARTESIAN_POINT('',(-11.1835263495,53.340401126878,
    46.592676341297));
#1129 = CARTESIAN_POINT('',(-11.28752252611,53.318402499539,
    46.375796499406));
#1130 = CARTESIAN_POINT('',(-11.6679614625,53.237126216366,
    45.535916502908));
#1131 = CARTESIAN_POINT('',(-11.90752787011,53.184167345613,
    44.903807900781));
#1132 = CARTESIAN_POINT('',(-12.10593693146,53.139144461679,
    44.263511520529));
#1133 = CARTESIAN_POINT('',(-12.26392151342,53.10260591941,
    43.616937252549));
#1134 = CARTESIAN_POINT('',(-12.38204089582,53.074944615019,
    42.965782451233));
#1135 = CARTESIAN_POINT('',(-12.46067239602,53.056415994079,
    42.31159780962));
#1136 = CARTESIAN_POINT('',(-12.5,53.047148839499,41.655852702793));
#1137 = CARTESIAN_POINT('',(-12.5,53.047148839499,40.508195572538));
#1138 = CARTESIAN_POINT('',(-12.47788599844,53.052359771776,
    40.016416374048));
#1139 = CARTESIAN_POINT('',(-12.43366208205,53.062780665566,
    39.525289912022));
#1140 = CARTESIAN_POINT('',(-12.36727098336,53.078370540008,
    39.035448154778));
#1141 = CARTESIAN_POINT('',(-12.27859643154,53.099047843235,
    38.547541963904));
#1142 = CARTESIAN_POINT('',(-12.16746493692,53.124688624939,
    38.062255568575));
#1143 = CARTESIAN_POINT('',(-12.0336470093,53.155123442408,
    37.580321081748));
#1144 = CARTESIAN_POINT('',(-11.74550337689,53.219463212925,
    36.702253095002));
#1145 = CARTESIAN_POINT('',(-11.59803113899,53.25200325362,
    36.304898949897));
#1146 = CARTESIAN_POINT('',(-11.43426144843,53.287626677629,
    35.910893844407));
#1147 = CARTESIAN_POINT('',(-11.25397993223,53.32618445595,
    35.520702052818));
#1148 = CARTESIAN_POINT('',(-11.05694135291,53.367501452687,
    35.13483830325));
#1149 = CARTESIAN_POINT('',(-10.84287346924,53.411372902659,
    34.753877178705));
#1150 = CARTESIAN_POINT('',(-10.61148089689,53.457560889009,
    34.378462518103));
#1151 = CARTESIAN_POINT('',(-10.19513581898,53.538194303771,
    33.761304722406));
#1152 = CARTESIAN_POINT('',(-10.01985589813,53.571520356488,
    33.516115281286));
#1153 = CARTESIAN_POINT('',(-9.83650434716,53.605687383827,
    33.273956276879));
#1154 = CARTESIAN_POINT('',(-9.644975121383,53.640605539296,
    33.035057967457));
#1155 = CARTESIAN_POINT('',(-9.445160948855,53.676176312596,
    32.799673102175));
#1156 = CARTESIAN_POINT('',(-9.236953288338,53.712292117175,
    32.568076936581));
#1157 = CARTESIAN_POINT('',(-9.020242287253,53.74883587778,
    32.340567248137));
#1158 = CARTESIAN_POINT('',(-8.343976752064,53.859417337521,
    31.67097251625));
#1159 = CARTESIAN_POINT('',(-7.85765698198,53.934503197361,
    31.241262805037));
#1160 = CARTESIAN_POINT('',(-7.335538637945,54.009811703504,
    30.831731217056));
#1161 = CARTESIAN_POINT('',(-6.777439751745,54.084037440581,
    30.446016764093));
#1162 = CARTESIAN_POINT('',(-6.183441718079,54.15570149559,
    30.08801677178));
#1163 = CARTESIAN_POINT('',(-5.553934589019,54.22317487361,
    29.761915242552));
#1164 = CARTESIAN_POINT('',(-4.889681123242,54.284719905534,
    29.472224280542));
#1165 = CARTESIAN_POINT('',(-3.229545799467,54.412789584026,
    28.881274451137));
#1166 = CARTESIAN_POINT('',(-2.199228013874,54.472678224634,
    28.615784851389));
#1167 = CARTESIAN_POINT('',(-1.121083616877,54.511925778123,
    28.447788029149));
#1168 = CARTESIAN_POINT('',(-1.805267303461E-02,54.525866962114,
    28.389374811634));
#1169 = CARTESIAN_POINT('',(1.085285401565,54.512681170804,
    28.444581014464));
#1170 = CARTESIAN_POINT('',(2.164326438082,54.474119848101,
    28.609541011748));
#1171 = CARTESIAN_POINT('',(3.196059927325,54.414798550425,
    28.872318591309));
#1172 = CARTESIAN_POINT('',(4.885244289625,54.285477744716,
    29.468500152711));
#1173 = CARTESIAN_POINT('',(5.574179247621,54.221614458667,
    29.76896376286));
#1174 = CARTESIAN_POINT('',(6.225707543766,54.151387506892,
    30.108534796243));
#1175 = CARTESIAN_POINT('',(6.839014087775,54.076733237311,
    30.48219467548));
#1176 = CARTESIAN_POINT('',(7.413709152354,53.99945923597,
    30.885313461285));
#1177 = CARTESIAN_POINT('',(7.949750499142,53.921196335115,
    31.313598979975));
#1178 = CARTESIAN_POINT('',(8.447388814105,53.843372765937,
    31.763062198535));
#1179 = CARTESIAN_POINT('',(9.129113280445,53.730437525423,
    32.455446696421));
#1180 = CARTESIAN_POINT('',(9.342482970575,53.694014382285,
    32.68519705356));
#1181 = CARTESIAN_POINT('',(9.547358007748,53.658065463015,32.9189361025
    ));
#1182 = CARTESIAN_POINT('',(9.743849072063,53.62270539084,
    33.156372192834));
#1183 = CARTESIAN_POINT('',(9.93206508735,53.588039401933,
    33.397235824605));
#1184 = CARTESIAN_POINT('',(10.112113269795,53.554163771571,
    33.641279632986));
#1185 = CARTESIAN_POINT('',(10.28409917658,53.521166240296,
    33.888278372971));
#1186 = CARTESIAN_POINT('',(10.971922409692,53.386812497722,
    34.935567005313));
#1187 = CARTESIAN_POINT('',(11.414803441972,53.29421803983,
    35.761533598928));
#1188 = CARTESIAN_POINT('',(11.779677480905,53.214018859311,
    36.608874416303));
#1189 = CARTESIAN_POINT('',(12.069014334003,53.148131721559,
    37.472013379685));
#1190 = CARTESIAN_POINT('',(12.284749408084,53.09787031701,
    38.34640690342));
#1191 = CARTESIAN_POINT('',(12.428202065788,53.064067279817,
    39.228179183713));
#1192 = CARTESIAN_POINT('',(12.499999911383,53.047148860381,
    40.113829181122));
#1193 = CARTESIAN_POINT('',(12.5,53.047148839499,41.));
#1194 = PCURVE('',#157,#1195);
#1195 = DEFINITIONAL_REPRESENTATION('',(#1196),#1311);
#1196 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1197,#1198,#1199,#1200,#1201,
    #1202,#1203,#1204,#1205,#1206,#1207,#1208,#1209,#1210,#1211,#1212,
    #1213,#1214,#1215,#1216,#1217,#1218,#1219,#1220,#1221,#1222,#1223,
    #1224,#1225,#1226,#1227,#1228,#1229,#1230,#1231,#1232,#1233,#1234,
    #1235,#1236,#1237,#1238,#1239,#1240,#1241,#1242,#1243,#1244,#1245,
    #1246,#1247,#1248,#1249,#1250,#1251,#1252,#1253,#1254,#1255,#1256,
    #1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,#1265,#1266,#1267,
    #1268,#1269,#1270,#1271,#1272,#1273,#1274,#1275,#1276,#1277,#1278,
    #1279,#1280,#1281,#1282,#1283,#1284,#1285,#1286,#1287,#1288,#1289,
    #1290,#1291,#1292,#1293,#1294,#1295,#1296,#1297,#1298,#1299,#1300,
    #1301,#1302,#1303,#1304,#1305,#1306,#1307,#1308,#1309,#1310),
  .UNSPECIFIED.,.T.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1197 = CARTESIAN_POINT('',(4.943806821493,-41.));
#1198 = CARTESIAN_POINT('',(4.943806819789,-41.90411414003));
#1199 = CARTESIAN_POINT('',(4.942397984145,-42.80765067407));
#1200 = CARTESIAN_POINT('',(4.9395833581,-43.7070658242));
#1201 = CARTESIAN_POINT('',(4.935352718547,-44.59865510054));
#1202 = CARTESIAN_POINT('',(4.929684185426,-45.47829814533));
#1203 = CARTESIAN_POINT('',(4.922545840497,-46.34113305841));
#1204 = CARTESIAN_POINT('',(4.913897194623,-47.18114866449));
#1205 = CARTESIAN_POINT('',(4.900563332904,-48.23869527759));
#1206 = CARTESIAN_POINT('',(4.897289591734,-48.48388471871));
#1207 = CARTESIAN_POINT('',(4.893867468041,-48.72604372312));
#1208 = CARTESIAN_POINT('',(4.890295300515,-48.96494203254));
#1209 = CARTESIAN_POINT('',(4.886571413174,-49.20032689782));
#1210 = CARTESIAN_POINT('',(4.882694110505,-49.43192306341));
#1211 = CARTESIAN_POINT('',(4.878661672616,-49.65943275186));
#1212 = CARTESIAN_POINT('',(4.866088334726,-50.32902748463));
#1213 = CARTESIAN_POINT('',(4.857059665,-50.75873719165));
#1214 = CARTESIAN_POINT('',(4.847381183787,-51.16826878363));
#1215 = CARTESIAN_POINT('',(4.837052001822,-51.55398323971));
#1216 = CARTESIAN_POINT('',(4.826075700221,-51.91198322855));
#1217 = CARTESIAN_POINT('',(4.814460869112,-52.2380847538));
#1218 = CARTESIAN_POINT('',(4.802221982687,-52.52777572038));
#1219 = CARTESIAN_POINT('',(4.771670278015,-53.11872554886));
#1220 = CARTESIAN_POINT('',(4.752736851374,-53.38421514861));
#1221 = CARTESIAN_POINT('',(4.732949112584,-53.55221197085));
#1222 = CARTESIAN_POINT('',(4.712720055115,-53.61062518836));
#1223 = CARTESIAN_POINT('',(4.692485552211,-53.55541898553));
#1224 = CARTESIAN_POINT('',(4.672681858641,-53.39045898825));
#1225 = CARTESIAN_POINT('',(4.653723120563,-53.12768140869));
#1226 = CARTESIAN_POINT('',(4.62263821395,-52.53149984705));
#1227 = CARTESIAN_POINT('',(4.609944643147,-52.23103623693));
#1228 = CARTESIAN_POINT('',(4.597922885618,-51.89146520381));
#1229 = CARTESIAN_POINT('',(4.586588452459,-51.51780532482));
#1230 = CARTESIAN_POINT('',(4.575950143301,-51.114686539));
#1231 = CARTESIAN_POINT('',(4.566011114792,-50.68640101998));
#1232 = CARTESIAN_POINT('',(4.556769535788,-50.23693780106));
#1233 = CARTESIAN_POINT('',(4.544090349716,-49.54455330358));
#1234 = CARTESIAN_POINT('',(4.540118724581,-49.31480294644));
#1235 = CARTESIAN_POINT('',(4.536302184254,-49.0810638975));
#1236 = CARTESIAN_POINT('',(4.532638995402,-48.84362780716));
#1237 = CARTESIAN_POINT('',(4.52912745615,-48.60276417539));
#1238 = CARTESIAN_POINT('',(4.525765891267,-48.35872036701));
#1239 = CARTESIAN_POINT('',(4.522552647361,-48.11172162702));
#1240 = CARTESIAN_POINT('',(4.51694192868,-47.65476650735));
#1241 = CARTESIAN_POINT('',(4.514498759201,-47.44566970031));
#1242 = CARTESIAN_POINT('',(4.512155712174,-47.23480153518));
#1243 = CARTESIAN_POINT('',(4.509911962478,-47.02227612743));
#1244 = CARTESIAN_POINT('',(4.507766725825,-46.80820085091));
#1245 = CARTESIAN_POINT('',(4.505719257255,-46.59267634129));
#1246 = CARTESIAN_POINT('',(4.503768849636,-46.3757964994));
#1247 = CARTESIAN_POINT('',(4.496630795865,-45.5359165029));
#1248 = CARTESIAN_POINT('',(4.492129070333,-44.90380790078));
#1249 = CARTESIAN_POINT('',(4.488396060035,-44.26351152052));
#1250 = CARTESIAN_POINT('',(4.485420765852,-43.61693725254));
#1251 = CARTESIAN_POINT('',(4.483194803658,-42.96578245123));
#1252 = CARTESIAN_POINT('',(4.481712510625,-42.31159780962));
#1253 = CARTESIAN_POINT('',(4.480971139276,-41.65585270279));
#1254 = CARTESIAN_POINT('',(4.480971139276,-40.50819557253));
#1255 = CARTESIAN_POINT('',(4.481388013794,-40.01641637404));
#1256 = CARTESIAN_POINT('',(4.482221685532,-39.52528991202));
#1257 = CARTESIAN_POINT('',(4.483473005139,-39.03544815477));
#1258 = CARTESIAN_POINT('',(4.485143705869,-38.5475419639));
#1259 = CARTESIAN_POINT('',(4.487236373593,-38.06225556857));
#1260 = CARTESIAN_POINT('',(4.489754432193,-37.58032108174));
#1261 = CARTESIAN_POINT('',(4.495171670619,-36.702253095));
#1262 = CARTESIAN_POINT('',(4.497942662027,-36.30489894989));
#1263 = CARTESIAN_POINT('',(4.501017857363,-35.9108938444));
#1264 = CARTESIAN_POINT('',(4.504400542874,-35.52070205281));
#1265 = CARTESIAN_POINT('',(4.508094490554,-35.13483830325));
#1266 = CARTESIAN_POINT('',(4.512103896475,-34.7538771787));
#1267 = CARTESIAN_POINT('',(4.516433319122,-34.3784625181));
#1268 = CARTESIAN_POINT('',(4.524214627865,-33.7613047224));
#1269 = CARTESIAN_POINT('',(4.527488369035,-33.51611528128));
#1270 = CARTESIAN_POINT('',(4.530910492728,-33.27395627687));
#1271 = CARTESIAN_POINT('',(4.534482660254,-33.03505796745));
#1272 = CARTESIAN_POINT('',(4.538206547596,-32.79967310217));
#1273 = CARTESIAN_POINT('',(4.542083850265,-32.56807693658));
#1274 = CARTESIAN_POINT('',(4.546116288153,-32.34056724813));
#1275 = CARTESIAN_POINT('',(4.558689626027,-31.67097251625));
#1276 = CARTESIAN_POINT('',(4.567718295861,-31.24126280503));
#1277 = CARTESIAN_POINT('',(4.577396776948,-30.83173121705));
#1278 = CARTESIAN_POINT('',(4.587725958834,-30.44601676409));
#1279 = CARTESIAN_POINT('',(4.598702260557,-30.08801677178));
#1280 = CARTESIAN_POINT('',(4.610317091779,-29.76191524255));
#1281 = CARTESIAN_POINT('',(4.622555978034,-29.47222428054));
#1282 = CARTESIAN_POINT('',(4.653107682755,-28.88127445113));
#1283 = CARTESIAN_POINT('',(4.672041109395,-28.61578485138));
#1284 = CARTESIAN_POINT('',(4.691828848185,-28.44778802915));
#1285 = CARTESIAN_POINT('',(4.712057905654,-28.38937481163));
#1286 = CARTESIAN_POINT('',(4.732292408558,-28.44458101446));
#1287 = CARTESIAN_POINT('',(4.752096102128,-28.60954101174));
#1288 = CARTESIAN_POINT('',(4.771054840206,-28.8723185913));
#1289 = CARTESIAN_POINT('',(4.802139746808,-29.46850015271));
#1290 = CARTESIAN_POINT('',(4.814833317617,-29.76896376286));
#1291 = CARTESIAN_POINT('',(4.826855075156,-30.10853479624));
#1292 = CARTESIAN_POINT('',(4.83818950832,-30.48219467548));
#1293 = CARTESIAN_POINT('',(4.848827817474,-30.88531346128));
#1294 = CARTESIAN_POINT('',(4.858766845975,-31.31359897997));
#1295 = CARTESIAN_POINT('',(4.868008424974,-31.76306219853));
#1296 = CARTESIAN_POINT('',(4.880687611054,-32.45544669642));
#1297 = CARTESIAN_POINT('',(4.884659236188,-32.68519705356));
#1298 = CARTESIAN_POINT('',(4.888475776516,-32.9189361025));
#1299 = CARTESIAN_POINT('',(4.892138965367,-33.15637219283));
#1300 = CARTESIAN_POINT('',(4.89565050462,-33.3972358246));
#1301 = CARTESIAN_POINT('',(4.899012069502,-33.64127963298));
#1302 = CARTESIAN_POINT('',(4.902225313408,-33.88827837297));
#1303 = CARTESIAN_POINT('',(4.91508443598,-34.93556700531));
#1304 = CARTESIAN_POINT('',(4.923385676619,-35.76153359892));
#1305 = CARTESIAN_POINT('',(4.930239970826,-36.6088744163));
#1306 = CARTESIAN_POINT('',(4.935684664084,-37.47201337968));
#1307 = CARTESIAN_POINT('',(4.939749097492,-38.34640690342));
#1308 = CARTESIAN_POINT('',(4.942453348103,-39.22817918371));
#1309 = CARTESIAN_POINT('',(4.943806819823,-40.11382918112));
#1310 = CARTESIAN_POINT('',(4.943806821493,-41.));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = PCURVE('',#1313,#1318);
#1313 = CYLINDRICAL_SURFACE('',#1314,12.5);
#1314 = AXIS2_PLACEMENT_3D('',#1315,#1316,#1317);
#1315 = CARTESIAN_POINT('',(0.,-9.1E-15,41.));
#1316 = DIRECTION('',(0.,-1.,-2.2E-16));
#1317 = DIRECTION('',(1.,0.,0.));
#1318 = DEFINITIONAL_REPRESENTATION('',(#1319),#1434);
#1319 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1320,#1321,#1322,#1323,#1324,
    #1325,#1326,#1327,#1328,#1329,#1330,#1331,#1332,#1333,#1334,#1335,
    #1336,#1337,#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,#1346,
    #1347,#1348,#1349,#1350,#1351,#1352,#1353,#1354,#1355,#1356,#1357,
    #1358,#1359,#1360,#1361,#1362,#1363,#1364,#1365,#1366,#1367,#1368,
    #1369,#1370,#1371,#1372,#1373,#1374,#1375,#1376,#1377,#1378,#1379,
    #1380,#1381,#1382,#1383,#1384,#1385,#1386,#1387,#1388,#1389,#1390,
    #1391,#1392,#1393,#1394,#1395,#1396,#1397,#1398,#1399,#1400,#1401,
    #1402,#1403,#1404,#1405,#1406,#1407,#1408,#1409,#1410,#1411,#1412,
    #1413,#1414,#1415,#1416,#1417,#1418,#1419,#1420,#1421,#1422,#1423,
    #1424,#1425,#1426,#1427,#1428,#1429,#1430,#1431,#1432,#1433),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1320 = CARTESIAN_POINT('',(0.,-53.04714883949));
#1321 = CARTESIAN_POINT('',(7.232913120308E-02,-53.0471488608));
#1322 = CARTESIAN_POINT('',(0.144612022183,-53.06475933142));
#1323 = CARTESIAN_POINT('',(0.217141941226,-53.0999420529));
#1324 = CARTESIAN_POINT('',(0.29019565168,-53.15223561971));
#1325 = CARTESIAN_POINT('',(0.364053571052,-53.22073997093));
#1326 = CARTESIAN_POINT('',(0.439006395881,-53.30403116563));
#1327 = CARTESIAN_POINT('',(0.515364502387,-53.40002284214));
#1328 = CARTESIAN_POINT('',(0.617420166665,-53.53819430377));
#1329 = CARTESIAN_POINT('',(0.641523132334,-53.57152035648));
#1330 = CARTESIAN_POINT('',(0.665807153264,-53.60568738382));
#1331 = CARTESIAN_POINT('',(0.690283822909,-53.64060553929));
#1332 = CARTESIAN_POINT('',(0.714964752076,-53.67617631259));
#1333 = CARTESIAN_POINT('',(0.73986181619,-53.71229211717));
#1334 = CARTESIAN_POINT('',(0.76498740256,-53.74883587778));
#1335 = CARTESIAN_POINT('',(0.841121691559,-53.85941733766));
#1336 = CARTESIAN_POINT('',(0.892955428345,-53.93450319675));
#1337 = CARTESIAN_POINT('',(0.945887679011,-54.00981170366));
#1338 = CARTESIAN_POINT('',(0.999958889119,-54.0840374413));
#1339 = CARTESIAN_POINT('',(1.055218891319,-54.15570149562));
#1340 = CARTESIAN_POINT('',(1.111728762655,-54.2231748729));
#1341 = CARTESIAN_POINT('',(1.169563786647,-54.28471990573));
#1342 = CARTESIAN_POINT('',(1.3105380194,-54.41278958402));
#1343 = CARTESIAN_POINT('',(1.39531307727,-54.47267822463));
#1344 = CARTESIAN_POINT('',(1.481908390876,-54.51192577812));
#1345 = CARTESIAN_POINT('',(1.569365101385,-54.52586696211));
#1346 = CARTESIAN_POINT('',(1.656832618598,-54.5126811708));
#1347 = CARTESIAN_POINT('',(1.743460474208,-54.4741198481));
#1348 = CARTESIAN_POINT('',(1.828290322267,-54.41479855042));
#1349 = CARTESIAN_POINT('',(1.971594781742,-54.28547774466));
#1350 = CARTESIAN_POINT('',(2.031567614287,-54.22161445862));
#1351 = CARTESIAN_POINT('',(2.090114016173,-54.1513875069));
#1352 = CARTESIAN_POINT('',(2.147319902345,-54.07673323737));
#1353 = CARTESIAN_POINT('',(2.203254888559,-53.99945923602));
#1354 = CARTESIAN_POINT('',(2.257975891105,-53.9211963351));
#1355 = CARTESIAN_POINT('',(2.311529355193,-53.84337276587));
#1356 = CARTESIAN_POINT('',(2.389263035505,-53.73043752542));
#1357 = CARTESIAN_POINT('',(2.414336130543,-53.69401438228));
#1358 = CARTESIAN_POINT('',(2.4391845108,-53.65806546301));
#1359 = CARTESIAN_POINT('',(2.463820540906,-53.62270539084));
#1360 = CARTESIAN_POINT('',(2.488256081089,-53.58803940193));
#1361 = CARTESIAN_POINT('',(2.512502731814,-53.55416377157));
#1362 = CARTESIAN_POINT('',(2.536572078413,-53.52116624029));
#1363 = CARTESIAN_POINT('',(2.580307681015,-53.46254474033));
#1364 = CARTESIAN_POINT('',(2.600025330471,-53.43662251874));
#1365 = CARTESIAN_POINT('',(2.619635134541,-53.41140593209));
#1366 = CARTESIAN_POINT('',(2.639143114956,-53.38693771382));
#1367 = CARTESIAN_POINT('',(2.658555133982,-53.36325732859));
#1368 = CARTESIAN_POINT('',(2.677876963656,-53.34040112687));
#1369 = CARTESIAN_POINT('',(2.697114355044,-53.31840249953));
#1370 = CARTESIAN_POINT('',(2.77087644581,-53.23712621636));
#1371 = CARTESIAN_POINT('',(2.824847692022,-53.18416734561));
#1372 = CARTESIAN_POINT('',(2.87830496241,-53.13914446167));
#1373 = CARTESIAN_POINT('',(2.931357589863,-53.10260591941));
#1374 = CARTESIAN_POINT('',(2.98411002512,-53.07494461501));
#1375 = CARTESIAN_POINT('',(3.036664783535,-53.05641599407));
#1376 = CARTESIAN_POINT('',(3.089124437366,-53.04714883949));
#1377 = CARTESIAN_POINT('',(3.180937007787,-53.04714883949));
#1378 = CARTESIAN_POINT('',(3.220279349187,-53.05235977177));
#1379 = CARTESIAN_POINT('',(3.259662258579,-53.06278066556));
#1380 = CARTESIAN_POINT('',(3.299127985908,-53.07837054));
#1381 = CARTESIAN_POINT('',(3.338718820558,-53.09904784323));
#1382 = CARTESIAN_POINT('',(3.378477587615,-53.12468862493));
#1383 = CARTESIAN_POINT('',(3.418448270141,-53.1551234424));
#1384 = CARTESIAN_POINT('',(3.492379271151,-53.21946321292));
#1385 = CARTESIAN_POINT('',(3.52626139518,-53.25200325362));
#1386 = CARTESIAN_POINT('',(3.560353872704,-53.28762667762));
#1387 = CARTESIAN_POINT('',(3.594687639415,-53.32618445595));
#1388 = CARTESIAN_POINT('',(3.629294145598,-53.36750145268));
#1389 = CARTESIAN_POINT('',(3.664205678215,-53.41137290265));
#1390 = CARTESIAN_POINT('',(3.699455682986,-53.457560889));
#1391 = CARTESIAN_POINT('',(3.759012820255,-53.53819430377));
#1392 = CARTESIAN_POINT('',(3.783115785924,-53.57152035648));
#1393 = CARTESIAN_POINT('',(3.807399806853,-53.60568738382));
#1394 = CARTESIAN_POINT('',(3.831876476499,-53.64060553929));
#1395 = CARTESIAN_POINT('',(3.856557405666,-53.67617631259));
#1396 = CARTESIAN_POINT('',(3.88145446978,-53.71229211717));
#1397 = CARTESIAN_POINT('',(3.90658005615,-53.74883587778));
#1398 = CARTESIAN_POINT('',(3.982714345048,-53.85941733752));
#1399 = CARTESIAN_POINT('',(4.034548082418,-53.93450319736));
#1400 = CARTESIAN_POINT('',(4.087480332443,-54.0098117035));
#1401 = CARTESIAN_POINT('',(4.141551542125,-54.08403744058));
#1402 = CARTESIAN_POINT('',(4.196811544928,-54.15570149559));
#1403 = CARTESIAN_POINT('',(4.253321416852,-54.22317487361));
#1404 = CARTESIAN_POINT('',(4.311156440016,-54.28471990553));
#1405 = CARTESIAN_POINT('',(4.452130672989,-54.41278958402));
#1406 = CARTESIAN_POINT('',(4.536905730861,-54.47267822463));
#1407 = CARTESIAN_POINT('',(4.623501044465,-54.51192577812));
#1408 = CARTESIAN_POINT('',(4.710957754976,-54.52586696211));
#1409 = CARTESIAN_POINT('',(4.798425272187,-54.5126811708));
#1410 = CARTESIAN_POINT('',(4.885053127798,-54.4741198481));
#1411 = CARTESIAN_POINT('',(4.969882975857,-54.41479855042));
#1412 = CARTESIAN_POINT('',(5.113187435276,-54.28547774471));
#1413 = CARTESIAN_POINT('',(5.173160267849,-54.22161445866));
#1414 = CARTESIAN_POINT('',(5.231706669783,-54.15138750689));
#1415 = CARTESIAN_POINT('',(5.288912555979,-54.07673323731));
#1416 = CARTESIAN_POINT('',(5.344847542181,-53.99945923597));
#1417 = CARTESIAN_POINT('',(5.399568544685,-53.92119633511));
#1418 = CARTESIAN_POINT('',(5.453122008737,-53.84337276593));
#1419 = CARTESIAN_POINT('',(5.530855689095,-53.73043752542));
#1420 = CARTESIAN_POINT('',(5.555928784132,-53.69401438228));
#1421 = CARTESIAN_POINT('',(5.58077716439,-53.65806546301));
#1422 = CARTESIAN_POINT('',(5.605413194496,-53.62270539084));
#1423 = CARTESIAN_POINT('',(5.629848734679,-53.58803940193));
#1424 = CARTESIAN_POINT('',(5.654095385404,-53.55416377157));
#1425 = CARTESIAN_POINT('',(5.678164732003,-53.52116624029));
#1426 = CARTESIAN_POINT('',(5.778401708157,-53.38681249772));
#1427 = CARTESIAN_POINT('',(5.853082305777,-53.29421803983));
#1428 = CARTESIAN_POINT('',(5.926443989849,-53.21401885931));
#1429 = CARTESIAN_POINT('',(5.998777324806,-53.14813172155));
#1430 = CARTESIAN_POINT('',(6.070354851269,-53.09787031701));
#1431 = CARTESIAN_POINT('',(6.14143966809,-53.06406727981));
#1432 = CARTESIAN_POINT('',(6.212291641669,-53.04714886038));
#1433 = CARTESIAN_POINT('',(6.28318530718,-53.04714883949));
#1434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1435 = FACE_BOUND('',#1436,.F.);
#1436 = EDGE_LOOP('',(#1437));
#1437 = ORIENTED_EDGE('',*,*,#1438,.T.);
#1438 = EDGE_CURVE('',#1439,#1439,#1441,.T.);
#1439 = VERTEX_POINT('',#1440);
#1440 = CARTESIAN_POINT('',(20.320508075689,50.570020284215,31.));
#1441 = SURFACE_CURVE('',#1442,(#1517,#1595),.PCURVE_S1.);
#1442 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1443,#1444,#1445,#1446,#1447,
    #1448,#1449,#1450,#1451,#1452,#1453,#1454,#1455,#1456,#1457,#1458,
    #1459,#1460,#1461,#1462,#1463,#1464,#1465,#1466,#1467,#1468,#1469,
    #1470,#1471,#1472,#1473,#1474,#1475,#1476,#1477,#1478,#1479,#1480,
    #1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488,#1489,#1490,#1491,
    #1492,#1493,#1494,#1495,#1496,#1497,#1498,#1499,#1500,#1501,#1502,
    #1503,#1504,#1505,#1506,#1507,#1508,#1509,#1510,#1511,#1512,#1513,
    #1514,#1515,#1516),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1443 = CARTESIAN_POINT('',(20.320508075689,50.570020284215,31.));
#1444 = CARTESIAN_POINT('',(20.320508045685,50.570020296271,
    31.30004235822));
#1445 = CARTESIAN_POINT('',(20.285498683018,50.584088066308,
    31.600119319529));
#1446 = CARTESIAN_POINT('',(20.215462024456,50.612230903541,
    31.896941813699));
#1447 = CARTESIAN_POINT('',(20.110123579602,50.654381428493,
    32.187084404773));
#1448 = CARTESIAN_POINT('',(19.969053061397,50.71035817804,
    32.466760764424));
#1449 = CARTESIAN_POINT('',(19.791781583076,50.779797639766,
    32.731519738803));
#1450 = CARTESIAN_POINT('',(19.411645445764,50.926121010195,
    33.165862218238));
#1451 = CARTESIAN_POINT('',(19.222491637874,50.998201713553,
    33.344307144701));
#1452 = CARTESIAN_POINT('',(19.011037077591,51.077782447966,
    33.507200053127));
#1453 = CARTESIAN_POINT('',(18.778442044322,51.164003294974,
    33.650692470807));
#1454 = CARTESIAN_POINT('',(18.526495126581,51.255750040084,
    33.771029071684));
#1455 = CARTESIAN_POINT('',(18.257619988982,51.351718324578,
    33.864563330308));
#1456 = CARTESIAN_POINT('',(17.691643930458,51.549449910142,
    33.991073968074));
#1457 = CARTESIAN_POINT('',(17.396136466755,51.650647458026,
    34.023549203447));
#1458 = CARTESIAN_POINT('',(17.095137855766,51.751421874239,
    34.021511967389));
#1459 = CARTESIAN_POINT('',(16.796056511442,51.849156767436,
    33.983884737171));
#1460 = CARTESIAN_POINT('',(16.506274448207,51.941637516905,
    33.912084835909));
#1461 = CARTESIAN_POINT('',(16.23243681893,52.027183637302,
    33.809959322795));
#1462 = CARTESIAN_POINT('',(15.786123550286,52.164061684018,
    33.586991385794));
#1463 = CARTESIAN_POINT('',(15.604541023002,52.218831853333,
    33.475906238966));
#1464 = CARTESIAN_POINT('',(15.435771460009,52.268964331581,
    33.352335969763));
#1465 = CARTESIAN_POINT('',(15.2803552825,52.314503175032,
    33.218060964963));
#1466 = CARTESIAN_POINT('',(15.138640460344,52.355535043161,
    33.074799950063));
#1467 = CARTESIAN_POINT('',(15.010792391939,52.392176463835,
    32.924221256156));
#1468 = CARTESIAN_POINT('',(14.817213896347,52.447183129112,
    32.658842187914));
#1469 = CARTESIAN_POINT('',(14.744270287953,52.467758208132,
    32.546806973416));
#1470 = CARTESIAN_POINT('',(14.677901840979,52.48635863698,
    32.43224760279));
#1471 = CARTESIAN_POINT('',(14.618035754308,52.503044404045,
    32.315526293854));
#1472 = CARTESIAN_POINT('',(14.564599758913,52.517869008644,
    32.196971267331));
#1473 = CARTESIAN_POINT('',(14.517525528956,52.530878930356,
    32.076882503385));
#1474 = CARTESIAN_POINT('',(14.431885622016,52.554475006511,
    31.822011294041));
#1475 = CARTESIAN_POINT('',(14.394644518115,52.564687859486,
    31.68695231598));
#1476 = CARTESIAN_POINT('',(14.364942832196,52.57280470599,
    31.550708977322));
#1477 = CARTESIAN_POINT('',(14.34271345551,52.578865505424,
    31.413599645208));
#1478 = CARTESIAN_POINT('',(14.327909899491,52.582897026443,
    31.27592175169));
#1479 = CARTESIAN_POINT('',(14.320508075689,52.584912745522,
    31.137960904865));
#1480 = CARTESIAN_POINT('',(14.320508075689,52.584912745522,
    30.812185077994));
#1481 = CARTESIAN_POINT('',(14.334226055017,52.581177074018,
    30.624368544516));
#1482 = CARTESIAN_POINT('',(14.361661833231,52.573705151839,
    30.43726224769));
#1483 = CARTESIAN_POINT('',(14.40289997548,52.562450532223,
    30.251590519093));
#1484 = CARTESIAN_POINT('',(14.458098115899,52.54732175875,
    30.068133126846));
#1485 = CARTESIAN_POINT('',(14.527475281005,52.528183223268,
    29.88776822871));
#1486 = CARTESIAN_POINT('',(14.644924410989,52.495498922655,
    29.640816043229));
#1487 = CARTESIAN_POINT('',(14.680872291973,52.485468094657,
    29.570780351629));
#1488 = CARTESIAN_POINT('',(14.719158952376,52.474752618025,
    29.501478528318));
#1489 = CARTESIAN_POINT('',(14.759799209607,52.463340823056,
    29.432985247702));
#1490 = CARTESIAN_POINT('',(14.802807602146,52.451220292145,
    29.365379580667));
#1491 = CARTESIAN_POINT('',(14.848198389552,52.438377859791,
    29.298744994568));
#1492 = CARTESIAN_POINT('',(15.015272032645,52.390905545312,
    29.069479212101));
#1493 = CARTESIAN_POINT('',(15.149795757707,52.352339742892,
    28.911968963967));
#1494 = CARTESIAN_POINT('',(15.299559910298,52.308942987433,
    28.762547645353));
#1495 = CARTESIAN_POINT('',(15.464346498798,52.260587660129,
    28.623163559514));
#1496 = CARTESIAN_POINT('',(15.643708975933,52.207185249312,
    28.495812942091));
#1497 = CARTESIAN_POINT('',(15.836962266044,52.148702593114,
    28.382552931292));
#1498 = CARTESIAN_POINT('',(16.339232639894,51.993994324202,
    28.146202433847));
#1499 = CARTESIAN_POINT('',(16.662169622396,51.892373914234,
    28.040254151874));
#1500 = CARTESIAN_POINT('',(17.003648540614,51.782176914742,
    27.976715069704));
#1501 = CARTESIAN_POINT('',(17.353003114192,51.666213504562,
    27.960364031603));
#1502 = CARTESIAN_POINT('',(17.698668679296,51.548199748604,
    27.991681473462));
#1503 = CARTESIAN_POINT('',(18.029608095958,51.43237881606,
    28.066789943049));
#1504 = CARTESIAN_POINT('',(18.552078362014,51.245708963284,
    28.254890813163));
#1505 = CARTESIAN_POINT('',(18.756184824584,51.171596310296,
    28.350027399374));
#1506 = CARTESIAN_POINT('',(18.94784191528,51.100958785834,
    28.460762923036));
#1507 = CARTESIAN_POINT('',(19.126125259647,51.034368402,28.585135037344
    ));
#1508 = CARTESIAN_POINT('',(19.290360440102,50.972307688029,
    28.721216930226));
#1509 = CARTESIAN_POINT('',(19.440105930748,50.915157051767,
    28.867112807818));
#1510 = CARTESIAN_POINT('',(19.789739082417,50.780597663249,
    29.265434042187));
#1511 = CARTESIAN_POINT('',(19.967698487679,50.710895663925,
    29.530556810045));
#1512 = CARTESIAN_POINT('',(20.109314614574,50.654705127983,
    29.810689346199));
#1513 = CARTESIAN_POINT('',(20.215058783059,50.61239293873,
    30.101350444293));
#1514 = CARTESIAN_POINT('',(20.2853643431,50.584142047774,
    30.398730049706));
#1515 = CARTESIAN_POINT('',(20.320508045627,50.570020296294,
    30.699382507555));
#1516 = CARTESIAN_POINT('',(20.320508075689,50.570020284215,31.));
#1517 = PCURVE('',#157,#1518);
#1518 = DEFINITIONAL_REPRESENTATION('',(#1519),#1594);
#1519 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1520,#1521,#1522,#1523,#1524,
    #1525,#1526,#1527,#1528,#1529,#1530,#1531,#1532,#1533,#1534,#1535,
    #1536,#1537,#1538,#1539,#1540,#1541,#1542,#1543,#1544,#1545,#1546,
    #1547,#1548,#1549,#1550,#1551,#1552,#1553,#1554,#1555,#1556,#1557,
    #1558,#1559,#1560,#1561,#1562,#1563,#1564,#1565,#1566,#1567,#1568,
    #1569,#1570,#1571,#1572,#1573,#1574,#1575,#1576,#1577,#1578,#1579,
    #1580,#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,#1589,#1590,
    #1591,#1592,#1593),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1520 = CARTESIAN_POINT('',(5.094471212436,-31.));
#1521 = CARTESIAN_POINT('',(5.094471211843,-31.30004235822));
#1522 = CARTESIAN_POINT('',(5.093778917098,-31.60011931952));
#1523 = CARTESIAN_POINT('',(5.092393972479,-31.8969418137));
#1524 = CARTESIAN_POINT('',(5.090312165257,-32.18708440477));
#1525 = CARTESIAN_POINT('',(5.087527411637,-32.46676076442));
#1526 = CARTESIAN_POINT('',(5.084034142344,-32.7315197388));
#1527 = CARTESIAN_POINT('',(5.076560281115,-33.16586221823));
#1528 = CARTESIAN_POINT('',(5.072846131788,-33.3443071447));
#1529 = CARTESIAN_POINT('',(5.068700613054,-33.50720005312));
#1530 = CARTESIAN_POINT('',(5.064149141374,-33.6506924708));
#1531 = CARTESIAN_POINT('',(5.059229440421,-33.77102907168));
#1532 = CARTESIAN_POINT('',(5.053991236127,-33.8645633303));
#1533 = CARTESIAN_POINT('',(5.042990831688,-33.99107396807));
#1534 = CARTESIAN_POINT('',(5.03725965745,-34.02354920344));
#1535 = CARTESIAN_POINT('',(5.031435655125,-34.02151196738));
#1536 = CARTESIAN_POINT('',(5.025662608733,-33.98388473717));
#1537 = CARTESIAN_POINT('',(5.020081469967,-33.9120848359));
#1538 = CARTESIAN_POINT('',(5.014817526907,-33.80995932279));
#1539 = CARTESIAN_POINT('',(5.00625182145,-33.58699138579));
#1540 = CARTESIAN_POINT('',(5.002771807091,-33.47590623896));
#1541 = CARTESIAN_POINT('',(4.999541426372,-33.35233596976));
#1542 = CARTESIAN_POINT('',(4.996569887773,-33.21806096496));
#1543 = CARTESIAN_POINT('',(4.993862831969,-33.07479995006));
#1544 = CARTESIAN_POINT('',(4.991422559437,-32.92422125615));
#1545 = CARTESIAN_POINT('',(4.987730044553,-32.65884218791));
#1546 = CARTESIAN_POINT('',(4.986339407353,-32.54680697341));
#1547 = CARTESIAN_POINT('',(4.985074718938,-32.43224760279));
#1548 = CARTESIAN_POINT('',(4.983934391901,-32.31552629385));
#1549 = CARTESIAN_POINT('',(4.982916883362,-32.19697126733));
#1550 = CARTESIAN_POINT('',(4.982020756504,-32.07688250338));
#1551 = CARTESIAN_POINT('',(4.980390827636,-31.82201129404));
#1552 = CARTESIAN_POINT('',(4.979682276036,-31.68695231598));
#1553 = CARTESIAN_POINT('',(4.979117307551,-31.55070897732));
#1554 = CARTESIAN_POINT('',(4.978694540588,-31.4135996452));
#1555 = CARTESIAN_POINT('',(4.97841302307,-31.27592175169));
#1556 = CARTESIAN_POINT('',(4.978272263798,-31.13796090486));
#1557 = CARTESIAN_POINT('',(4.978272263798,-30.81218507799));
#1558 = CARTESIAN_POINT('',(4.978533135585,-30.62436854451));
#1559 = CARTESIAN_POINT('',(4.979054880078,-30.43726224769));
#1560 = CARTESIAN_POINT('',(4.979839216793,-30.25159051909));
#1561 = CARTESIAN_POINT('',(4.980889377262,-30.06813312684));
#1562 = CARTESIAN_POINT('',(4.982209898443,-29.88776822871));
#1563 = CARTESIAN_POINT('',(4.984446817823,-29.64081604322));
#1564 = CARTESIAN_POINT('',(4.985131609317,-29.57078035162));
#1565 = CARTESIAN_POINT('',(4.985861111496,-29.50147852831));
#1566 = CARTESIAN_POINT('',(4.986635644418,-29.4329852477));
#1567 = CARTESIAN_POINT('',(4.987455527325,-29.36537958066));
#1568 = CARTESIAN_POINT('',(4.988321078645,-29.29874499456));
#1569 = CARTESIAN_POINT('',(4.991507998659,-29.0694792121));
#1570 = CARTESIAN_POINT('',(4.994075747048,-28.91196896396));
#1571 = CARTESIAN_POINT('',(4.996936735645,-28.76254764535));
#1572 = CARTESIAN_POINT('',(5.000087797067,-28.62316355951));
#1573 = CARTESIAN_POINT('',(5.003521573496,-28.49581294209));
#1574 = CARTESIAN_POINT('',(5.007226271946,-28.38255293129));
#1575 = CARTESIAN_POINT('',(5.016869519943,-28.14620243384));
#1576 = CARTESIAN_POINT('',(5.023081302051,-28.04025415187));
#1577 = CARTESIAN_POINT('',(5.029664855214,-27.9767150697));
#1578 = CARTESIAN_POINT('',(5.036418500878,-27.9603640316));
#1579 = CARTESIAN_POINT('',(5.043120123817,-27.99168147346));
#1580 = CARTESIAN_POINT('',(5.049553409081,-28.06678994304));
#1581 = CARTESIAN_POINT('',(5.059733519487,-28.25489081316));
#1582 = CARTESIAN_POINT('',(5.063717786028,-28.35002739937));
#1583 = CARTESIAN_POINT('',(5.067465608834,-28.46076292303));
#1584 = CARTESIAN_POINT('',(5.070957548546,-28.58513503734));
#1585 = CARTESIAN_POINT('',(5.074178982444,-28.72121693022));
#1586 = CARTESIAN_POINT('',(5.077119901361,-28.86711280781));
#1587 = CARTESIAN_POINT('',(5.083993893649,-29.26543404218));
#1588 = CARTESIAN_POINT('',(5.087500672207,-29.53055681004));
#1589 = CARTESIAN_POINT('',(5.090296177671,-29.81068934619));
#1590 = CARTESIAN_POINT('',(5.092385998552,-30.10135044429));
#1591 = CARTESIAN_POINT('',(5.093776260586,-30.3987300497));
#1592 = CARTESIAN_POINT('',(5.094471211842,-30.69938250755));
#1593 = CARTESIAN_POINT('',(5.094471212436,-31.));
#1594 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1595 = PCURVE('',#1596,#1601);
#1596 = CYLINDRICAL_SURFACE('',#1597,3.);
#1597 = AXIS2_PLACEMENT_3D('',#1598,#1599,#1600);
#1598 = CARTESIAN_POINT('',(17.320508075689,-6.88E-15,31.));
#1599 = DIRECTION('',(0.,-1.,-2.2E-16));
#1600 = DIRECTION('',(1.,0.,0.));
#1601 = DEFINITIONAL_REPRESENTATION('',(#1602),#1677);
#1602 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1603,#1604,#1605,#1606,#1607,
    #1608,#1609,#1610,#1611,#1612,#1613,#1614,#1615,#1616,#1617,#1618,
    #1619,#1620,#1621,#1622,#1623,#1624,#1625,#1626,#1627,#1628,#1629,
    #1630,#1631,#1632,#1633,#1634,#1635,#1636,#1637,#1638,#1639,#1640,
    #1641,#1642,#1643,#1644,#1645,#1646,#1647,#1648,#1649,#1650,#1651,
    #1652,#1653,#1654,#1655,#1656,#1657,#1658,#1659,#1660,#1661,#1662,
    #1663,#1664,#1665,#1666,#1667,#1668,#1669,#1670,#1671,#1672,#1673,
    #1674,#1675,#1676),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1603 = CARTESIAN_POINT('',(0.,-50.57002028421));
#1604 = CARTESIAN_POINT('',(0.100014119407,-50.57002029627));
#1605 = CARTESIAN_POINT('',(0.200039675558,-50.5840880663));
#1606 = CARTESIAN_POINT('',(0.300614988949,-50.61223090354));
#1607 = CARTESIAN_POINT('',(0.402232044324,-50.65438142849));
#1608 = CARTESIAN_POINT('',(0.505421761045,-50.71035817804));
#1609 = CARTESIAN_POINT('',(0.610748802244,-50.77979763976));
#1610 = CARTESIAN_POINT('',(0.803147945328,-50.92612101019));
#1611 = CARTESIAN_POINT('',(0.889433061186,-50.99820171355));
#1612 = CARTESIAN_POINT('',(0.977687487709,-51.07778244796));
#1613 = CARTESIAN_POINT('',(1.067884733127,-51.16400329497));
#1614 = CARTESIAN_POINT('',(1.160064055144,-51.25575004008));
#1615 = CARTESIAN_POINT('',(1.254332143065,-51.35171832457));
#1616 = CARTESIAN_POINT('',(1.447646464053,-51.54944991014));
#1617 = CARTESIAN_POINT('',(1.546138089096,-51.65064745802));
#1618 = CARTESIAN_POINT('',(1.645386305597,-51.75142187423));
#1619 = CARTESIAN_POINT('',(1.744620331801,-51.84915676743));
#1620 = CARTESIAN_POINT('',(1.843089971547,-51.9416375169));
#1621 = CARTESIAN_POINT('',(1.939960099014,-52.0271836373));
#1622 = CARTESIAN_POINT('',(2.106263113852,-52.16406168401));
#1623 = CARTESIAN_POINT('',(2.176958162849,-52.21883185333));
#1624 = CARTESIAN_POINT('',(2.246305773449,-52.26896433158));
#1625 = CARTESIAN_POINT('',(2.314382170268,-52.31450317503));
#1626 = CARTESIAN_POINT('',(2.381238370611,-52.35553504316));
#1627 = CARTESIAN_POINT('',(2.446903712243,-52.39217646383));
#1628 = CARTESIAN_POINT('',(2.556396829133,-52.44718312911));
#1629 = CARTESIAN_POINT('',(2.600897009314,-52.46775820813));
#1630 = CARTESIAN_POINT('',(2.644934170132,-52.48635863698));
#1631 = CARTESIAN_POINT('',(2.688558971827,-52.50304440404));
#1632 = CARTESIAN_POINT('',(2.73181947123,-52.51786900864));
#1633 = CARTESIAN_POINT('',(2.774762127689,-52.53087893035));
#1634 = CARTESIAN_POINT('',(2.864386979781,-52.55447500651));
#1635 = CARTESIAN_POINT('',(2.91101590729,-52.56468785948));
#1636 = CARTESIAN_POINT('',(2.957387572042,-52.57280470599));
#1637 = CARTESIAN_POINT('',(3.003567250358,-52.57886550542));
#1638 = CARTESIAN_POINT('',(3.049618756581,-52.58289702644));
#1639 = CARTESIAN_POINT('',(3.095605685301,-52.58491274552));
#1640 = CARTESIAN_POINT('',(3.204197627592,-52.58491274552));
#1641 = CARTESIAN_POINT('',(3.266802962355,-52.58117707401));
#1642 = CARTESIAN_POINT('',(3.329572745722,-52.57370515183));
#1643 = CARTESIAN_POINT('',(3.392665917731,-52.56245053222));
#1644 = CARTESIAN_POINT('',(3.456242369806,-52.54732175875));
#1645 = CARTESIAN_POINT('',(3.520469043963,-52.52818322326));
#1646 = CARTESIAN_POINT('',(3.611621955813,-52.49549892265));
#1647 = CARTESIAN_POINT('',(3.637850919763,-52.48546809465));
#1648 = CARTESIAN_POINT('',(3.664222656748,-52.47475261802));
#1649 = CARTESIAN_POINT('',(3.690747233106,-52.46334082305));
#1650 = CARTESIAN_POINT('',(3.717435048631,-52.45122029214));
#1651 = CARTESIAN_POINT('',(3.744296836564,-52.43837785979));
#1652 = CARTESIAN_POINT('',(3.838858046151,-52.39090554531));
#1653 = CARTESIAN_POINT('',(3.907698991031,-52.35233974289));
#1654 = CARTESIAN_POINT('',(3.977855076864,-52.30894298743));
#1655 = CARTESIAN_POINT('',(4.04935102893,-52.26058766012));
#1656 = CARTESIAN_POINT('',(4.122238543156,-52.20718524931));
#1657 = CARTESIAN_POINT('',(4.196600134377,-52.14870259311));
#1658 = CARTESIAN_POINT('',(4.381633790802,-51.9939943242));
#1659 = CARTESIAN_POINT('',(4.494071386901,-51.89237391423));
#1660 = CARTESIAN_POINT('',(4.608201267477,-51.78217691474));
#1661 = CARTESIAN_POINT('',(4.722838650826,-51.66621350456));
#1662 = CARTESIAN_POINT('',(4.836888293591,-51.5481997486));
#1663 = CARTESIAN_POINT('',(4.94916053828,-51.43237881606));
#1664 = CARTESIAN_POINT('',(5.134260246021,-51.24570896328));
#1665 = CARTESIAN_POINT('',(5.20901570686,-51.17159631029));
#1666 = CARTESIAN_POINT('',(5.282353157261,-51.10095878583));
#1667 = CARTESIAN_POINT('',(5.354355609859,-51.034368402));
#1668 = CARTESIAN_POINT('',(5.42507919296,-50.97230768802));
#1669 = CARTESIAN_POINT('',(5.494556477075,-50.91515705176));
#1670 = CARTESIAN_POINT('',(5.671224133202,-50.78059766324));
#1671 = CARTESIAN_POINT('',(5.776773639789,-50.71089566392));
#1672 = CARTESIAN_POINT('',(5.88017350666,-50.65470512798));
#1673 = CARTESIAN_POINT('',(5.981991650552,-50.61239293873));
#1674 = CARTESIAN_POINT('',(6.08276208956,-50.58414204777));
#1675 = CARTESIAN_POINT('',(6.182979476365,-50.57002029629));
#1676 = CARTESIAN_POINT('',(6.28318530718,-50.57002028421));
#1677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1678 = FACE_BOUND('',#1679,.F.);
#1679 = EDGE_LOOP('',(#1680));
#1680 = ORIENTED_EDGE('',*,*,#1681,.T.);
#1681 = EDGE_CURVE('',#1682,#1682,#1684,.T.);
#1682 = VERTEX_POINT('',#1683);
#1683 = CARTESIAN_POINT('',(-14.32050807568,52.584912745522,31.));
#1684 = SURFACE_CURVE('',#1685,(#1760,#1838),.PCURVE_S1.);
#1685 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1686,#1687,#1688,#1689,#1690,
    #1691,#1692,#1693,#1694,#1695,#1696,#1697,#1698,#1699,#1700,#1701,
    #1702,#1703,#1704,#1705,#1706,#1707,#1708,#1709,#1710,#1711,#1712,
    #1713,#1714,#1715,#1716,#1717,#1718,#1719,#1720,#1721,#1722,#1723,
    #1724,#1725,#1726,#1727,#1728,#1729,#1730,#1731,#1732,#1733,#1734,
    #1735,#1736,#1737,#1738,#1739,#1740,#1741,#1742,#1743,#1744,#1745,
    #1746,#1747,#1748,#1749,#1750,#1751,#1752,#1753,#1754,#1755,#1756,
    #1757,#1758,#1759),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1686 = CARTESIAN_POINT('',(-14.32050807568,52.584912745522,31.));
#1687 = CARTESIAN_POINT('',(-14.320508102,52.584912738356,
    31.263131799246));
#1688 = CARTESIAN_POINT('',(-14.34743377952,52.577580130886,
    31.526290318888));
#1689 = CARTESIAN_POINT('',(-14.40129715482,52.562911136451,
    31.78753477559));
#1690 = CARTESIAN_POINT('',(-14.48243182887,52.540722780751,
    32.044820745698));
#1691 = CARTESIAN_POINT('',(-14.59142360374,52.510667245987,
    32.295823777876));
#1692 = CARTESIAN_POINT('',(-14.72901527254,52.472242557457,
    32.537706703784));
#1693 = CARTESIAN_POINT('',(-15.01527203264,52.390905545312,
    32.930520787899));
#1694 = CARTESIAN_POINT('',(-15.1497957577,52.352339742892,
    33.088031036033));
#1695 = CARTESIAN_POINT('',(-15.29955991029,52.308942987433,
    33.237452354648));
#1696 = CARTESIAN_POINT('',(-15.46434649879,52.260587660129,
    33.376836440487));
#1697 = CARTESIAN_POINT('',(-15.64370897593,52.207185249312,
    33.504187057909));
#1698 = CARTESIAN_POINT('',(-15.83696226604,52.148702593114,
    33.617447068708));
#1699 = CARTESIAN_POINT('',(-16.33923263989,51.993994324202,
    33.853797566153));
#1700 = CARTESIAN_POINT('',(-16.66216962239,51.892373914234,
    33.959745848126));
#1701 = CARTESIAN_POINT('',(-17.00364854061,51.782176914741,
    34.023284930295));
#1702 = CARTESIAN_POINT('',(-17.35300311419,51.666213504563,
    34.039635968398));
#1703 = CARTESIAN_POINT('',(-17.69866867929,51.548199748604,
    34.008318526538));
#1704 = CARTESIAN_POINT('',(-18.02960809595,51.43237881606,
    33.933210056951));
#1705 = CARTESIAN_POINT('',(-18.55207836201,51.245708963284,
    33.745109186837));
#1706 = CARTESIAN_POINT('',(-18.75618482458,51.171596310296,
    33.649972600626));
#1707 = CARTESIAN_POINT('',(-18.94784191528,51.100958785834,
    33.539237076964));
#1708 = CARTESIAN_POINT('',(-19.12612525964,51.034368402,33.414864962656
    ));
#1709 = CARTESIAN_POINT('',(-19.2903604401,50.972307688029,
    33.278783069773));
#1710 = CARTESIAN_POINT('',(-19.44010593074,50.915157051767,
    33.132887192183));
#1711 = CARTESIAN_POINT('',(-19.67509060921,50.824721134881,
    32.865179777322));
#1712 = CARTESIAN_POINT('',(-19.76712123703,50.789039242453,
    32.746801547014));
#1713 = CARTESIAN_POINT('',(-19.85124427644,50.756211850017,
    32.624488433172));
#1714 = CARTESIAN_POINT('',(-19.92748355252,50.72629534247,32.4987678828
    ));
#1715 = CARTESIAN_POINT('',(-19.99587311471,50.69933332311,
    32.370123975433));
#1716 = CARTESIAN_POINT('',(-20.05645208458,50.675358488038,
    32.239005241359));
#1717 = CARTESIAN_POINT('',(-20.16979989818,50.630360603443,
    31.953158259286));
#1718 = CARTESIAN_POINT('',(-20.22013160567,50.610281746129,
    31.797770492405));
#1719 = CARTESIAN_POINT('',(-20.26032199133,50.594190244831,
    31.640272678355));
#1720 = CARTESIAN_POINT('',(-20.29042537845,50.582108369908,
    31.481224502098));
#1721 = CARTESIAN_POINT('',(-20.31048052088,50.574049668006,
    31.321155289792));
#1722 = CARTESIAN_POINT('',(-20.32050807568,50.570020284215,
    31.160577464184));
#1723 = CARTESIAN_POINT('',(-20.32050807568,50.570020284215,
    30.838960134466));
#1724 = CARTESIAN_POINT('',(-20.31042268855,50.574072907042,
    30.677919926001));
#1725 = CARTESIAN_POINT('',(-20.29025187906,50.582178086716,
    30.517392721751));
#1726 = CARTESIAN_POINT('',(-20.25997474969,50.594329608159,
    30.357895397146));
#1727 = CARTESIAN_POINT('',(-20.21955210805,50.610513763865,
    30.199961986914));
#1728 = CARTESIAN_POINT('',(-20.16892903453,50.630708010574,
    30.044157316089));
#1729 = CARTESIAN_POINT('',(-20.05546856524,50.675747449664,
    29.758945197059));
#1730 = CARTESIAN_POINT('',(-19.99524224826,50.699581276767,
    29.628830306039));
#1731 = CARTESIAN_POINT('',(-19.92732031073,50.726357852839,
    29.501157806191));
#1732 = CARTESIAN_POINT('',(-19.85166452585,50.756045141774,
    29.37636653838));
#1733 = CARTESIAN_POINT('',(-19.76824158935,50.788600520045,
    29.254929870651));
#1734 = CARTESIAN_POINT('',(-19.67702808076,50.823968970978,
    29.13736323278));
#1735 = CARTESIAN_POINT('',(-19.41164544592,50.926121010132,
    28.834137781948));
#1736 = CARTESIAN_POINT('',(-19.22249163732,50.998201713757,
    28.655692854896));
#1737 = CARTESIAN_POINT('',(-19.0110370781,51.077782447781,
    28.49279994708));
#1738 = CARTESIAN_POINT('',(-18.77844204463,51.164003294858,
    28.349307529457));
#1739 = CARTESIAN_POINT('',(-18.52649512576,51.255750040375,
    28.228970927977));
#1740 = CARTESIAN_POINT('',(-18.25761998939,51.351718324435,
    28.135436669784));
#1741 = CARTESIAN_POINT('',(-17.69164393045,51.549449910142,
    28.008926031926));
#1742 = CARTESIAN_POINT('',(-17.39613646675,51.650647458026,
    27.976450796552));
#1743 = CARTESIAN_POINT('',(-17.09513785576,51.751421874239,
    27.978488032611));
#1744 = CARTESIAN_POINT('',(-16.79605651144,51.849156767437,
    28.016115262829));
#1745 = CARTESIAN_POINT('',(-16.5062744482,51.941637516904,
    28.087915164091));
#1746 = CARTESIAN_POINT('',(-16.23243681893,52.027183637302,
    28.190040677205));
#1747 = CARTESIAN_POINT('',(-15.78612355028,52.164061684018,
    28.413008614206));
#1748 = CARTESIAN_POINT('',(-15.604541023,52.218831853333,
    28.524093761034));
#1749 = CARTESIAN_POINT('',(-15.43577146,52.268964331581,28.647664030237
    ));
#1750 = CARTESIAN_POINT('',(-15.2803552825,52.314503175032,
    28.781939035037));
#1751 = CARTESIAN_POINT('',(-15.13864046034,52.355535043161,
    28.925200049937));
#1752 = CARTESIAN_POINT('',(-15.01079239193,52.392176463835,
    29.075778743843));
#1753 = CARTESIAN_POINT('',(-14.72959268924,52.472081299723,
    29.461278769876));
#1754 = CARTESIAN_POINT('',(-14.59180478095,52.510562132976,
    29.703298747836));
#1755 = CARTESIAN_POINT('',(-14.48265877873,52.540660715569,
    29.954459746131));
#1756 = CARTESIAN_POINT('',(-14.40141012422,52.562880369983,
    30.21191740122));
#1757 = CARTESIAN_POINT('',(-14.34747142353,52.577569879761,
    30.473341867979));
#1758 = CARTESIAN_POINT('',(-14.32050810202,52.584912738351,
    30.736684322692));
#1759 = CARTESIAN_POINT('',(-14.32050807568,52.584912745522,31.));
#1760 = PCURVE('',#157,#1761);
#1761 = DEFINITIONAL_REPRESENTATION('',(#1762),#1837);
#1762 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1763,#1764,#1765,#1766,#1767,
    #1768,#1769,#1770,#1771,#1772,#1773,#1774,#1775,#1776,#1777,#1778,
    #1779,#1780,#1781,#1782,#1783,#1784,#1785,#1786,#1787,#1788,#1789,
    #1790,#1791,#1792,#1793,#1794,#1795,#1796,#1797,#1798,#1799,#1800,
    #1801,#1802,#1803,#1804,#1805,#1806,#1807,#1808,#1809,#1810,#1811,
    #1812,#1813,#1814,#1815,#1816,#1817,#1818,#1819,#1820,#1821,#1822,
    #1823,#1824,#1825,#1826,#1827,#1828,#1829,#1830,#1831,#1832,#1833,
    #1834,#1835,#1836),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1763 = CARTESIAN_POINT('',(4.446505696971,-31.));
#1764 = CARTESIAN_POINT('',(4.446505696471,-31.26313179924));
#1765 = CARTESIAN_POINT('',(4.445993654875,-31.52629031888));
#1766 = CARTESIAN_POINT('',(4.44496934153,-31.78753477559));
#1767 = CARTESIAN_POINT('',(4.443425966294,-32.04482074569));
#1768 = CARTESIAN_POINT('',(4.44135148324,-32.29582377787));
#1769 = CARTESIAN_POINT('',(4.438730293141,-32.53770670378));
#1770 = CARTESIAN_POINT('',(4.43326996211,-32.93052078789));
#1771 = CARTESIAN_POINT('',(4.430702213721,-33.08803103603));
#1772 = CARTESIAN_POINT('',(4.427841225125,-33.23745235464));
#1773 = CARTESIAN_POINT('',(4.424690163702,-33.37683644048));
#1774 = CARTESIAN_POINT('',(4.421256387274,-33.5041870579));
#1775 = CARTESIAN_POINT('',(4.417551688824,-33.6174470687));
#1776 = CARTESIAN_POINT('',(4.407908440826,-33.85379756615));
#1777 = CARTESIAN_POINT('',(4.401696658719,-33.95974584812));
#1778 = CARTESIAN_POINT('',(4.395113105556,-34.02328493029));
#1779 = CARTESIAN_POINT('',(4.388359459892,-34.03963596839));
#1780 = CARTESIAN_POINT('',(4.381657836953,-34.00831852653));
#1781 = CARTESIAN_POINT('',(4.375224551688,-33.93321005695));
#1782 = CARTESIAN_POINT('',(4.365044441282,-33.74510918683));
#1783 = CARTESIAN_POINT('',(4.361060174742,-33.64997260062));
#1784 = CARTESIAN_POINT('',(4.357312351936,-33.53923707696));
#1785 = CARTESIAN_POINT('',(4.353820412223,-33.41486496265));
#1786 = CARTESIAN_POINT('',(4.350598978325,-33.27878306977));
#1787 = CARTESIAN_POINT('',(4.347658059408,-33.13288719218));
#1788 = CARTESIAN_POINT('',(4.34303812317,-32.86517977732));
#1789 = CARTESIAN_POINT('',(4.341227013213,-32.74680154701));
#1790 = CARTESIAN_POINT('',(4.339570114548,-32.62448843317));
#1791 = CARTESIAN_POINT('',(4.338067386862,-32.4987678828));
#1792 = CARTESIAN_POINT('',(4.336718536117,-32.37012397543));
#1793 = CARTESIAN_POINT('',(4.335523112483,-32.23900524135));
#1794 = CARTESIAN_POINT('',(4.333285442589,-31.95315825928));
#1795 = CARTESIAN_POINT('',(4.332291151358,-31.7977704924));
#1796 = CARTESIAN_POINT('',(4.331496802279,-31.64027267835));
#1797 = CARTESIAN_POINT('',(4.330901620477,-31.48122450209));
#1798 = CARTESIAN_POINT('',(4.330505038839,-31.32115528979));
#1799 = CARTESIAN_POINT('',(4.330306748333,-31.16057746418));
#1800 = CARTESIAN_POINT('',(4.330306748333,-30.83896013446));
#1801 = CARTESIAN_POINT('',(4.330506182448,-30.677919926));
#1802 = CARTESIAN_POINT('',(4.330905051352,-30.51739272175));
#1803 = CARTESIAN_POINT('',(4.33150366768,-30.35789539714));
#1804 = CARTESIAN_POINT('',(4.332302604912,-30.19996198691));
#1805 = CARTESIAN_POINT('',(4.333302646248,-30.04415731608));
#1806 = CARTESIAN_POINT('',(4.335542518697,-29.75894519705));
#1807 = CARTESIAN_POINT('',(4.336730973596,-29.62883030603));
#1808 = CARTESIAN_POINT('',(4.338070594064,-29.50115780619));
#1809 = CARTESIAN_POINT('',(4.339561819088,-29.37636653838));
#1810 = CARTESIAN_POINT('',(4.341204936454,-29.25492987065));
#1811 = CARTESIAN_POINT('',(4.342999988433,-29.13736323278));
#1812 = CARTESIAN_POINT('',(4.348217679651,-28.83413778194));
#1813 = CARTESIAN_POINT('',(4.351931828992,-28.65569285489));
#1814 = CARTESIAN_POINT('',(4.356077347705,-28.49279994708));
#1815 = CARTESIAN_POINT('',(4.360628819389,-28.34930752945));
#1816 = CARTESIAN_POINT('',(4.365548520364,-28.22897092797));
#1817 = CARTESIAN_POINT('',(4.370786724635,-28.13543666978));
#1818 = CARTESIAN_POINT('',(4.381787129082,-28.00892603192));
#1819 = CARTESIAN_POINT('',(4.387518303319,-27.97645079655));
#1820 = CARTESIAN_POINT('',(4.393342305644,-27.97848803261));
#1821 = CARTESIAN_POINT('',(4.399115352036,-28.01611526282));
#1822 = CARTESIAN_POINT('',(4.404696490802,-28.08791516409));
#1823 = CARTESIAN_POINT('',(4.409960433863,-28.1900406772));
#1824 = CARTESIAN_POINT('',(4.418526139319,-28.4130086142));
#1825 = CARTESIAN_POINT('',(4.422006153678,-28.52409376103));
#1826 = CARTESIAN_POINT('',(4.425236534397,-28.64766403023));
#1827 = CARTESIAN_POINT('',(4.428208072996,-28.78193903503));
#1828 = CARTESIAN_POINT('',(4.4309151288,-28.92520004993));
#1829 = CARTESIAN_POINT('',(4.433355401333,-29.07577874384));
#1830 = CARTESIAN_POINT('',(4.438719293041,-29.46127876987));
#1831 = CARTESIAN_POINT('',(4.441344228145,-29.70329874783));
#1832 = CARTESIAN_POINT('',(4.443421649167,-29.95445974613));
#1833 = CARTESIAN_POINT('',(4.444967193201,-30.21191740122));
#1834 = CARTESIAN_POINT('',(4.445992939006,-30.47334186797));
#1835 = CARTESIAN_POINT('',(4.446505696471,-30.73668432269));
#1836 = CARTESIAN_POINT('',(4.446505696971,-31.));
#1837 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1838 = PCURVE('',#1839,#1844);
#1839 = CYLINDRICAL_SURFACE('',#1840,3.);
#1840 = AXIS2_PLACEMENT_3D('',#1841,#1842,#1843);
#1841 = CARTESIAN_POINT('',(-17.32050807568,-6.88E-15,31.));
#1842 = DIRECTION('',(0.,-1.,-2.2E-16));
#1843 = DIRECTION('',(1.,0.,0.));
#1844 = DEFINITIONAL_REPRESENTATION('',(#1845),#1920);
#1845 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1846,#1847,#1848,#1849,#1850,
    #1851,#1852,#1853,#1854,#1855,#1856,#1857,#1858,#1859,#1860,#1861,
    #1862,#1863,#1864,#1865,#1866,#1867,#1868,#1869,#1870,#1871,#1872,
    #1873,#1874,#1875,#1876,#1877,#1878,#1879,#1880,#1881,#1882,#1883,
    #1884,#1885,#1886,#1887,#1888,#1889,#1890,#1891,#1892,#1893,#1894,
    #1895,#1896,#1897,#1898,#1899,#1900,#1901,#1902,#1903,#1904,#1905,
    #1906,#1907,#1908,#1909,#1910,#1911,#1912,#1913,#1914,#1915,#1916,
    #1917,#1918,#1919),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1846 = CARTESIAN_POINT('',(0.,-52.58491274552));
#1847 = CARTESIAN_POINT('',(8.771059974874E-02,-52.58491273835));
#1848 = CARTESIAN_POINT('',(0.175430674758,-52.57758013088));
#1849 = CARTESIAN_POINT('',(0.263612327933,-52.56291113645));
#1850 = CARTESIAN_POINT('',(0.35268440216,-52.54072278075));
#1851 = CARTESIAN_POINT('',(0.443075222705,-52.51066724598));
#1852 = CARTESIAN_POINT('',(0.535248302453,-52.47224255745));
#1853 = CARTESIAN_POINT('',(0.697265392561,-52.39090554531));
#1854 = CARTESIAN_POINT('',(0.766106337441,-52.35233974289));
#1855 = CARTESIAN_POINT('',(0.836262423274,-52.30894298743));
#1856 = CARTESIAN_POINT('',(0.90775837534,-52.26058766012));
#1857 = CARTESIAN_POINT('',(0.980645889566,-52.20718524931));
#1858 = CARTESIAN_POINT('',(1.055007480787,-52.14870259311));
#1859 = CARTESIAN_POINT('',(1.240041137212,-51.9939943242));
#1860 = CARTESIAN_POINT('',(1.352478733311,-51.89237391423));
#1861 = CARTESIAN_POINT('',(1.466608613887,-51.78217691474));
#1862 = CARTESIAN_POINT('',(1.581245997236,-51.66621350456));
#1863 = CARTESIAN_POINT('',(1.695295640001,-51.5481997486));
#1864 = CARTESIAN_POINT('',(1.80756788469,-51.43237881606));
#1865 = CARTESIAN_POINT('',(1.992667592431,-51.24570896328));
#1866 = CARTESIAN_POINT('',(2.06742305327,-51.17159631029));
#1867 = CARTESIAN_POINT('',(2.140760503672,-51.10095878583));
#1868 = CARTESIAN_POINT('',(2.212762956269,-51.034368402));
#1869 = CARTESIAN_POINT('',(2.28348653937,-50.97230768802));
#1870 = CARTESIAN_POINT('',(2.352963823485,-50.91515705176));
#1871 = CARTESIAN_POINT('',(2.471700253859,-50.82472113488));
#1872 = CARTESIAN_POINT('',(2.521592857756,-50.78903924245));
#1873 = CARTESIAN_POINT('',(2.570942460347,-50.75621185001));
#1874 = CARTESIAN_POINT('',(2.619810229942,-50.72629534247));
#1875 = CARTESIAN_POINT('',(2.668253048541,-50.69933332311));
#1876 = CARTESIAN_POINT('',(2.716324936981,-50.67535848803));
#1877 = CARTESIAN_POINT('',(2.818824922895,-50.63036060344));
#1878 = CARTESIAN_POINT('',(2.873157921603,-50.61028174612));
#1879 = CARTESIAN_POINT('',(2.927166438366,-50.59419024483));
#1880 = CARTESIAN_POINT('',(2.9809339881,-50.5821083699));
#1881 = CARTESIAN_POINT('',(3.034540942187,-50.574049668));
#1882 = CARTESIAN_POINT('',(3.088066832195,-50.57002028421));
#1883 = CARTESIAN_POINT('',(3.195272608768,-50.57002028421));
#1884 = CARTESIAN_POINT('',(3.248952625234,-50.57407290704));
#1885 = CARTESIAN_POINT('',(3.302714414947,-50.58217808671));
#1886 = CARTESIAN_POINT('',(3.356638192244,-50.59432960815));
#1887 = CARTESIAN_POINT('',(3.410805019631,-50.61051376386));
#1888 = CARTESIAN_POINT('',(3.465299153552,-50.63070801057));
#1889 = CARTESIAN_POINT('',(3.567616324335,-50.67574744966));
#1890 = CARTESIAN_POINT('',(3.615336756159,-50.69958127676));
#1891 = CARTESIAN_POINT('',(3.66342313902,-50.72635785283));
#1892 = CARTESIAN_POINT('',(3.711928335217,-50.75604514177));
#1893 = CARTESIAN_POINT('',(3.760907988983,-50.78860052004));
#1894 = CARTESIAN_POINT('',(3.810421899506,-50.82396897097));
#1895 = CARTESIAN_POINT('',(3.944740598835,-50.92612101013));
#1896 = CARTESIAN_POINT('',(4.031025715013,-50.99820171375));
#1897 = CARTESIAN_POINT('',(4.119280141108,-51.07778244778));
#1898 = CARTESIAN_POINT('',(4.209477386578,-51.16400329485));
#1899 = CARTESIAN_POINT('',(4.301656709036,-51.25575004037));
#1900 = CARTESIAN_POINT('',(4.395924796514,-51.35171832443));
#1901 = CARTESIAN_POINT('',(4.589239117642,-51.54944991014));
#1902 = CARTESIAN_POINT('',(4.687730742687,-51.65064745802));
#1903 = CARTESIAN_POINT('',(4.786978959186,-51.75142187423));
#1904 = CARTESIAN_POINT('',(4.886212985392,-51.84915676743));
#1905 = CARTESIAN_POINT('',(4.984682625136,-51.9416375169));
#1906 = CARTESIAN_POINT('',(5.081552752604,-52.0271836373));
#1907 = CARTESIAN_POINT('',(5.247855767442,-52.16406168401));
#1908 = CARTESIAN_POINT('',(5.318550816439,-52.21883185333));
#1909 = CARTESIAN_POINT('',(5.387898427039,-52.26896433158));
#1910 = CARTESIAN_POINT('',(5.455974823858,-52.31450317503));
#1911 = CARTESIAN_POINT('',(5.522831024201,-52.35553504316));
#1912 = CARTESIAN_POINT('',(5.588496365833,-52.39217646383));
#1913 = CARTESIAN_POINT('',(5.747550354872,-52.47208129972));
#1914 = CARTESIAN_POINT('',(5.839794069457,-52.51056213297));
#1915 = CARTESIAN_POINT('',(5.93025181004,-52.54066071556));
#1916 = CARTESIAN_POINT('',(6.019388065895,-52.56288036998));
#1917 = CARTESIAN_POINT('',(6.107632025669,-52.57756987976));
#1918 = CARTESIAN_POINT('',(6.195413414743,-52.58491273835));
#1919 = CARTESIAN_POINT('',(6.28318530718,-52.58491274552));
#1920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1921 = FACE_BOUND('',#1922,.F.);
#1922 = EDGE_LOOP('',(#1923));
#1923 = ORIENTED_EDGE('',*,*,#1924,.T.);
#1924 = EDGE_CURVE('',#1925,#1925,#1927,.T.);
#1925 = VERTEX_POINT('',#1926);
#1926 = CARTESIAN_POINT('',(3.,54.417368550859,61.));
#1927 = SURFACE_CURVE('',#1928,(#2003,#2081),.PCURVE_S1.);
#1928 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1929,#1930,#1931,#1932,#1933,
    #1934,#1935,#1936,#1937,#1938,#1939,#1940,#1941,#1942,#1943,#1944,
    #1945,#1946,#1947,#1948,#1949,#1950,#1951,#1952,#1953,#1954,#1955,
    #1956,#1957,#1958,#1959,#1960,#1961,#1962,#1963,#1964,#1965,#1966,
    #1967,#1968,#1969,#1970,#1971,#1972,#1973,#1974,#1975,#1976,#1977,
    #1978,#1979,#1980,#1981,#1982,#1983,#1984,#1985,#1986,#1987,#1988,
    #1989,#1990,#1991,#1992,#1993,#1994,#1995,#1996,#1997,#1998,#1999,
    #2000,#2001,#2002),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#1929 = CARTESIAN_POINT('',(3.,54.417368550859,61.));
#1930 = CARTESIAN_POINT('',(2.999999969832,54.417368552522,
    61.301675404984));
#1931 = CARTESIAN_POINT('',(2.964609026852,54.419319616309,
    61.603006772432));
#1932 = CARTESIAN_POINT('',(2.893965029493,54.423214284332,
    61.901768828789));
#1933 = CARTESIAN_POINT('',(2.787174533175,54.428955875812,
    62.195466360299));
#1934 = CARTESIAN_POINT('',(2.642558277606,54.436350297716,
    62.480833492903));
#1935 = CARTESIAN_POINT('',(2.45793211932,54.44501933438,62.75320895181)
  );
#1936 = CARTESIAN_POINT('',(2.063103655172,54.461195888245,
    63.19242226762));
#1937 = CARTESIAN_POINT('',(1.870492538865,54.468482054394,
    63.369973548066));
#1938 = CARTESIAN_POINT('',(1.653071166507,54.475859619699,
    63.533365155146));
#1939 = CARTESIAN_POINT('',(1.411549498806,54.482906790071,
    63.677688286879));
#1940 = CARTESIAN_POINT('',(1.147450079878,54.48912317489,
    63.798028403946));
#1941 = CARTESIAN_POINT('',(0.863169063463,54.493995677802,
    63.889438139478));
#1942 = CARTESIAN_POINT('',(0.224252530968,54.500586176271,
    64.011317986418));
#1943 = CARTESIAN_POINT('',(-0.125543171186,54.501749582478,
    64.031199639289));
#1944 = CARTESIAN_POINT('',(-0.476230923152,54.500170834836,
    64.002771098859));
#1945 = CARTESIAN_POINT('',(-0.816040368012,54.495869619363,
    63.926994407233));
#1946 = CARTESIAN_POINT('',(-1.133902307673,54.489512375875,
    63.809287379311));
#1947 = CARTESIAN_POINT('',(-1.421079579336,54.482048154056,
    63.65930263254));
#1948 = CARTESIAN_POINT('',(-1.768256496164,54.471391905819,
    63.426252124426));
#1949 = CARTESIAN_POINT('',(-1.860114170785,54.468373943648,
    63.358300223116));
#1950 = CARTESIAN_POINT('',(-1.947616057246,54.465323584161,
    63.28732280723));
#1951 = CARTESIAN_POINT('',(-2.030781955822,54.462270251616,
    63.213631848085));
#1952 = CARTESIAN_POINT('',(-2.109643477642,54.459240533206,
    63.137515686202));
#1953 = CARTESIAN_POINT('',(-2.184243541196,54.456258076803,
    63.059239286154));
#1954 = CARTESIAN_POINT('',(-2.356015139287,54.449145888983,
    62.863547674376));
#1955 = CARTESIAN_POINT('',(-2.448693949109,54.445087947171,
    62.744040340302));
#1956 = CARTESIAN_POINT('',(-2.53288203845,54.441229079813,
    62.621172052502));
#1957 = CARTESIAN_POINT('',(-2.60879050453,54.437617115477,
    62.495490215457));
#1958 = CARTESIAN_POINT('',(-2.676621417658,54.434290021871,
    62.367455665698));
#1959 = CARTESIAN_POINT('',(-2.736557437259,54.431277632965,
    62.237458262713));
#1960 = CARTESIAN_POINT('',(-2.849715614724,54.42547976182,
    61.952089515876));
#1961 = CARTESIAN_POINT('',(-2.900119012591,54.42281745858,
    61.796121169862));
#1962 = CARTESIAN_POINT('',(-2.940195687485,54.420653946984,
    61.638482316552));
#1963 = CARTESIAN_POINT('',(-2.970128011531,54.419015302945,
    61.479646679161));
#1964 = CARTESIAN_POINT('',(-2.990041558612,54.417917639872,
    61.320031637863));
#1965 = CARTESIAN_POINT('',(-3.,54.417368550859,61.160023038284));
#1966 = CARTESIAN_POINT('',(-3.,54.417368550859,60.835060926139));
#1967 = CARTESIAN_POINT('',(-2.989420292702,54.417951906668,
    60.670138642564));
#1968 = CARTESIAN_POINT('',(-2.968264887483,54.419118001206,
    60.505647305867));
#1969 = CARTESIAN_POINT('',(-2.936461732819,54.420858303487,
    60.342008846501));
#1970 = CARTESIAN_POINT('',(-2.893868883146,54.423154637924,
    60.179681958478));
#1971 = CARTESIAN_POINT('',(-2.840280665723,54.425977453635,
    60.019191089058));
#1972 = CARTESIAN_POINT('',(-2.721727737606,54.43202265182,
    59.730270355158));
#1973 = CARTESIAN_POINT('',(-2.660297204776,54.435093103089,
    59.601064761824));
#1974 = CARTESIAN_POINT('',(-2.590986628064,54.438471303214,
    59.473877926516));
#1975 = CARTESIAN_POINT('',(-2.513611765767,54.442126595468,
    59.349105631054));
#1976 = CARTESIAN_POINT('',(-2.427970378255,54.446020099095,
    59.227215667185));
#1977 = CARTESIAN_POINT('',(-2.333852850821,54.450102997674,
    59.10876334277));
#1978 = CARTESIAN_POINT('',(-2.063103655173,54.461195888245,
    58.807577732381));
#1979 = CARTESIAN_POINT('',(-1.870492538865,54.468482054394,
    58.630026451934));
#1980 = CARTESIAN_POINT('',(-1.653071166506,54.475859619699,
    58.466634844853));
#1981 = CARTESIAN_POINT('',(-1.411549498805,54.482906790071,
    58.322311713121));
#1982 = CARTESIAN_POINT('',(-1.147450079878,54.48912317489,
    58.201971596054));
#1983 = CARTESIAN_POINT('',(-0.863169063464,54.493995677802,
    58.110561860522));
#1984 = CARTESIAN_POINT('',(-0.224252531464,54.500586176266,
    57.988682013677));
#1985 = CARTESIAN_POINT('',(0.125543171656,54.501749582491,
    57.968800360484));
#1986 = CARTESIAN_POINT('',(0.476230923588,54.50017083482,
    57.997228901381));
#1987 = CARTESIAN_POINT('',(0.816040367584,54.495869619363,
    58.073005592842));
#1988 = CARTESIAN_POINT('',(1.133902307266,54.489512375893,
    58.190712620276));
#1989 = CARTESIAN_POINT('',(1.421079579744,54.482048154044,
    58.340697367734));
#1990 = CARTESIAN_POINT('',(1.768256476943,54.471391906409,
    58.573747862672));
#1991 = CARTESIAN_POINT('',(1.860114169321,54.468373943742,
    58.641699773714));
#1992 = CARTESIAN_POINT('',(1.947616070627,54.465323583732,
    58.712677202445));
#1993 = CARTESIAN_POINT('',(2.03078196731,54.462270251167,
    58.786368163767));
#1994 = CARTESIAN_POINT('',(2.109643475215,54.459240533243,
    58.862484313752));
#1995 = CARTESIAN_POINT('',(2.184243528544,54.456258077327,
    58.940760699432));
#1996 = CARTESIAN_POINT('',(2.474385204854,54.444244786892,
    59.271305986966));
#1997 = CARTESIAN_POINT('',(2.6532959987,54.435800590103,59.540544407481
    ));
#1998 = CARTESIAN_POINT('',(2.793517914856,54.42861473049,
    59.822122033229));
#1999 = CARTESIAN_POINT('',(2.897114192032,54.423040658421,
    60.111646544826));
#2000 = CARTESIAN_POINT('',(2.965662538079,54.419261540179,
    60.406017948945));
#2001 = CARTESIAN_POINT('',(2.999999970285,54.417368552497,
    60.702848976135));
#2002 = CARTESIAN_POINT('',(3.,54.417368550859,61.));
#2003 = PCURVE('',#157,#2004);
#2004 = DEFINITIONAL_REPRESENTATION('',(#2005),#2080);
#2005 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2006,#2007,#2008,#2009,#2010,
    #2011,#2012,#2013,#2014,#2015,#2016,#2017,#2018,#2019,#2020,#2021,
    #2022,#2023,#2024,#2025,#2026,#2027,#2028,#2029,#2030,#2031,#2032,
    #2033,#2034,#2035,#2036,#2037,#2038,#2039,#2040,#2041,#2042,#2043,
    #2044,#2045,#2046,#2047,#2048,#2049,#2050,#2051,#2052,#2053,#2054,
    #2055,#2056,#2057,#2058,#2059,#2060,#2061,#2062,#2063,#2064,#2065,
    #2066,#2067,#2068,#2069,#2070,#2071,#2072,#2073,#2074,#2075,#2076,
    #2077,#2078,#2079),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#2006 = CARTESIAN_POINT('',(4.767462688522,-61.));
#2007 = CARTESIAN_POINT('',(4.767462687968,-61.30167540498));
#2008 = CARTESIAN_POINT('',(4.766812326879,-61.60300677243));
#2009 = CARTESIAN_POINT('',(4.765514138064,-61.90176882878));
#2010 = CARTESIAN_POINT('',(4.763551852125,-62.19546636029));
#2011 = CARTESIAN_POINT('',(4.760894895079,-62.4808334929));
#2012 = CARTESIAN_POINT('',(4.757503587222,-62.75320895181));
#2013 = CARTESIAN_POINT('',(4.750252950328,-63.19242226762));
#2014 = CARTESIAN_POINT('',(4.746716291797,-63.36997354806));
#2015 = CARTESIAN_POINT('',(4.742724660827,-63.53336515514));
#2016 = CARTESIAN_POINT('',(4.738291288821,-63.67768828687));
#2017 = CARTESIAN_POINT('',(4.733444230811,-63.79802840394));
#2018 = CARTESIAN_POINT('',(4.728227421597,-63.88943813947));
#2019 = CARTESIAN_POINT('',(4.716503558799,-64.01131798641));
#2020 = CARTESIAN_POINT('',(4.710085409349,-64.03119963928));
#2021 = CARTESIAN_POINT('',(4.703651028338,-64.00277109886));
#2022 = CARTESIAN_POINT('',(4.697415834757,-63.92699440723));
#2023 = CARTESIAN_POINT('',(4.691582507732,-63.80928737931));
#2024 = CARTESIAN_POINT('',(4.686311479483,-63.65930263254));
#2025 = CARTESIAN_POINT('',(4.679938260771,-63.42625212442));
#2026 = CARTESIAN_POINT('',(4.678251893344,-63.35830022311));
#2027 = CARTESIAN_POINT('',(4.676645383796,-63.28732280723));
#2028 = CARTESIAN_POINT('',(4.67511838019,-63.21363184808));
#2029 = CARTESIAN_POINT('',(4.673670315223,-63.1375156862));
#2030 = CARTESIAN_POINT('',(4.672300414636,-63.05923928615));
#2031 = CARTESIAN_POINT('',(4.669145941704,-62.86354767437));
#2032 = CARTESIAN_POINT('',(4.667443788496,-62.7440403403));
#2033 = CARTESIAN_POINT('',(4.665897435835,-62.6211720525));
#2034 = CARTESIAN_POINT('',(4.664503047061,-62.49549021545));
#2035 = CARTESIAN_POINT('',(4.663256948212,-62.36745566569));
#2036 = CARTESIAN_POINT('',(4.662155817213,-62.23745826271));
#2037 = CARTESIAN_POINT('',(4.6600767968,-61.95208951587));
#2038 = CARTESIAN_POINT('',(4.659150675622,-61.79612116986));
#2039 = CARTESIAN_POINT('',(4.658414253761,-61.63848231655));
#2040 = CARTESIAN_POINT('',(4.657864214439,-61.47964667916));
#2041 = CARTESIAN_POINT('',(4.657498273369,-61.32003163786));
#2042 = CARTESIAN_POINT('',(4.657315272247,-61.16002303828));
#2043 = CARTESIAN_POINT('',(4.657315272247,-60.83506092613));
#2044 = CARTESIAN_POINT('',(4.657509690045,-60.67013864256));
#2045 = CARTESIAN_POINT('',(4.657898452116,-60.50564730586));
#2046 = CARTESIAN_POINT('',(4.658482869241,-60.3420088465));
#2047 = CARTESIAN_POINT('',(4.659265523551,-60.17968195847));
#2048 = CARTESIAN_POINT('',(4.660250155587,-60.01919108905));
#2049 = CARTESIAN_POINT('',(4.662428264855,-59.73027035515));
#2050 = CARTESIAN_POINT('',(4.663556837017,-59.60106476182));
#2051 = CARTESIAN_POINT('',(4.66483009894,-59.47387792651));
#2052 = CARTESIAN_POINT('',(4.666251401337,-59.34910563105));
#2053 = CARTESIAN_POINT('',(4.667824421024,-59.22721566718));
#2054 = CARTESIAN_POINT('',(4.669552967427,-59.10876334277));
#2055 = CARTESIAN_POINT('',(4.674525010442,-58.80757773238));
#2056 = CARTESIAN_POINT('',(4.678061668972,-58.63002645193));
#2057 = CARTESIAN_POINT('',(4.682053299942,-58.46663484485));
#2058 = CARTESIAN_POINT('',(4.686486671948,-58.32231171312));
#2059 = CARTESIAN_POINT('',(4.691333729958,-58.20197159605));
#2060 = CARTESIAN_POINT('',(4.696550539172,-58.11056186052));
#2061 = CARTESIAN_POINT('',(4.708274401961,-57.98868201367));
#2062 = CARTESIAN_POINT('',(4.714692551429,-57.96880036048));
#2063 = CARTESIAN_POINT('',(4.721126932439,-57.99722890138));
#2064 = CARTESIAN_POINT('',(4.727362126004,-58.07300559284));
#2065 = CARTESIAN_POINT('',(4.73319545303,-58.19071262027));
#2066 = CARTESIAN_POINT('',(4.738466481294,-58.34069736773));
#2067 = CARTESIAN_POINT('',(4.744839699645,-58.57374786267));
#2068 = CARTESIAN_POINT('',(4.746526067399,-58.64169977371));
#2069 = CARTESIAN_POINT('',(4.748132577219,-58.71267720244));
#2070 = CARTESIAN_POINT('',(4.74965958079,-58.78636816376));
#2071 = CARTESIAN_POINT('',(4.751107645502,-58.86248431375));
#2072 = CARTESIAN_POINT('',(4.752477545901,-58.94076069943));
#2073 = CARTESIAN_POINT('',(4.757805807817,-59.27130598696));
#2074 = CARTESIAN_POINT('',(4.761092174158,-59.54054440748));
#2075 = CARTESIAN_POINT('',(4.763668412478,-59.82212203322));
#2076 = CARTESIAN_POINT('',(4.765572008639,-60.11164654482));
#2077 = CARTESIAN_POINT('',(4.766831686705,-60.40601794894));
#2078 = CARTESIAN_POINT('',(4.767462687976,-60.70284897613));
#2079 = CARTESIAN_POINT('',(4.767462688522,-61.));
#2080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2081 = PCURVE('',#2082,#2087);
#2082 = CYLINDRICAL_SURFACE('',#2083,3.);
#2083 = AXIS2_PLACEMENT_3D('',#2084,#2085,#2086);
#2084 = CARTESIAN_POINT('',(0.,-1.354E-14,61.));
#2085 = DIRECTION('',(0.,-1.,-2.2E-16));
#2086 = DIRECTION('',(1.,0.,0.));
#2087 = DEFINITIONAL_REPRESENTATION('',(#2088),#2163);
#2088 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2089,#2090,#2091,#2092,#2093,
    #2094,#2095,#2096,#2097,#2098,#2099,#2100,#2101,#2102,#2103,#2104,
    #2105,#2106,#2107,#2108,#2109,#2110,#2111,#2112,#2113,#2114,#2115,
    #2116,#2117,#2118,#2119,#2120,#2121,#2122,#2123,#2124,#2125,#2126,
    #2127,#2128,#2129,#2130,#2131,#2132,#2133,#2134,#2135,#2136,#2137,
    #2138,#2139,#2140,#2141,#2142,#2143,#2144,#2145,#2146,#2147,#2148,
    #2149,#2150,#2151,#2152,#2153,#2154,#2155,#2156,#2157,#2158,#2159,
    #2160,#2161,#2162),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#2089 = CARTESIAN_POINT('',(0.,-54.41736855085));
#2090 = CARTESIAN_POINT('',(0.100558468328,-54.41736855252));
#2091 = CARTESIAN_POINT('',(0.201002027823,-54.4193196163));
#2092 = CARTESIAN_POINT('',(0.302251297155,-54.42321428433));
#2093 = CARTESIAN_POINT('',(0.40511389169,-54.42895587581));
#2094 = CARTESIAN_POINT('',(0.510445044941,-54.43635029771));
#2095 = CARTESIAN_POINT('',(0.619108432665,-54.44501933438));
#2096 = CARTESIAN_POINT('',(0.815972134213,-54.46119588824));
#2097 = CARTESIAN_POINT('',(0.90290874239,-54.46848205439));
#2098 = CARTESIAN_POINT('',(0.992821338449,-54.47585961969));
#2099 = CARTESIAN_POINT('',(1.085628608031,-54.48290679007));
#2100 = CARTESIAN_POINT('',(1.181357706856,-54.48912317489));
#2101 = CARTESIAN_POINT('',(1.280149684488,-54.4939956778));
#2102 = CARTESIAN_POINT('',(1.496962207852,-54.50058617627));
#2103 = CARTESIAN_POINT('',(1.612736968494,-54.50174958247));
#2104 = CARTESIAN_POINT('',(1.728250627632,-54.50017083483));
#2105 = CARTESIAN_POINT('',(1.842378609009,-54.49586961936));
#2106 = CARTESIAN_POINT('',(1.953885566946,-54.48951237587));
#2107 = CARTESIAN_POINT('',(2.061229755628,-54.48204815405));
#2108 = CARTESIAN_POINT('',(2.200611067507,-54.47139190581));
#2109 = CARTESIAN_POINT('',(2.238657882334,-54.46837394364));
#2110 = CARTESIAN_POINT('',(2.276155319147,-54.46532358416));
#2111 = CARTESIAN_POINT('',(2.313132474971,-54.46227025161));
#2112 = CARTESIAN_POINT('',(2.349615447365,-54.4592405332));
#2113 = CARTESIAN_POINT('',(2.385628923445,-54.4562580768));
#2114 = CARTESIAN_POINT('',(2.472424141459,-54.44914588898));
#2115 = CARTESIAN_POINT('',(2.52274221575,-54.44508794717));
#2116 = CARTESIAN_POINT('',(2.572254214675,-54.44122907981));
#2117 = CARTESIAN_POINT('',(2.621054948465,-54.43761711547));
#2118 = CARTESIAN_POINT('',(2.669234291286,-54.43429002187));
#2119 = CARTESIAN_POINT('',(2.716879657862,-54.43127763296));
#2120 = CARTESIAN_POINT('',(2.819208156552,-54.42547976182));
#2121 = CARTESIAN_POINT('',(2.873729915203,-54.42281745858));
#2122 = CARTESIAN_POINT('',(2.927773716383,-54.42065394698));
#2123 = CARTESIAN_POINT('',(2.981462504303,-54.41901530294));
#2124 = CARTESIAN_POINT('',(3.034915524425,-54.41791763987));
#2125 = CARTESIAN_POINT('',(3.088251640828,-54.41736855085));
#2126 = CARTESIAN_POINT('',(3.196572344877,-54.41736855085));
#2127 = CARTESIAN_POINT('',(3.251546336094,-54.41795190666));
#2128 = CARTESIAN_POINT('',(3.306648370688,-54.4191180012));
#2129 = CARTESIAN_POINT('',(3.362008743141,-54.42085830348));
#2130 = CARTESIAN_POINT('',(3.417758180445,-54.42315463792));
#2131 = CARTESIAN_POINT('',(3.474031723575,-54.42597745363));
#2132 = CARTESIAN_POINT('',(3.578131028324,-54.43202265182));
#2133 = CARTESIAN_POINT('',(3.62574881089,-54.43509310308));
#2134 = CARTESIAN_POINT('',(3.67391287984,-54.43847130321));
#2135 = CARTESIAN_POINT('',(3.722710212757,-54.44212659546));
#2136 = CARTESIAN_POINT('',(3.772230317841,-54.44602009909));
#2137 = CARTESIAN_POINT('',(3.822567710118,-54.45010299767));
#2138 = CARTESIAN_POINT('',(3.957564787803,-54.46119588824));
#2139 = CARTESIAN_POINT('',(4.04450139598,-54.46848205439));
#2140 = CARTESIAN_POINT('',(4.134413992039,-54.47585961969));
#2141 = CARTESIAN_POINT('',(4.227221261621,-54.48290679007));
#2142 = CARTESIAN_POINT('',(4.322950360446,-54.48912317489));
#2143 = CARTESIAN_POINT('',(4.421742338078,-54.4939956778));
#2144 = CARTESIAN_POINT('',(4.638554861273,-54.50058617626));
#2145 = CARTESIAN_POINT('',(4.754329622252,-54.50174958249));
#2146 = CARTESIAN_POINT('',(4.86984328134,-54.50017083482));
#2147 = CARTESIAN_POINT('',(4.983971262504,-54.49586961936));
#2148 = CARTESIAN_POINT('',(5.095478220347,-54.48951237589));
#2149 = CARTESIAN_POINT('',(5.202822409381,-54.48204815404));
#2150 = CARTESIAN_POINT('',(5.34220371338,-54.4713919064));
#2151 = CARTESIAN_POINT('',(5.380250534847,-54.46837394374));
#2152 = CARTESIAN_POINT('',(5.417747978255,-54.46532358373));
#2153 = CARTESIAN_POINT('',(5.454725134082,-54.46227025116));
#2154 = CARTESIAN_POINT('',(5.491208100326,-54.45924053324));
#2155 = CARTESIAN_POINT('',(5.527221570641,-54.45625807732));
#2156 = CARTESIAN_POINT('',(5.673828520394,-54.44424478689));
#2157 = CARTESIAN_POINT('',(5.780618336297,-54.4358005901));
#2158 = CARTESIAN_POINT('',(5.884226831582,-54.42861473049));
#2159 = CARTESIAN_POINT('',(5.985479521324,-54.42304065842));
#2160 = CARTESIAN_POINT('',(6.085191489599,-54.41926154017));
#2161 = CARTESIAN_POINT('',(6.184134965891,-54.41736855249));
#2162 = CARTESIAN_POINT('',(6.28318530718,-54.41736855085));
#2163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2164 = ADVANCED_FACE('',(#2165),#183,.T.);
#2165 = FACE_BOUND('',#2166,.F.);
#2166 = EDGE_LOOP('',(#2167,#2168,#2189,#2190));
#2167 = ORIENTED_EDGE('',*,*,#908,.T.);
#2168 = ORIENTED_EDGE('',*,*,#2169,.T.);
#2169 = EDGE_CURVE('',#881,#200,#2170,.T.);
#2170 = SURFACE_CURVE('',#2171,(#2175,#2182),.PCURVE_S1.);
#2171 = LINE('',#2172,#2173);
#2172 = CARTESIAN_POINT('',(2.5,43.749285708455,70.));
#2173 = VECTOR('',#2174,1.);
#2174 = DIRECTION('',(0.,1.,0.));
#2175 = PCURVE('',#183,#2176);
#2176 = DEFINITIONAL_REPRESENTATION('',(#2177),#2181);
#2177 = LINE('',#2178,#2179);
#2178 = CARTESIAN_POINT('',(-1.570796326795,0.));
#2179 = VECTOR('',#2180,1.);
#2180 = DIRECTION('',(-0.,1.));
#2181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2182 = PCURVE('',#239,#2183);
#2183 = DEFINITIONAL_REPRESENTATION('',(#2184),#2188);
#2184 = LINE('',#2185,#2186);
#2185 = CARTESIAN_POINT('',(2.5,-12.62977458842));
#2186 = VECTOR('',#2187,1.);
#2187 = DIRECTION('',(0.,1.));
#2188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2189 = ORIENTED_EDGE('',*,*,#199,.F.);
#2190 = ORIENTED_EDGE('',*,*,#169,.F.);
#2191 = ADVANCED_FACE('',(#2192),#239,.T.);
#2192 = FACE_BOUND('',#2193,.T.);
#2193 = EDGE_LOOP('',(#2194,#2195,#2216,#2217));
#2194 = ORIENTED_EDGE('',*,*,#223,.T.);
#2195 = ORIENTED_EDGE('',*,*,#2196,.T.);
#2196 = EDGE_CURVE('',#224,#714,#2197,.T.);
#2197 = SURFACE_CURVE('',#2198,(#2202,#2209),.PCURVE_S1.);
#2198 = LINE('',#2199,#2200);
#2199 = CARTESIAN_POINT('',(-2.5,64.5,70.));
#2200 = VECTOR('',#2201,1.);
#2201 = DIRECTION('',(0.,-1.,0.));
#2202 = PCURVE('',#239,#2203);
#2203 = DEFINITIONAL_REPRESENTATION('',(#2204),#2208);
#2204 = LINE('',#2205,#2206);
#2205 = CARTESIAN_POINT('',(-2.5,8.120939703118));
#2206 = VECTOR('',#2207,1.);
#2207 = DIRECTION('',(0.,-1.));
#2208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2209 = PCURVE('',#268,#2210);
#2210 = DEFINITIONAL_REPRESENTATION('',(#2211),#2215);
#2211 = LINE('',#2212,#2213);
#2212 = CARTESIAN_POINT('',(0.,0.));
#2213 = VECTOR('',#2214,1.);
#2214 = DIRECTION('',(0.,1.));
#2215 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2216 = ORIENTED_EDGE('',*,*,#880,.T.);
#2217 = ORIENTED_EDGE('',*,*,#2169,.T.);
#2218 = ADVANCED_FACE('',(#2219),#268,.T.);
#2219 = FACE_BOUND('',#2220,.T.);
#2220 = EDGE_LOOP('',(#2221,#2222,#2243,#2244));
#2221 = ORIENTED_EDGE('',*,*,#251,.T.);
#2222 = ORIENTED_EDGE('',*,*,#2223,.T.);
#2223 = EDGE_CURVE('',#252,#691,#2224,.T.);
#2224 = SURFACE_CURVE('',#2225,(#2229,#2236),.PCURVE_S1.);
#2225 = LINE('',#2226,#2227);
#2226 = CARTESIAN_POINT('',(-32.5,64.5,40.));
#2227 = VECTOR('',#2228,1.);
#2228 = DIRECTION('',(0.,-1.,0.));
#2229 = PCURVE('',#268,#2230);
#2230 = DEFINITIONAL_REPRESENTATION('',(#2231),#2235);
#2231 = LINE('',#2232,#2233);
#2232 = CARTESIAN_POINT('',(1.570796326795,0.));
#2233 = VECTOR('',#2234,1.);
#2234 = DIRECTION('',(0.,1.));
#2235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2236 = PCURVE('',#296,#2237);
#2237 = DEFINITIONAL_REPRESENTATION('',(#2238),#2242);
#2238 = LINE('',#2239,#2240);
#2239 = CARTESIAN_POINT('',(0.,-40.));
#2240 = VECTOR('',#2241,1.);
#2241 = DIRECTION('',(1.,0.));
#2242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2243 = ORIENTED_EDGE('',*,*,#713,.F.);
#2244 = ORIENTED_EDGE('',*,*,#2196,.F.);
#2245 = ADVANCED_FACE('',(#2246),#296,.F.);
#2246 = FACE_BOUND('',#2247,.F.);
#2247 = EDGE_LOOP('',(#2248,#2249,#2250,#2251,#2279,#2300));
#2248 = ORIENTED_EDGE('',*,*,#280,.T.);
#2249 = ORIENTED_EDGE('',*,*,#2223,.T.);
#2250 = ORIENTED_EDGE('',*,*,#690,.F.);
#2251 = ORIENTED_EDGE('',*,*,#2252,.F.);
#2252 = EDGE_CURVE('',#2253,#667,#2255,.T.);
#2253 = VERTEX_POINT('',#2254);
#2254 = CARTESIAN_POINT('',(-32.5,43.749285708455,0.));
#2255 = SURFACE_CURVE('',#2256,(#2260,#2267),.PCURVE_S1.);
#2256 = LINE('',#2257,#2258);
#2257 = CARTESIAN_POINT('',(-32.5,43.749285708455,0.));
#2258 = VECTOR('',#2259,1.);
#2259 = DIRECTION('',(0.,0.,1.));
#2260 = PCURVE('',#296,#2261);
#2261 = DEFINITIONAL_REPRESENTATION('',(#2262),#2266);
#2262 = LINE('',#2263,#2264);
#2263 = CARTESIAN_POINT('',(20.750714291545,0.));
#2264 = VECTOR('',#2265,1.);
#2265 = DIRECTION('',(0.,-1.));
#2266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2267 = PCURVE('',#2268,#2273);
#2268 = CYLINDRICAL_SURFACE('',#2269,54.5);
#2269 = AXIS2_PLACEMENT_3D('',#2270,#2271,#2272);
#2270 = CARTESIAN_POINT('',(0.,0.,0.));
#2271 = DIRECTION('',(-0.,-0.,-1.));
#2272 = DIRECTION('',(1.,0.,0.));
#2273 = DEFINITIONAL_REPRESENTATION('',(#2274),#2278);
#2274 = LINE('',#2275,#2276);
#2275 = CARTESIAN_POINT('',(-2.20971812738,0.));
#2276 = VECTOR('',#2277,1.);
#2277 = DIRECTION('',(-0.,-1.));
#2278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2279 = ORIENTED_EDGE('',*,*,#2280,.F.);
#2280 = EDGE_CURVE('',#485,#2253,#2281,.T.);
#2281 = SURFACE_CURVE('',#2282,(#2286,#2293),.PCURVE_S1.);
#2282 = LINE('',#2283,#2284);
#2283 = CARTESIAN_POINT('',(-32.5,64.5,0.));
#2284 = VECTOR('',#2285,1.);
#2285 = DIRECTION('',(0.,-1.,0.));
#2286 = PCURVE('',#296,#2287);
#2287 = DEFINITIONAL_REPRESENTATION('',(#2288),#2292);
#2288 = LINE('',#2289,#2290);
#2289 = CARTESIAN_POINT('',(0.,0.));
#2290 = VECTOR('',#2291,1.);
#2291 = DIRECTION('',(1.,0.));
#2292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2293 = PCURVE('',#500,#2294);
#2294 = DEFINITIONAL_REPRESENTATION('',(#2295),#2299);
#2295 = LINE('',#2296,#2297);
#2296 = CARTESIAN_POINT('',(-32.5,8.120939703118));
#2297 = VECTOR('',#2298,1.);
#2298 = DIRECTION('',(0.,-1.));
#2299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2300 = ORIENTED_EDGE('',*,*,#512,.F.);
#2301 = ADVANCED_FACE('',(#2302),#348,.F.);
#2302 = FACE_BOUND('',#2303,.F.);
#2303 = EDGE_LOOP('',(#2304,#2333,#2354,#2355));
#2304 = ORIENTED_EDGE('',*,*,#2305,.F.);
#2305 = EDGE_CURVE('',#2306,#2306,#2308,.T.);
#2306 = VERTEX_POINT('',#2307);
#2307 = CARTESIAN_POINT('',(18.845508075689,58.,31.));
#2308 = SURFACE_CURVE('',#2309,(#2314,#2321),.PCURVE_S1.);
#2309 = CIRCLE('',#2310,1.525);
#2310 = AXIS2_PLACEMENT_3D('',#2311,#2312,#2313);
#2311 = CARTESIAN_POINT('',(17.320508075689,58.,31.));
#2312 = DIRECTION('',(0.,-1.,-2.2E-16));
#2313 = DIRECTION('',(1.,0.,0.));
#2314 = PCURVE('',#348,#2315);
#2315 = DEFINITIONAL_REPRESENTATION('',(#2316),#2320);
#2316 = LINE('',#2317,#2318);
#2317 = CARTESIAN_POINT('',(0.,-58.));
#2318 = VECTOR('',#2319,1.);
#2319 = DIRECTION('',(1.,0.));
#2320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2321 = PCURVE('',#2322,#2327);
#2322 = PLANE('',#2323);
#2323 = AXIS2_PLACEMENT_3D('',#2324,#2325,#2326);
#2324 = CARTESIAN_POINT('',(17.320508075689,58.,31.));
#2325 = DIRECTION('',(0.,-1.,-2.2E-16));
#2326 = DIRECTION('',(0.,2.2E-16,-1.));
#2327 = DEFINITIONAL_REPRESENTATION('',(#2328),#2332);
#2328 = CIRCLE('',#2329,1.525);
#2329 = AXIS2_PLACEMENT_2D('',#2330,#2331);
#2330 = CARTESIAN_POINT('',(3.552713678801E-15,0.));
#2331 = DIRECTION('',(0.,1.));
#2332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2333 = ORIENTED_EDGE('',*,*,#2334,.T.);
#2334 = EDGE_CURVE('',#2306,#332,#2335,.T.);
#2335 = SEAM_CURVE('',#2336,(#2340,#2347),.PCURVE_S1.);
#2336 = LINE('',#2337,#2338);
#2337 = CARTESIAN_POINT('',(18.845508075689,-6.88E-15,31.));
#2338 = VECTOR('',#2339,1.);
#2339 = DIRECTION('',(0.,1.,2.2E-16));
#2340 = PCURVE('',#348,#2341);
#2341 = DEFINITIONAL_REPRESENTATION('',(#2342),#2346);
#2342 = LINE('',#2343,#2344);
#2343 = CARTESIAN_POINT('',(6.28318530718,0.));
#2344 = VECTOR('',#2345,1.);
#2345 = DIRECTION('',(0.,-1.));
#2346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2347 = PCURVE('',#348,#2348);
#2348 = DEFINITIONAL_REPRESENTATION('',(#2349),#2353);
#2349 = LINE('',#2350,#2351);
#2350 = CARTESIAN_POINT('',(0.,0.));
#2351 = VECTOR('',#2352,1.);
#2352 = DIRECTION('',(0.,-1.));
#2353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2354 = ORIENTED_EDGE('',*,*,#331,.T.);
#2355 = ORIENTED_EDGE('',*,*,#2334,.F.);
#2356 = ADVANCED_FACE('',(#2357),#383,.F.);
#2357 = FACE_BOUND('',#2358,.F.);
#2358 = EDGE_LOOP('',(#2359,#2382,#2409,#2410));
#2359 = ORIENTED_EDGE('',*,*,#2360,.T.);
#2360 = EDGE_CURVE('',#363,#2361,#2363,.T.);
#2361 = VERTEX_POINT('',#2362);
#2362 = CARTESIAN_POINT('',(-11.5,62.7,41.));
#2363 = SEAM_CURVE('',#2364,(#2368,#2375),.PCURVE_S1.);
#2364 = LINE('',#2365,#2366);
#2365 = CARTESIAN_POINT('',(-11.5,64.5,41.));
#2366 = VECTOR('',#2367,1.);
#2367 = DIRECTION('',(0.,-1.,2.2E-16));
#2368 = PCURVE('',#383,#2369);
#2369 = DEFINITIONAL_REPRESENTATION('',(#2370),#2374);
#2370 = LINE('',#2371,#2372);
#2371 = CARTESIAN_POINT('',(0.,0.));
#2372 = VECTOR('',#2373,1.);
#2373 = DIRECTION('',(0.,-1.));
#2374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2375 = PCURVE('',#383,#2376);
#2376 = DEFINITIONAL_REPRESENTATION('',(#2377),#2381);
#2377 = LINE('',#2378,#2379);
#2378 = CARTESIAN_POINT('',(6.28318530718,0.));
#2379 = VECTOR('',#2380,1.);
#2380 = DIRECTION('',(0.,-1.));
#2381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2382 = ORIENTED_EDGE('',*,*,#2383,.T.);
#2383 = EDGE_CURVE('',#2361,#2361,#2384,.T.);
#2384 = SURFACE_CURVE('',#2385,(#2390,#2397),.PCURVE_S1.);
#2385 = CIRCLE('',#2386,11.5);
#2386 = AXIS2_PLACEMENT_3D('',#2387,#2388,#2389);
#2387 = CARTESIAN_POINT('',(0.,62.7,41.));
#2388 = DIRECTION('',(0.,1.,-0.));
#2389 = DIRECTION('',(-1.,0.,0.));
#2390 = PCURVE('',#383,#2391);
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = LINE('',#2393,#2394);
#2393 = CARTESIAN_POINT('',(0.,-1.8));
#2394 = VECTOR('',#2395,1.);
#2395 = DIRECTION('',(1.,0.));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = PCURVE('',#2398,#2403);
#2398 = PLANE('',#2399);
#2399 = AXIS2_PLACEMENT_3D('',#2400,#2401,#2402);
#2400 = CARTESIAN_POINT('',(7.1E-16,62.7,41.));
#2401 = DIRECTION('',(4.1E-16,1.,-7.1E-16));
#2402 = DIRECTION('',(0.,7.1E-16,1.));
#2403 = DEFINITIONAL_REPRESENTATION('',(#2404),#2408);
#2404 = CIRCLE('',#2405,11.5);
#2405 = AXIS2_PLACEMENT_2D('',#2406,#2407);
#2406 = CARTESIAN_POINT('',(-7.105427357601E-15,-7.1E-16));
#2407 = DIRECTION('',(0.,-1.));
#2408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2409 = ORIENTED_EDGE('',*,*,#2360,.F.);
#2410 = ORIENTED_EDGE('',*,*,#362,.F.);
#2411 = ADVANCED_FACE('',(#2412),#414,.F.);
#2412 = FACE_BOUND('',#2413,.F.);
#2413 = EDGE_LOOP('',(#2414,#2443,#2464,#2465));
#2414 = ORIENTED_EDGE('',*,*,#2415,.F.);
#2415 = EDGE_CURVE('',#2416,#2416,#2418,.T.);
#2416 = VERTEX_POINT('',#2417);
#2417 = CARTESIAN_POINT('',(1.525,58.,61.));
#2418 = SURFACE_CURVE('',#2419,(#2424,#2431),.PCURVE_S1.);
#2419 = CIRCLE('',#2420,1.525);
#2420 = AXIS2_PLACEMENT_3D('',#2421,#2422,#2423);
#2421 = CARTESIAN_POINT('',(0.,58.,61.));
#2422 = DIRECTION('',(0.,-1.,-2.2E-16));
#2423 = DIRECTION('',(1.,0.,0.));
#2424 = PCURVE('',#414,#2425);
#2425 = DEFINITIONAL_REPRESENTATION('',(#2426),#2430);
#2426 = LINE('',#2427,#2428);
#2427 = CARTESIAN_POINT('',(0.,-58.));
#2428 = VECTOR('',#2429,1.);
#2429 = DIRECTION('',(1.,0.));
#2430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2431 = PCURVE('',#2432,#2437);
#2432 = PLANE('',#2433);
#2433 = AXIS2_PLACEMENT_3D('',#2434,#2435,#2436);
#2434 = CARTESIAN_POINT('',(-2.3E-16,58.,61.));
#2435 = DIRECTION('',(-0.,-1.,-2.2E-16));
#2436 = DIRECTION('',(0.,2.2E-16,-1.));
#2437 = DEFINITIONAL_REPRESENTATION('',(#2438),#2442);
#2438 = CIRCLE('',#2439,1.525);
#2439 = AXIS2_PLACEMENT_2D('',#2440,#2441);
#2440 = CARTESIAN_POINT('',(7.105427357601E-15,2.3E-16));
#2441 = DIRECTION('',(0.,1.));
#2442 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2443 = ORIENTED_EDGE('',*,*,#2444,.T.);
#2444 = EDGE_CURVE('',#2416,#398,#2445,.T.);
#2445 = SEAM_CURVE('',#2446,(#2450,#2457),.PCURVE_S1.);
#2446 = LINE('',#2447,#2448);
#2447 = CARTESIAN_POINT('',(1.525,-1.354E-14,61.));
#2448 = VECTOR('',#2449,1.);
#2449 = DIRECTION('',(0.,1.,2.2E-16));
#2450 = PCURVE('',#414,#2451);
#2451 = DEFINITIONAL_REPRESENTATION('',(#2452),#2456);
#2452 = LINE('',#2453,#2454);
#2453 = CARTESIAN_POINT('',(6.28318530718,0.));
#2454 = VECTOR('',#2455,1.);
#2455 = DIRECTION('',(0.,-1.));
#2456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2457 = PCURVE('',#414,#2458);
#2458 = DEFINITIONAL_REPRESENTATION('',(#2459),#2463);
#2459 = LINE('',#2460,#2461);
#2460 = CARTESIAN_POINT('',(0.,0.));
#2461 = VECTOR('',#2462,1.);
#2462 = DIRECTION('',(0.,-1.));
#2463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2464 = ORIENTED_EDGE('',*,*,#397,.T.);
#2465 = ORIENTED_EDGE('',*,*,#2444,.F.);
#2466 = ADVANCED_FACE('',(#2467),#445,.F.);
#2467 = FACE_BOUND('',#2468,.F.);
#2468 = EDGE_LOOP('',(#2469,#2498,#2519,#2520));
#2469 = ORIENTED_EDGE('',*,*,#2470,.F.);
#2470 = EDGE_CURVE('',#2471,#2471,#2473,.T.);
#2471 = VERTEX_POINT('',#2472);
#2472 = CARTESIAN_POINT('',(-15.79550807568,58.,31.));
#2473 = SURFACE_CURVE('',#2474,(#2479,#2486),.PCURVE_S1.);
#2474 = CIRCLE('',#2475,1.525);
#2475 = AXIS2_PLACEMENT_3D('',#2476,#2477,#2478);
#2476 = CARTESIAN_POINT('',(-17.32050807568,58.,31.));
#2477 = DIRECTION('',(0.,-1.,-2.2E-16));
#2478 = DIRECTION('',(1.,0.,0.));
#2479 = PCURVE('',#445,#2480);
#2480 = DEFINITIONAL_REPRESENTATION('',(#2481),#2485);
#2481 = LINE('',#2482,#2483);
#2482 = CARTESIAN_POINT('',(0.,-58.));
#2483 = VECTOR('',#2484,1.);
#2484 = DIRECTION('',(1.,0.));
#2485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2486 = PCURVE('',#2487,#2492);
#2487 = PLANE('',#2488);
#2488 = AXIS2_PLACEMENT_3D('',#2489,#2490,#2491);
#2489 = CARTESIAN_POINT('',(-17.32050807568,58.,31.));
#2490 = DIRECTION('',(-0.,-1.,-2.2E-16));
#2491 = DIRECTION('',(0.,2.2E-16,-1.));
#2492 = DEFINITIONAL_REPRESENTATION('',(#2493),#2497);
#2493 = CIRCLE('',#2494,1.525);
#2494 = AXIS2_PLACEMENT_2D('',#2495,#2496);
#2495 = CARTESIAN_POINT('',(-3.552713678801E-15,0.));
#2496 = DIRECTION('',(0.,1.));
#2497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2498 = ORIENTED_EDGE('',*,*,#2499,.T.);
#2499 = EDGE_CURVE('',#2471,#429,#2500,.T.);
#2500 = SEAM_CURVE('',#2501,(#2505,#2512),.PCURVE_S1.);
#2501 = LINE('',#2502,#2503);
#2502 = CARTESIAN_POINT('',(-15.79550807568,-6.88E-15,31.));
#2503 = VECTOR('',#2504,1.);
#2504 = DIRECTION('',(0.,1.,2.2E-16));
#2505 = PCURVE('',#445,#2506);
#2506 = DEFINITIONAL_REPRESENTATION('',(#2507),#2511);
#2507 = LINE('',#2508,#2509);
#2508 = CARTESIAN_POINT('',(6.28318530718,0.));
#2509 = VECTOR('',#2510,1.);
#2510 = DIRECTION('',(0.,-1.));
#2511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2512 = PCURVE('',#445,#2513);
#2513 = DEFINITIONAL_REPRESENTATION('',(#2514),#2518);
#2514 = LINE('',#2515,#2516);
#2515 = CARTESIAN_POINT('',(0.,0.));
#2516 = VECTOR('',#2517,1.);
#2517 = DIRECTION('',(0.,-1.));
#2518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2519 = ORIENTED_EDGE('',*,*,#428,.T.);
#2520 = ORIENTED_EDGE('',*,*,#2499,.F.);
#2521 = ADVANCED_FACE('',(#2522),#500,.F.);
#2522 = FACE_BOUND('',#2523,.F.);
#2523 = EDGE_LOOP('',(#2524,#2525,#2526));
#2524 = ORIENTED_EDGE('',*,*,#484,.T.);
#2525 = ORIENTED_EDGE('',*,*,#2280,.T.);
#2526 = ORIENTED_EDGE('',*,*,#2527,.T.);
#2527 = EDGE_CURVE('',#2253,#462,#2528,.T.);
#2528 = SURFACE_CURVE('',#2529,(#2534,#2545),.PCURVE_S1.);
#2529 = CIRCLE('',#2530,54.5);
#2530 = AXIS2_PLACEMENT_3D('',#2531,#2532,#2533);
#2531 = CARTESIAN_POINT('',(0.,0.,0.));
#2532 = DIRECTION('',(0.,0.,-1.));
#2533 = DIRECTION('',(1.,0.,0.));
#2534 = PCURVE('',#500,#2535);
#2535 = DEFINITIONAL_REPRESENTATION('',(#2536),#2544);
#2536 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2537,#2538,#2539,#2540,
#2541,#2542,#2543),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2537 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#2538 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#2539 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#2540 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#2541 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#2542 = CARTESIAN_POINT('',(54.5,38.017708715621));
#2543 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#2544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2545 = PCURVE('',#562,#2546);
#2546 = DEFINITIONAL_REPRESENTATION('',(#2547),#2555);
#2547 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2548,#2549,#2550,#2551,
#2552,#2553,#2554),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2548 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#2549 = CARTESIAN_POINT('',(54.5,-94.3967690125));
#2550 = CARTESIAN_POINT('',(-27.25,-47.19838450625));
#2551 = CARTESIAN_POINT('',(-109.,-1.754865011071E-14));
#2552 = CARTESIAN_POINT('',(-27.25,47.198384506252));
#2553 = CARTESIAN_POINT('',(54.5,94.396769012504));
#2554 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#2555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2556 = ADVANCED_FACE('',(#2557,#2828,#3080,#3111,#3142,#3173,#3208,
    #3239,#3270,#3305,#3336,#3367,#3402,#3437),#562,.F.);
#2557 = FACE_BOUND('',#2558,.F.);
#2558 = EDGE_LOOP('',(#2559,#2589,#2618,#2639,#2640,#2641,#2642,#2666,
    #2694,#2723,#2746,#2774,#2802));
#2559 = ORIENTED_EDGE('',*,*,#2560,.T.);
#2560 = EDGE_CURVE('',#2561,#2563,#2565,.T.);
#2561 = VERTEX_POINT('',#2562);
#2562 = CARTESIAN_POINT('',(15.5,-50.,0.));
#2563 = VERTEX_POINT('',#2564);
#2564 = CARTESIAN_POINT('',(21.685248442201,-50.,0.));
#2565 = SURFACE_CURVE('',#2566,(#2570,#2577),.PCURVE_S1.);
#2566 = LINE('',#2567,#2568);
#2567 = CARTESIAN_POINT('',(-22.5,-50.,0.));
#2568 = VECTOR('',#2569,1.);
#2569 = DIRECTION('',(1.,0.,0.));
#2570 = PCURVE('',#562,#2571);
#2571 = DEFINITIONAL_REPRESENTATION('',(#2572),#2576);
#2572 = LINE('',#2573,#2574);
#2573 = CARTESIAN_POINT('',(-22.5,-50.));
#2574 = VECTOR('',#2575,1.);
#2575 = DIRECTION('',(1.,0.));
#2576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2577 = PCURVE('',#2578,#2583);
#2578 = PLANE('',#2579);
#2579 = AXIS2_PLACEMENT_3D('',#2580,#2581,#2582);
#2580 = CARTESIAN_POINT('',(-45.,-50.,15.));
#2581 = DIRECTION('',(0.,-1.,0.));
#2582 = DIRECTION('',(1.,0.,0.));
#2583 = DEFINITIONAL_REPRESENTATION('',(#2584),#2588);
#2584 = LINE('',#2585,#2586);
#2585 = CARTESIAN_POINT('',(22.5,-15.));
#2586 = VECTOR('',#2587,1.);
#2587 = DIRECTION('',(1.,0.));
#2588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2589 = ORIENTED_EDGE('',*,*,#2590,.T.);
#2590 = EDGE_CURVE('',#2563,#2591,#2593,.T.);
#2591 = VERTEX_POINT('',#2592);
#2592 = CARTESIAN_POINT('',(51.,-19.21587885057,0.));
#2593 = SURFACE_CURVE('',#2594,(#2599,#2606),.PCURVE_S1.);
#2594 = CIRCLE('',#2595,54.5);
#2595 = AXIS2_PLACEMENT_3D('',#2596,#2597,#2598);
#2596 = CARTESIAN_POINT('',(0.,0.,0.));
#2597 = DIRECTION('',(0.,0.,1.));
#2598 = DIRECTION('',(1.,0.,0.));
#2599 = PCURVE('',#562,#2600);
#2600 = DEFINITIONAL_REPRESENTATION('',(#2601),#2605);
#2601 = CIRCLE('',#2602,54.5);
#2602 = AXIS2_PLACEMENT_2D('',#2603,#2604);
#2603 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2604 = DIRECTION('',(1.,0.));
#2605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2606 = PCURVE('',#2607,#2612);
#2607 = CYLINDRICAL_SURFACE('',#2608,54.5);
#2608 = AXIS2_PLACEMENT_3D('',#2609,#2610,#2611);
#2609 = CARTESIAN_POINT('',(0.,0.,0.));
#2610 = DIRECTION('',(-0.,-0.,-1.));
#2611 = DIRECTION('',(1.,0.,0.));
#2612 = DEFINITIONAL_REPRESENTATION('',(#2613),#2617);
#2613 = LINE('',#2614,#2615);
#2614 = CARTESIAN_POINT('',(-0.,0.));
#2615 = VECTOR('',#2616,1.);
#2616 = DIRECTION('',(-1.,0.));
#2617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2618 = ORIENTED_EDGE('',*,*,#2619,.T.);
#2619 = EDGE_CURVE('',#2591,#583,#2620,.T.);
#2620 = SURFACE_CURVE('',#2621,(#2625,#2632),.PCURVE_S1.);
#2621 = LINE('',#2622,#2623);
#2622 = CARTESIAN_POINT('',(51.,-25.,0.));
#2623 = VECTOR('',#2624,1.);
#2624 = DIRECTION('',(0.,1.,0.));
#2625 = PCURVE('',#562,#2626);
#2626 = DEFINITIONAL_REPRESENTATION('',(#2627),#2631);
#2627 = LINE('',#2628,#2629);
#2628 = CARTESIAN_POINT('',(51.,-25.));
#2629 = VECTOR('',#2630,1.);
#2630 = DIRECTION('',(0.,1.));
#2631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2632 = PCURVE('',#622,#2633);
#2633 = DEFINITIONAL_REPRESENTATION('',(#2634),#2638);
#2634 = LINE('',#2635,#2636);
#2635 = CARTESIAN_POINT('',(25.,-15.));
#2636 = VECTOR('',#2637,1.);
#2637 = DIRECTION('',(1.,0.));
#2638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2639 = ORIENTED_EDGE('',*,*,#582,.T.);
#2640 = ORIENTED_EDGE('',*,*,#543,.F.);
#2641 = ORIENTED_EDGE('',*,*,#2527,.F.);
#2642 = ORIENTED_EDGE('',*,*,#2643,.T.);
#2643 = EDGE_CURVE('',#2253,#2644,#2646,.T.);
#2644 = VERTEX_POINT('',#2645);
#2645 = CARTESIAN_POINT('',(-51.,19.215878850576,0.));
#2646 = SURFACE_CURVE('',#2647,(#2652,#2659),.PCURVE_S1.);
#2647 = CIRCLE('',#2648,54.5);
#2648 = AXIS2_PLACEMENT_3D('',#2649,#2650,#2651);
#2649 = CARTESIAN_POINT('',(0.,0.,0.));
#2650 = DIRECTION('',(0.,0.,1.));
#2651 = DIRECTION('',(1.,0.,0.));
#2652 = PCURVE('',#562,#2653);
#2653 = DEFINITIONAL_REPRESENTATION('',(#2654),#2658);
#2654 = CIRCLE('',#2655,54.5);
#2655 = AXIS2_PLACEMENT_2D('',#2656,#2657);
#2656 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2657 = DIRECTION('',(1.,0.));
#2658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2659 = PCURVE('',#2268,#2660);
#2660 = DEFINITIONAL_REPRESENTATION('',(#2661),#2665);
#2661 = LINE('',#2662,#2663);
#2662 = CARTESIAN_POINT('',(-0.,0.));
#2663 = VECTOR('',#2664,1.);
#2664 = DIRECTION('',(-1.,0.));
#2665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2666 = ORIENTED_EDGE('',*,*,#2667,.T.);
#2667 = EDGE_CURVE('',#2644,#2668,#2670,.T.);
#2668 = VERTEX_POINT('',#2669);
#2669 = CARTESIAN_POINT('',(-51.,-19.21587885057,0.));
#2670 = SURFACE_CURVE('',#2671,(#2675,#2682),.PCURVE_S1.);
#2671 = LINE('',#2672,#2673);
#2672 = CARTESIAN_POINT('',(-51.,25.,0.));
#2673 = VECTOR('',#2674,1.);
#2674 = DIRECTION('',(0.,-1.,0.));
#2675 = PCURVE('',#562,#2676);
#2676 = DEFINITIONAL_REPRESENTATION('',(#2677),#2681);
#2677 = LINE('',#2678,#2679);
#2678 = CARTESIAN_POINT('',(-51.,25.));
#2679 = VECTOR('',#2680,1.);
#2680 = DIRECTION('',(0.,-1.));
#2681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2682 = PCURVE('',#2683,#2688);
#2683 = PLANE('',#2684);
#2684 = AXIS2_PLACEMENT_3D('',#2685,#2686,#2687);
#2685 = CARTESIAN_POINT('',(-51.,50.,15.));
#2686 = DIRECTION('',(-1.,0.,0.));
#2687 = DIRECTION('',(0.,-1.,0.));
#2688 = DEFINITIONAL_REPRESENTATION('',(#2689),#2693);
#2689 = LINE('',#2690,#2691);
#2690 = CARTESIAN_POINT('',(25.,-15.));
#2691 = VECTOR('',#2692,1.);
#2692 = DIRECTION('',(1.,0.));
#2693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2694 = ORIENTED_EDGE('',*,*,#2695,.T.);
#2695 = EDGE_CURVE('',#2668,#2696,#2698,.T.);
#2696 = VERTEX_POINT('',#2697);
#2697 = CARTESIAN_POINT('',(-21.6852484422,-50.,0.));
#2698 = SURFACE_CURVE('',#2699,(#2704,#2711),.PCURVE_S1.);
#2699 = CIRCLE('',#2700,54.5);
#2700 = AXIS2_PLACEMENT_3D('',#2701,#2702,#2703);
#2701 = CARTESIAN_POINT('',(0.,0.,0.));
#2702 = DIRECTION('',(0.,0.,1.));
#2703 = DIRECTION('',(1.,0.,0.));
#2704 = PCURVE('',#562,#2705);
#2705 = DEFINITIONAL_REPRESENTATION('',(#2706),#2710);
#2706 = CIRCLE('',#2707,54.5);
#2707 = AXIS2_PLACEMENT_2D('',#2708,#2709);
#2708 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2709 = DIRECTION('',(1.,0.));
#2710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2711 = PCURVE('',#2712,#2717);
#2712 = CYLINDRICAL_SURFACE('',#2713,54.5);
#2713 = AXIS2_PLACEMENT_3D('',#2714,#2715,#2716);
#2714 = CARTESIAN_POINT('',(0.,0.,0.));
#2715 = DIRECTION('',(-0.,-0.,-1.));
#2716 = DIRECTION('',(1.,0.,0.));
#2717 = DEFINITIONAL_REPRESENTATION('',(#2718),#2722);
#2718 = LINE('',#2719,#2720);
#2719 = CARTESIAN_POINT('',(-0.,0.));
#2720 = VECTOR('',#2721,1.);
#2721 = DIRECTION('',(-1.,0.));
#2722 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2723 = ORIENTED_EDGE('',*,*,#2724,.T.);
#2724 = EDGE_CURVE('',#2696,#2725,#2727,.T.);
#2725 = VERTEX_POINT('',#2726);
#2726 = CARTESIAN_POINT('',(-15.5,-50.,0.));
#2727 = SURFACE_CURVE('',#2728,(#2732,#2739),.PCURVE_S1.);
#2728 = LINE('',#2729,#2730);
#2729 = CARTESIAN_POINT('',(-22.5,-50.,0.));
#2730 = VECTOR('',#2731,1.);
#2731 = DIRECTION('',(1.,0.,0.));
#2732 = PCURVE('',#562,#2733);
#2733 = DEFINITIONAL_REPRESENTATION('',(#2734),#2738);
#2734 = LINE('',#2735,#2736);
#2735 = CARTESIAN_POINT('',(-22.5,-50.));
#2736 = VECTOR('',#2737,1.);
#2737 = DIRECTION('',(1.,0.));
#2738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2739 = PCURVE('',#2578,#2740);
#2740 = DEFINITIONAL_REPRESENTATION('',(#2741),#2745);
#2741 = LINE('',#2742,#2743);
#2742 = CARTESIAN_POINT('',(22.5,-15.));
#2743 = VECTOR('',#2744,1.);
#2744 = DIRECTION('',(1.,0.));
#2745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2746 = ORIENTED_EDGE('',*,*,#2747,.F.);
#2747 = EDGE_CURVE('',#2748,#2725,#2750,.T.);
#2748 = VERTEX_POINT('',#2749);
#2749 = CARTESIAN_POINT('',(-15.5,-10.,0.));
#2750 = SURFACE_CURVE('',#2751,(#2755,#2762),.PCURVE_S1.);
#2751 = LINE('',#2752,#2753);
#2752 = CARTESIAN_POINT('',(-15.5,-10.,0.));
#2753 = VECTOR('',#2754,1.);
#2754 = DIRECTION('',(0.,-1.,0.));
#2755 = PCURVE('',#562,#2756);
#2756 = DEFINITIONAL_REPRESENTATION('',(#2757),#2761);
#2757 = LINE('',#2758,#2759);
#2758 = CARTESIAN_POINT('',(-15.5,-10.));
#2759 = VECTOR('',#2760,1.);
#2760 = DIRECTION('',(0.,-1.));
#2761 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2762 = PCURVE('',#2763,#2768);
#2763 = PLANE('',#2764);
#2764 = AXIS2_PLACEMENT_3D('',#2765,#2766,#2767);
#2765 = CARTESIAN_POINT('',(-15.5,-10.,0.));
#2766 = DIRECTION('',(1.,0.,0.));
#2767 = DIRECTION('',(0.,-1.,0.));
#2768 = DEFINITIONAL_REPRESENTATION('',(#2769),#2773);
#2769 = LINE('',#2770,#2771);
#2770 = CARTESIAN_POINT('',(0.,0.));
#2771 = VECTOR('',#2772,1.);
#2772 = DIRECTION('',(1.,0.));
#2773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2774 = ORIENTED_EDGE('',*,*,#2775,.F.);
#2775 = EDGE_CURVE('',#2776,#2748,#2778,.T.);
#2776 = VERTEX_POINT('',#2777);
#2777 = CARTESIAN_POINT('',(15.5,-10.,0.));
#2778 = SURFACE_CURVE('',#2779,(#2783,#2790),.PCURVE_S1.);
#2779 = LINE('',#2780,#2781);
#2780 = CARTESIAN_POINT('',(15.5,-10.,0.));
#2781 = VECTOR('',#2782,1.);
#2782 = DIRECTION('',(-1.,0.,0.));
#2783 = PCURVE('',#562,#2784);
#2784 = DEFINITIONAL_REPRESENTATION('',(#2785),#2789);
#2785 = LINE('',#2786,#2787);
#2786 = CARTESIAN_POINT('',(15.5,-10.));
#2787 = VECTOR('',#2788,1.);
#2788 = DIRECTION('',(-1.,0.));
#2789 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2790 = PCURVE('',#2791,#2796);
#2791 = PLANE('',#2792);
#2792 = AXIS2_PLACEMENT_3D('',#2793,#2794,#2795);
#2793 = CARTESIAN_POINT('',(15.5,-10.,0.));
#2794 = DIRECTION('',(0.,-1.,0.));
#2795 = DIRECTION('',(-1.,0.,0.));
#2796 = DEFINITIONAL_REPRESENTATION('',(#2797),#2801);
#2797 = LINE('',#2798,#2799);
#2798 = CARTESIAN_POINT('',(0.,-0.));
#2799 = VECTOR('',#2800,1.);
#2800 = DIRECTION('',(1.,0.));
#2801 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2802 = ORIENTED_EDGE('',*,*,#2803,.F.);
#2803 = EDGE_CURVE('',#2561,#2776,#2804,.T.);
#2804 = SURFACE_CURVE('',#2805,(#2809,#2816),.PCURVE_S1.);
#2805 = LINE('',#2806,#2807);
#2806 = CARTESIAN_POINT('',(15.5,-60.,0.));
#2807 = VECTOR('',#2808,1.);
#2808 = DIRECTION('',(0.,1.,0.));
#2809 = PCURVE('',#562,#2810);
#2810 = DEFINITIONAL_REPRESENTATION('',(#2811),#2815);
#2811 = LINE('',#2812,#2813);
#2812 = CARTESIAN_POINT('',(15.5,-60.));
#2813 = VECTOR('',#2814,1.);
#2814 = DIRECTION('',(0.,1.));
#2815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2816 = PCURVE('',#2817,#2822);
#2817 = PLANE('',#2818);
#2818 = AXIS2_PLACEMENT_3D('',#2819,#2820,#2821);
#2819 = CARTESIAN_POINT('',(15.5,-60.,0.));
#2820 = DIRECTION('',(-1.,0.,0.));
#2821 = DIRECTION('',(0.,1.,0.));
#2822 = DEFINITIONAL_REPRESENTATION('',(#2823),#2827);
#2823 = LINE('',#2824,#2825);
#2824 = CARTESIAN_POINT('',(0.,0.));
#2825 = VECTOR('',#2826,1.);
#2826 = DIRECTION('',(1.,0.));
#2827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2828 = FACE_BOUND('',#2829,.F.);
#2829 = EDGE_LOOP('',(#2830,#2861,#2889,#2917,#2945,#2974,#2998,#3026,
    #3054));
#2830 = ORIENTED_EDGE('',*,*,#2831,.F.);
#2831 = EDGE_CURVE('',#2832,#2834,#2836,.T.);
#2832 = VERTEX_POINT('',#2833);
#2833 = CARTESIAN_POINT('',(-15.5,30.,0.));
#2834 = VERTEX_POINT('',#2835);
#2835 = CARTESIAN_POINT('',(-15.5,14.,0.));
#2836 = SURFACE_CURVE('',#2837,(#2842,#2849),.PCURVE_S1.);
#2837 = CIRCLE('',#2838,10.);
#2838 = AXIS2_PLACEMENT_3D('',#2839,#2840,#2841);
#2839 = CARTESIAN_POINT('',(-21.5,22.,0.));
#2840 = DIRECTION('',(0.,0.,1.));
#2841 = DIRECTION('',(1.,0.,0.));
#2842 = PCURVE('',#562,#2843);
#2843 = DEFINITIONAL_REPRESENTATION('',(#2844),#2848);
#2844 = CIRCLE('',#2845,10.);
#2845 = AXIS2_PLACEMENT_2D('',#2846,#2847);
#2846 = CARTESIAN_POINT('',(-21.5,22.));
#2847 = DIRECTION('',(1.,0.));
#2848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2849 = PCURVE('',#2850,#2855);
#2850 = CYLINDRICAL_SURFACE('',#2851,10.);
#2851 = AXIS2_PLACEMENT_3D('',#2852,#2853,#2854);
#2852 = CARTESIAN_POINT('',(-21.5,22.,15.));
#2853 = DIRECTION('',(0.,0.,1.));
#2854 = DIRECTION('',(1.,0.,0.));
#2855 = DEFINITIONAL_REPRESENTATION('',(#2856),#2860);
#2856 = LINE('',#2857,#2858);
#2857 = CARTESIAN_POINT('',(0.,-15.));
#2858 = VECTOR('',#2859,1.);
#2859 = DIRECTION('',(1.,0.));
#2860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2861 = ORIENTED_EDGE('',*,*,#2862,.F.);
#2862 = EDGE_CURVE('',#2863,#2832,#2865,.T.);
#2863 = VERTEX_POINT('',#2864);
#2864 = CARTESIAN_POINT('',(-15.5,46.,0.));
#2865 = SURFACE_CURVE('',#2866,(#2870,#2877),.PCURVE_S1.);
#2866 = LINE('',#2867,#2868);
#2867 = CARTESIAN_POINT('',(-15.5,46.,0.));
#2868 = VECTOR('',#2869,1.);
#2869 = DIRECTION('',(0.,-1.,0.));
#2870 = PCURVE('',#562,#2871);
#2871 = DEFINITIONAL_REPRESENTATION('',(#2872),#2876);
#2872 = LINE('',#2873,#2874);
#2873 = CARTESIAN_POINT('',(-15.5,46.));
#2874 = VECTOR('',#2875,1.);
#2875 = DIRECTION('',(0.,-1.));
#2876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2877 = PCURVE('',#2878,#2883);
#2878 = PLANE('',#2879);
#2879 = AXIS2_PLACEMENT_3D('',#2880,#2881,#2882);
#2880 = CARTESIAN_POINT('',(-15.5,46.,0.));
#2881 = DIRECTION('',(1.,0.,0.));
#2882 = DIRECTION('',(0.,-1.,0.));
#2883 = DEFINITIONAL_REPRESENTATION('',(#2884),#2888);
#2884 = LINE('',#2885,#2886);
#2885 = CARTESIAN_POINT('',(0.,0.));
#2886 = VECTOR('',#2887,1.);
#2887 = DIRECTION('',(1.,0.));
#2888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2889 = ORIENTED_EDGE('',*,*,#2890,.F.);
#2890 = EDGE_CURVE('',#2891,#2863,#2893,.T.);
#2891 = VERTEX_POINT('',#2892);
#2892 = CARTESIAN_POINT('',(15.5,46.,0.));
#2893 = SURFACE_CURVE('',#2894,(#2898,#2905),.PCURVE_S1.);
#2894 = LINE('',#2895,#2896);
#2895 = CARTESIAN_POINT('',(15.5,46.,0.));
#2896 = VECTOR('',#2897,1.);
#2897 = DIRECTION('',(-1.,0.,0.));
#2898 = PCURVE('',#562,#2899);
#2899 = DEFINITIONAL_REPRESENTATION('',(#2900),#2904);
#2900 = LINE('',#2901,#2902);
#2901 = CARTESIAN_POINT('',(15.5,46.));
#2902 = VECTOR('',#2903,1.);
#2903 = DIRECTION('',(-1.,0.));
#2904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2905 = PCURVE('',#2906,#2911);
#2906 = PLANE('',#2907);
#2907 = AXIS2_PLACEMENT_3D('',#2908,#2909,#2910);
#2908 = CARTESIAN_POINT('',(15.5,46.,0.));
#2909 = DIRECTION('',(0.,-1.,0.));
#2910 = DIRECTION('',(-1.,0.,0.));
#2911 = DEFINITIONAL_REPRESENTATION('',(#2912),#2916);
#2912 = LINE('',#2913,#2914);
#2913 = CARTESIAN_POINT('',(0.,-0.));
#2914 = VECTOR('',#2915,1.);
#2915 = DIRECTION('',(1.,0.));
#2916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2917 = ORIENTED_EDGE('',*,*,#2918,.F.);
#2918 = EDGE_CURVE('',#2919,#2891,#2921,.T.);
#2919 = VERTEX_POINT('',#2920);
#2920 = CARTESIAN_POINT('',(15.5,30.,0.));
#2921 = SURFACE_CURVE('',#2922,(#2926,#2933),.PCURVE_S1.);
#2922 = LINE('',#2923,#2924);
#2923 = CARTESIAN_POINT('',(15.5,5.,0.));
#2924 = VECTOR('',#2925,1.);
#2925 = DIRECTION('',(0.,1.,0.));
#2926 = PCURVE('',#562,#2927);
#2927 = DEFINITIONAL_REPRESENTATION('',(#2928),#2932);
#2928 = LINE('',#2929,#2930);
#2929 = CARTESIAN_POINT('',(15.5,5.));
#2930 = VECTOR('',#2931,1.);
#2931 = DIRECTION('',(0.,1.));
#2932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2933 = PCURVE('',#2934,#2939);
#2934 = PLANE('',#2935);
#2935 = AXIS2_PLACEMENT_3D('',#2936,#2937,#2938);
#2936 = CARTESIAN_POINT('',(15.5,5.,0.));
#2937 = DIRECTION('',(-1.,0.,0.));
#2938 = DIRECTION('',(0.,1.,0.));
#2939 = DEFINITIONAL_REPRESENTATION('',(#2940),#2944);
#2940 = LINE('',#2941,#2942);
#2941 = CARTESIAN_POINT('',(0.,0.));
#2942 = VECTOR('',#2943,1.);
#2943 = DIRECTION('',(1.,0.));
#2944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2945 = ORIENTED_EDGE('',*,*,#2946,.F.);
#2946 = EDGE_CURVE('',#2947,#2919,#2949,.T.);
#2947 = VERTEX_POINT('',#2948);
#2948 = CARTESIAN_POINT('',(31.5,22.,0.));
#2949 = SURFACE_CURVE('',#2950,(#2955,#2962),.PCURVE_S1.);
#2950 = CIRCLE('',#2951,10.);
#2951 = AXIS2_PLACEMENT_3D('',#2952,#2953,#2954);
#2952 = CARTESIAN_POINT('',(21.5,22.,0.));
#2953 = DIRECTION('',(0.,0.,1.));
#2954 = DIRECTION('',(1.,0.,0.));
#2955 = PCURVE('',#562,#2956);
#2956 = DEFINITIONAL_REPRESENTATION('',(#2957),#2961);
#2957 = CIRCLE('',#2958,10.);
#2958 = AXIS2_PLACEMENT_2D('',#2959,#2960);
#2959 = CARTESIAN_POINT('',(21.5,22.));
#2960 = DIRECTION('',(1.,0.));
#2961 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2962 = PCURVE('',#2963,#2968);
#2963 = CYLINDRICAL_SURFACE('',#2964,10.);
#2964 = AXIS2_PLACEMENT_3D('',#2965,#2966,#2967);
#2965 = CARTESIAN_POINT('',(21.5,22.,15.));
#2966 = DIRECTION('',(0.,0.,1.));
#2967 = DIRECTION('',(1.,0.,0.));
#2968 = DEFINITIONAL_REPRESENTATION('',(#2969),#2973);
#2969 = LINE('',#2970,#2971);
#2970 = CARTESIAN_POINT('',(0.,-15.));
#2971 = VECTOR('',#2972,1.);
#2972 = DIRECTION('',(1.,0.));
#2973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2974 = ORIENTED_EDGE('',*,*,#2975,.F.);
#2975 = EDGE_CURVE('',#2976,#2947,#2978,.T.);
#2976 = VERTEX_POINT('',#2977);
#2977 = CARTESIAN_POINT('',(15.5,14.,0.));
#2978 = SURFACE_CURVE('',#2979,(#2984,#2991),.PCURVE_S1.);
#2979 = CIRCLE('',#2980,10.);
#2980 = AXIS2_PLACEMENT_3D('',#2981,#2982,#2983);
#2981 = CARTESIAN_POINT('',(21.5,22.,0.));
#2982 = DIRECTION('',(0.,0.,1.));
#2983 = DIRECTION('',(1.,0.,0.));
#2984 = PCURVE('',#562,#2985);
#2985 = DEFINITIONAL_REPRESENTATION('',(#2986),#2990);
#2986 = CIRCLE('',#2987,10.);
#2987 = AXIS2_PLACEMENT_2D('',#2988,#2989);
#2988 = CARTESIAN_POINT('',(21.5,22.));
#2989 = DIRECTION('',(1.,0.));
#2990 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2991 = PCURVE('',#2963,#2992);
#2992 = DEFINITIONAL_REPRESENTATION('',(#2993),#2997);
#2993 = LINE('',#2994,#2995);
#2994 = CARTESIAN_POINT('',(0.,-15.));
#2995 = VECTOR('',#2996,1.);
#2996 = DIRECTION('',(1.,0.));
#2997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2998 = ORIENTED_EDGE('',*,*,#2999,.F.);
#2999 = EDGE_CURVE('',#3000,#2976,#3002,.T.);
#3000 = VERTEX_POINT('',#3001);
#3001 = CARTESIAN_POINT('',(15.5,5.,0.));
#3002 = SURFACE_CURVE('',#3003,(#3007,#3014),.PCURVE_S1.);
#3003 = LINE('',#3004,#3005);
#3004 = CARTESIAN_POINT('',(15.5,5.,0.));
#3005 = VECTOR('',#3006,1.);
#3006 = DIRECTION('',(0.,1.,0.));
#3007 = PCURVE('',#562,#3008);
#3008 = DEFINITIONAL_REPRESENTATION('',(#3009),#3013);
#3009 = LINE('',#3010,#3011);
#3010 = CARTESIAN_POINT('',(15.5,5.));
#3011 = VECTOR('',#3012,1.);
#3012 = DIRECTION('',(0.,1.));
#3013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3014 = PCURVE('',#3015,#3020);
#3015 = PLANE('',#3016);
#3016 = AXIS2_PLACEMENT_3D('',#3017,#3018,#3019);
#3017 = CARTESIAN_POINT('',(15.5,5.,0.));
#3018 = DIRECTION('',(-1.,0.,0.));
#3019 = DIRECTION('',(0.,1.,0.));
#3020 = DEFINITIONAL_REPRESENTATION('',(#3021),#3025);
#3021 = LINE('',#3022,#3023);
#3022 = CARTESIAN_POINT('',(0.,0.));
#3023 = VECTOR('',#3024,1.);
#3024 = DIRECTION('',(1.,0.));
#3025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3026 = ORIENTED_EDGE('',*,*,#3027,.F.);
#3027 = EDGE_CURVE('',#3028,#3000,#3030,.T.);
#3028 = VERTEX_POINT('',#3029);
#3029 = CARTESIAN_POINT('',(-15.5,5.,0.));
#3030 = SURFACE_CURVE('',#3031,(#3035,#3042),.PCURVE_S1.);
#3031 = LINE('',#3032,#3033);
#3032 = CARTESIAN_POINT('',(-15.5,5.,0.));
#3033 = VECTOR('',#3034,1.);
#3034 = DIRECTION('',(1.,0.,0.));
#3035 = PCURVE('',#562,#3036);
#3036 = DEFINITIONAL_REPRESENTATION('',(#3037),#3041);
#3037 = LINE('',#3038,#3039);
#3038 = CARTESIAN_POINT('',(-15.5,5.));
#3039 = VECTOR('',#3040,1.);
#3040 = DIRECTION('',(1.,0.));
#3041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3042 = PCURVE('',#3043,#3048);
#3043 = PLANE('',#3044);
#3044 = AXIS2_PLACEMENT_3D('',#3045,#3046,#3047);
#3045 = CARTESIAN_POINT('',(-15.5,5.,0.));
#3046 = DIRECTION('',(0.,1.,0.));
#3047 = DIRECTION('',(1.,0.,0.));
#3048 = DEFINITIONAL_REPRESENTATION('',(#3049),#3053);
#3049 = LINE('',#3050,#3051);
#3050 = CARTESIAN_POINT('',(0.,0.));
#3051 = VECTOR('',#3052,1.);
#3052 = DIRECTION('',(1.,0.));
#3053 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3054 = ORIENTED_EDGE('',*,*,#3055,.F.);
#3055 = EDGE_CURVE('',#2834,#3028,#3056,.T.);
#3056 = SURFACE_CURVE('',#3057,(#3061,#3068),.PCURVE_S1.);
#3057 = LINE('',#3058,#3059);
#3058 = CARTESIAN_POINT('',(-15.5,46.,0.));
#3059 = VECTOR('',#3060,1.);
#3060 = DIRECTION('',(0.,-1.,0.));
#3061 = PCURVE('',#562,#3062);
#3062 = DEFINITIONAL_REPRESENTATION('',(#3063),#3067);
#3063 = LINE('',#3064,#3065);
#3064 = CARTESIAN_POINT('',(-15.5,46.));
#3065 = VECTOR('',#3066,1.);
#3066 = DIRECTION('',(0.,-1.));
#3067 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3068 = PCURVE('',#3069,#3074);
#3069 = PLANE('',#3070);
#3070 = AXIS2_PLACEMENT_3D('',#3071,#3072,#3073);
#3071 = CARTESIAN_POINT('',(-15.5,46.,0.));
#3072 = DIRECTION('',(1.,0.,0.));
#3073 = DIRECTION('',(0.,-1.,0.));
#3074 = DEFINITIONAL_REPRESENTATION('',(#3075),#3079);
#3075 = LINE('',#3076,#3077);
#3076 = CARTESIAN_POINT('',(0.,0.));
#3077 = VECTOR('',#3078,1.);
#3078 = DIRECTION('',(1.,0.));
#3079 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3080 = FACE_BOUND('',#3081,.F.);
#3081 = EDGE_LOOP('',(#3082));
#3082 = ORIENTED_EDGE('',*,*,#3083,.F.);
#3083 = EDGE_CURVE('',#3084,#3084,#3086,.T.);
#3084 = VERTEX_POINT('',#3085);
#3085 = CARTESIAN_POINT('',(-31.,33.,0.));
#3086 = SURFACE_CURVE('',#3087,(#3092,#3099),.PCURVE_S1.);
#3087 = CIRCLE('',#3088,2.);
#3088 = AXIS2_PLACEMENT_3D('',#3089,#3090,#3091);
#3089 = CARTESIAN_POINT('',(-33.,33.,0.));
#3090 = DIRECTION('',(0.,0.,1.));
#3091 = DIRECTION('',(1.,0.,0.));
#3092 = PCURVE('',#562,#3093);
#3093 = DEFINITIONAL_REPRESENTATION('',(#3094),#3098);
#3094 = CIRCLE('',#3095,2.);
#3095 = AXIS2_PLACEMENT_2D('',#3096,#3097);
#3096 = CARTESIAN_POINT('',(-33.,33.));
#3097 = DIRECTION('',(1.,0.));
#3098 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3099 = PCURVE('',#3100,#3105);
#3100 = CYLINDRICAL_SURFACE('',#3101,2.);
#3101 = AXIS2_PLACEMENT_3D('',#3102,#3103,#3104);
#3102 = CARTESIAN_POINT('',(-33.,33.,15.));
#3103 = DIRECTION('',(0.,0.,1.));
#3104 = DIRECTION('',(1.,0.,0.));
#3105 = DEFINITIONAL_REPRESENTATION('',(#3106),#3110);
#3106 = LINE('',#3107,#3108);
#3107 = CARTESIAN_POINT('',(0.,-15.));
#3108 = VECTOR('',#3109,1.);
#3109 = DIRECTION('',(1.,0.));
#3110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3111 = FACE_BOUND('',#3112,.F.);
#3112 = EDGE_LOOP('',(#3113));
#3113 = ORIENTED_EDGE('',*,*,#3114,.F.);
#3114 = EDGE_CURVE('',#3115,#3115,#3117,.T.);
#3115 = VERTEX_POINT('',#3116);
#3116 = CARTESIAN_POINT('',(35.,33.,0.));
#3117 = SURFACE_CURVE('',#3118,(#3123,#3130),.PCURVE_S1.);
#3118 = CIRCLE('',#3119,2.);
#3119 = AXIS2_PLACEMENT_3D('',#3120,#3121,#3122);
#3120 = CARTESIAN_POINT('',(33.,33.,0.));
#3121 = DIRECTION('',(0.,0.,1.));
#3122 = DIRECTION('',(1.,0.,0.));
#3123 = PCURVE('',#562,#3124);
#3124 = DEFINITIONAL_REPRESENTATION('',(#3125),#3129);
#3125 = CIRCLE('',#3126,2.);
#3126 = AXIS2_PLACEMENT_2D('',#3127,#3128);
#3127 = CARTESIAN_POINT('',(33.,33.));
#3128 = DIRECTION('',(1.,0.));
#3129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3130 = PCURVE('',#3131,#3136);
#3131 = CYLINDRICAL_SURFACE('',#3132,2.);
#3132 = AXIS2_PLACEMENT_3D('',#3133,#3134,#3135);
#3133 = CARTESIAN_POINT('',(33.,33.,15.));
#3134 = DIRECTION('',(0.,0.,1.));
#3135 = DIRECTION('',(1.,0.,0.));
#3136 = DEFINITIONAL_REPRESENTATION('',(#3137),#3141);
#3137 = LINE('',#3138,#3139);
#3138 = CARTESIAN_POINT('',(0.,-15.));
#3139 = VECTOR('',#3140,1.);
#3140 = DIRECTION('',(1.,0.));
#3141 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3142 = FACE_BOUND('',#3143,.F.);
#3143 = EDGE_LOOP('',(#3144));
#3144 = ORIENTED_EDGE('',*,*,#3145,.F.);
#3145 = EDGE_CURVE('',#3146,#3146,#3148,.T.);
#3146 = VERTEX_POINT('',#3147);
#3147 = CARTESIAN_POINT('',(3.25,0.,0.));
#3148 = SURFACE_CURVE('',#3149,(#3154,#3161),.PCURVE_S1.);
#3149 = CIRCLE('',#3150,3.25);
#3150 = AXIS2_PLACEMENT_3D('',#3151,#3152,#3153);
#3151 = CARTESIAN_POINT('',(0.,0.,0.));
#3152 = DIRECTION('',(0.,0.,1.));
#3153 = DIRECTION('',(1.,0.,0.));
#3154 = PCURVE('',#562,#3155);
#3155 = DEFINITIONAL_REPRESENTATION('',(#3156),#3160);
#3156 = CIRCLE('',#3157,3.25);
#3157 = AXIS2_PLACEMENT_2D('',#3158,#3159);
#3158 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3159 = DIRECTION('',(1.,0.));
#3160 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3161 = PCURVE('',#3162,#3167);
#3162 = CYLINDRICAL_SURFACE('',#3163,3.25);
#3163 = AXIS2_PLACEMENT_3D('',#3164,#3165,#3166);
#3164 = CARTESIAN_POINT('',(0.,0.,0.));
#3165 = DIRECTION('',(-0.,-0.,-1.));
#3166 = DIRECTION('',(1.,0.,0.));
#3167 = DEFINITIONAL_REPRESENTATION('',(#3168),#3172);
#3168 = LINE('',#3169,#3170);
#3169 = CARTESIAN_POINT('',(-0.,0.));
#3170 = VECTOR('',#3171,1.);
#3171 = DIRECTION('',(-1.,0.));
#3172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3173 = FACE_BOUND('',#3174,.F.);
#3174 = EDGE_LOOP('',(#3175));
#3175 = ORIENTED_EDGE('',*,*,#3176,.T.);
#3176 = EDGE_CURVE('',#3177,#3177,#3179,.T.);
#3177 = VERTEX_POINT('',#3178);
#3178 = CARTESIAN_POINT('',(33.556276,-39.287605,0.));
#3179 = SURFACE_CURVE('',#3180,(#3185,#3196),.PCURVE_S1.);
#3180 = CIRCLE('',#3181,1.475);
#3181 = AXIS2_PLACEMENT_3D('',#3182,#3183,#3184);
#3182 = CARTESIAN_POINT('',(32.081276,-39.287605,0.));
#3183 = DIRECTION('',(0.,0.,-1.));
#3184 = DIRECTION('',(1.,0.,0.));
#3185 = PCURVE('',#562,#3186);
#3186 = DEFINITIONAL_REPRESENTATION('',(#3187),#3195);
#3187 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3188,#3189,#3190,#3191,
#3192,#3193,#3194),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3188 = CARTESIAN_POINT('',(33.556276,-39.287605));
#3189 = CARTESIAN_POINT('',(33.556276,-41.84237994116));
#3190 = CARTESIAN_POINT('',(31.343776,-40.56499247058));
#3191 = CARTESIAN_POINT('',(29.131276,-39.287605));
#3192 = CARTESIAN_POINT('',(31.343776,-38.01021752941));
#3193 = CARTESIAN_POINT('',(33.556276,-36.73283005883));
#3194 = CARTESIAN_POINT('',(33.556276,-39.287605));
#3195 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3196 = PCURVE('',#3197,#3202);
#3197 = CYLINDRICAL_SURFACE('',#3198,1.475);
#3198 = AXIS2_PLACEMENT_3D('',#3199,#3200,#3201);
#3199 = CARTESIAN_POINT('',(32.081276,-39.287605,0.));
#3200 = DIRECTION('',(0.,0.,-1.));
#3201 = DIRECTION('',(1.,0.,0.));
#3202 = DEFINITIONAL_REPRESENTATION('',(#3203),#3207);
#3203 = LINE('',#3204,#3205);
#3204 = CARTESIAN_POINT('',(0.,0.));
#3205 = VECTOR('',#3206,1.);
#3206 = DIRECTION('',(1.,0.));
#3207 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3208 = FACE_BOUND('',#3209,.F.);
#3209 = EDGE_LOOP('',(#3210));
#3210 = ORIENTED_EDGE('',*,*,#3211,.F.);
#3211 = EDGE_CURVE('',#3212,#3212,#3214,.T.);
#3212 = VERTEX_POINT('',#3213);
#3213 = CARTESIAN_POINT('',(35.,-33.,0.));
#3214 = SURFACE_CURVE('',#3215,(#3220,#3227),.PCURVE_S1.);
#3215 = CIRCLE('',#3216,2.);
#3216 = AXIS2_PLACEMENT_3D('',#3217,#3218,#3219);
#3217 = CARTESIAN_POINT('',(33.,-33.,0.));
#3218 = DIRECTION('',(0.,0.,1.));
#3219 = DIRECTION('',(1.,0.,0.));
#3220 = PCURVE('',#562,#3221);
#3221 = DEFINITIONAL_REPRESENTATION('',(#3222),#3226);
#3222 = CIRCLE('',#3223,2.);
#3223 = AXIS2_PLACEMENT_2D('',#3224,#3225);
#3224 = CARTESIAN_POINT('',(33.,-33.));
#3225 = DIRECTION('',(1.,0.));
#3226 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3227 = PCURVE('',#3228,#3233);
#3228 = CYLINDRICAL_SURFACE('',#3229,2.);
#3229 = AXIS2_PLACEMENT_3D('',#3230,#3231,#3232);
#3230 = CARTESIAN_POINT('',(33.,-33.,15.));
#3231 = DIRECTION('',(0.,0.,1.));
#3232 = DIRECTION('',(1.,0.,0.));
#3233 = DEFINITIONAL_REPRESENTATION('',(#3234),#3238);
#3234 = LINE('',#3235,#3236);
#3235 = CARTESIAN_POINT('',(0.,-15.));
#3236 = VECTOR('',#3237,1.);
#3237 = DIRECTION('',(1.,0.));
#3238 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3239 = FACE_BOUND('',#3240,.F.);
#3240 = EDGE_LOOP('',(#3241));
#3241 = ORIENTED_EDGE('',*,*,#3242,.F.);
#3242 = EDGE_CURVE('',#3243,#3243,#3245,.T.);
#3243 = VERTEX_POINT('',#3244);
#3244 = CARTESIAN_POINT('',(-31.,-33.,0.));
#3245 = SURFACE_CURVE('',#3246,(#3251,#3258),.PCURVE_S1.);
#3246 = CIRCLE('',#3247,2.);
#3247 = AXIS2_PLACEMENT_3D('',#3248,#3249,#3250);
#3248 = CARTESIAN_POINT('',(-33.,-33.,0.));
#3249 = DIRECTION('',(0.,0.,1.));
#3250 = DIRECTION('',(1.,0.,0.));
#3251 = PCURVE('',#562,#3252);
#3252 = DEFINITIONAL_REPRESENTATION('',(#3253),#3257);
#3253 = CIRCLE('',#3254,2.);
#3254 = AXIS2_PLACEMENT_2D('',#3255,#3256);
#3255 = CARTESIAN_POINT('',(-33.,-33.));
#3256 = DIRECTION('',(1.,0.));
#3257 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3258 = PCURVE('',#3259,#3264);
#3259 = CYLINDRICAL_SURFACE('',#3260,2.);
#3260 = AXIS2_PLACEMENT_3D('',#3261,#3262,#3263);
#3261 = CARTESIAN_POINT('',(-33.,-33.,15.));
#3262 = DIRECTION('',(0.,0.,1.));
#3263 = DIRECTION('',(1.,0.,0.));
#3264 = DEFINITIONAL_REPRESENTATION('',(#3265),#3269);
#3265 = LINE('',#3266,#3267);
#3266 = CARTESIAN_POINT('',(0.,-15.));
#3267 = VECTOR('',#3268,1.);
#3268 = DIRECTION('',(1.,0.));
#3269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3270 = FACE_BOUND('',#3271,.F.);
#3271 = EDGE_LOOP('',(#3272));
#3272 = ORIENTED_EDGE('',*,*,#3273,.T.);
#3273 = EDGE_CURVE('',#3274,#3274,#3276,.T.);
#3274 = VERTEX_POINT('',#3275);
#3275 = CARTESIAN_POINT('',(-30.606276,-39.287605,0.));
#3276 = SURFACE_CURVE('',#3277,(#3282,#3293),.PCURVE_S1.);
#3277 = CIRCLE('',#3278,1.475);
#3278 = AXIS2_PLACEMENT_3D('',#3279,#3280,#3281);
#3279 = CARTESIAN_POINT('',(-32.081276,-39.287605,0.));
#3280 = DIRECTION('',(0.,0.,-1.));
#3281 = DIRECTION('',(1.,0.,0.));
#3282 = PCURVE('',#562,#3283);
#3283 = DEFINITIONAL_REPRESENTATION('',(#3284),#3292);
#3284 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3285,#3286,#3287,#3288,
#3289,#3290,#3291),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3285 = CARTESIAN_POINT('',(-30.606276,-39.287605));
#3286 = CARTESIAN_POINT('',(-30.606276,-41.84237994116));
#3287 = CARTESIAN_POINT('',(-32.818776,-40.56499247058));
#3288 = CARTESIAN_POINT('',(-35.031276,-39.287605));
#3289 = CARTESIAN_POINT('',(-32.818776,-38.01021752941));
#3290 = CARTESIAN_POINT('',(-30.606276,-36.73283005883));
#3291 = CARTESIAN_POINT('',(-30.606276,-39.287605));
#3292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3293 = PCURVE('',#3294,#3299);
#3294 = CYLINDRICAL_SURFACE('',#3295,1.475);
#3295 = AXIS2_PLACEMENT_3D('',#3296,#3297,#3298);
#3296 = CARTESIAN_POINT('',(-32.081276,-39.287605,0.));
#3297 = DIRECTION('',(0.,0.,-1.));
#3298 = DIRECTION('',(1.,0.,0.));
#3299 = DEFINITIONAL_REPRESENTATION('',(#3300),#3304);
#3300 = LINE('',#3301,#3302);
#3301 = CARTESIAN_POINT('',(0.,0.));
#3302 = VECTOR('',#3303,1.);
#3303 = DIRECTION('',(1.,0.));
#3304 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3305 = FACE_BOUND('',#3306,.F.);
#3306 = EDGE_LOOP('',(#3307));
#3307 = ORIENTED_EDGE('',*,*,#3308,.F.);
#3308 = EDGE_CURVE('',#3309,#3309,#3311,.T.);
#3309 = VERTEX_POINT('',#3310);
#3310 = CARTESIAN_POINT('',(-21.5,-17.,0.));
#3311 = SURFACE_CURVE('',#3312,(#3317,#3324),.PCURVE_S1.);
#3312 = CIRCLE('',#3313,11.);
#3313 = AXIS2_PLACEMENT_3D('',#3314,#3315,#3316);
#3314 = CARTESIAN_POINT('',(-32.5,-17.,0.));
#3315 = DIRECTION('',(0.,0.,1.));
#3316 = DIRECTION('',(1.,0.,0.));
#3317 = PCURVE('',#562,#3318);
#3318 = DEFINITIONAL_REPRESENTATION('',(#3319),#3323);
#3319 = CIRCLE('',#3320,11.);
#3320 = AXIS2_PLACEMENT_2D('',#3321,#3322);
#3321 = CARTESIAN_POINT('',(-32.5,-17.));
#3322 = DIRECTION('',(1.,0.));
#3323 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3324 = PCURVE('',#3325,#3330);
#3325 = CYLINDRICAL_SURFACE('',#3326,11.);
#3326 = AXIS2_PLACEMENT_3D('',#3327,#3328,#3329);
#3327 = CARTESIAN_POINT('',(-32.5,-17.,15.));
#3328 = DIRECTION('',(0.,0.,1.));
#3329 = DIRECTION('',(1.,0.,0.));
#3330 = DEFINITIONAL_REPRESENTATION('',(#3331),#3335);
#3331 = LINE('',#3332,#3333);
#3332 = CARTESIAN_POINT('',(0.,-15.));
#3333 = VECTOR('',#3334,1.);
#3334 = DIRECTION('',(1.,0.));
#3335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3336 = FACE_BOUND('',#3337,.F.);
#3337 = EDGE_LOOP('',(#3338));
#3338 = ORIENTED_EDGE('',*,*,#3339,.F.);
#3339 = EDGE_CURVE('',#3340,#3340,#3342,.T.);
#3340 = VERTEX_POINT('',#3341);
#3341 = CARTESIAN_POINT('',(43.5,-17.,0.));
#3342 = SURFACE_CURVE('',#3343,(#3348,#3355),.PCURVE_S1.);
#3343 = CIRCLE('',#3344,11.);
#3344 = AXIS2_PLACEMENT_3D('',#3345,#3346,#3347);
#3345 = CARTESIAN_POINT('',(32.5,-17.,0.));
#3346 = DIRECTION('',(0.,0.,1.));
#3347 = DIRECTION('',(1.,0.,0.));
#3348 = PCURVE('',#562,#3349);
#3349 = DEFINITIONAL_REPRESENTATION('',(#3350),#3354);
#3350 = CIRCLE('',#3351,11.);
#3351 = AXIS2_PLACEMENT_2D('',#3352,#3353);
#3352 = CARTESIAN_POINT('',(32.5,-17.));
#3353 = DIRECTION('',(1.,0.));
#3354 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3355 = PCURVE('',#3356,#3361);
#3356 = CYLINDRICAL_SURFACE('',#3357,11.);
#3357 = AXIS2_PLACEMENT_3D('',#3358,#3359,#3360);
#3358 = CARTESIAN_POINT('',(32.5,-17.,15.));
#3359 = DIRECTION('',(0.,0.,1.));
#3360 = DIRECTION('',(1.,0.,0.));
#3361 = DEFINITIONAL_REPRESENTATION('',(#3362),#3366);
#3362 = LINE('',#3363,#3364);
#3363 = CARTESIAN_POINT('',(0.,-15.));
#3364 = VECTOR('',#3365,1.);
#3365 = DIRECTION('',(1.,0.));
#3366 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3367 = FACE_BOUND('',#3368,.F.);
#3368 = EDGE_LOOP('',(#3369));
#3369 = ORIENTED_EDGE('',*,*,#3370,.T.);
#3370 = EDGE_CURVE('',#3371,#3371,#3373,.T.);
#3371 = VERTEX_POINT('',#3372);
#3372 = CARTESIAN_POINT('',(1.475,50.726503,0.));
#3373 = SURFACE_CURVE('',#3374,(#3379,#3390),.PCURVE_S1.);
#3374 = CIRCLE('',#3375,1.475);
#3375 = AXIS2_PLACEMENT_3D('',#3376,#3377,#3378);
#3376 = CARTESIAN_POINT('',(0.,50.726503,0.));
#3377 = DIRECTION('',(0.,0.,-1.));
#3378 = DIRECTION('',(1.,0.,0.));
#3379 = PCURVE('',#562,#3380);
#3380 = DEFINITIONAL_REPRESENTATION('',(#3381),#3389);
#3381 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3382,#3383,#3384,#3385,
#3386,#3387,#3388),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3382 = CARTESIAN_POINT('',(1.475,50.726503));
#3383 = CARTESIAN_POINT('',(1.475,48.171728058836));
#3384 = CARTESIAN_POINT('',(-0.7375,49.449115529418));
#3385 = CARTESIAN_POINT('',(-2.95,50.726503));
#3386 = CARTESIAN_POINT('',(-0.7375,52.003890470582));
#3387 = CARTESIAN_POINT('',(1.475,53.281277941164));
#3388 = CARTESIAN_POINT('',(1.475,50.726503));
#3389 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3390 = PCURVE('',#3391,#3396);
#3391 = CYLINDRICAL_SURFACE('',#3392,1.475);
#3392 = AXIS2_PLACEMENT_3D('',#3393,#3394,#3395);
#3393 = CARTESIAN_POINT('',(0.,50.726503,0.));
#3394 = DIRECTION('',(0.,0.,-1.));
#3395 = DIRECTION('',(1.,0.,0.));
#3396 = DEFINITIONAL_REPRESENTATION('',(#3397),#3401);
#3397 = LINE('',#3398,#3399);
#3398 = CARTESIAN_POINT('',(0.,0.));
#3399 = VECTOR('',#3400,1.);
#3400 = DIRECTION('',(1.,0.));
#3401 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3402 = FACE_BOUND('',#3403,.F.);
#3403 = EDGE_LOOP('',(#3404));
#3404 = ORIENTED_EDGE('',*,*,#3405,.T.);
#3405 = EDGE_CURVE('',#3406,#3406,#3408,.T.);
#3406 = VERTEX_POINT('',#3407);
#3407 = CARTESIAN_POINT('',(-45.328799,0.,0.));
#3408 = SURFACE_CURVE('',#3409,(#3414,#3425),.PCURVE_S1.);
#3409 = CIRCLE('',#3410,1.475);
#3410 = AXIS2_PLACEMENT_3D('',#3411,#3412,#3413);
#3411 = CARTESIAN_POINT('',(-46.803799,0.,0.));
#3412 = DIRECTION('',(0.,0.,-1.));
#3413 = DIRECTION('',(1.,0.,0.));
#3414 = PCURVE('',#562,#3415);
#3415 = DEFINITIONAL_REPRESENTATION('',(#3416),#3424);
#3416 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3417,#3418,#3419,#3420,
#3421,#3422,#3423),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3417 = CARTESIAN_POINT('',(-45.328799,-4.2E-15));
#3418 = CARTESIAN_POINT('',(-45.328799,-2.554774941164));
#3419 = CARTESIAN_POINT('',(-47.541299,-1.277387470582));
#3420 = CARTESIAN_POINT('',(-49.753799,-4.561270805748E-15));
#3421 = CARTESIAN_POINT('',(-47.541299,1.277387470582));
#3422 = CARTESIAN_POINT('',(-45.328799,2.554774941164));
#3423 = CARTESIAN_POINT('',(-45.328799,-4.2E-15));
#3424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3425 = PCURVE('',#3426,#3431);
#3426 = CYLINDRICAL_SURFACE('',#3427,1.475);
#3427 = AXIS2_PLACEMENT_3D('',#3428,#3429,#3430);
#3428 = CARTESIAN_POINT('',(-46.803799,0.,0.));
#3429 = DIRECTION('',(0.,0.,-1.));
#3430 = DIRECTION('',(1.,0.,0.));
#3431 = DEFINITIONAL_REPRESENTATION('',(#3432),#3436);
#3432 = LINE('',#3433,#3434);
#3433 = CARTESIAN_POINT('',(0.,0.));
#3434 = VECTOR('',#3435,1.);
#3435 = DIRECTION('',(1.,0.));
#3436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3437 = FACE_BOUND('',#3438,.F.);
#3438 = EDGE_LOOP('',(#3439));
#3439 = ORIENTED_EDGE('',*,*,#3440,.T.);
#3440 = EDGE_CURVE('',#3441,#3441,#3443,.T.);
#3441 = VERTEX_POINT('',#3442);
#3442 = CARTESIAN_POINT('',(48.278799,0.,0.));
#3443 = SURFACE_CURVE('',#3444,(#3449,#3460),.PCURVE_S1.);
#3444 = CIRCLE('',#3445,1.475);
#3445 = AXIS2_PLACEMENT_3D('',#3446,#3447,#3448);
#3446 = CARTESIAN_POINT('',(46.803799,0.,0.));
#3447 = DIRECTION('',(0.,0.,-1.));
#3448 = DIRECTION('',(1.,0.,0.));
#3449 = PCURVE('',#562,#3450);
#3450 = DEFINITIONAL_REPRESENTATION('',(#3451),#3459);
#3451 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3452,#3453,#3454,#3455,
#3456,#3457,#3458),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3452 = CARTESIAN_POINT('',(48.278799,-4.2E-15));
#3453 = CARTESIAN_POINT('',(48.278799,-2.554774941164));
#3454 = CARTESIAN_POINT('',(46.066299,-1.277387470582));
#3455 = CARTESIAN_POINT('',(43.853799,-4.561270805748E-15));
#3456 = CARTESIAN_POINT('',(46.066299,1.277387470582));
#3457 = CARTESIAN_POINT('',(48.278799,2.554774941164));
#3458 = CARTESIAN_POINT('',(48.278799,-4.2E-15));
#3459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3460 = PCURVE('',#3461,#3466);
#3461 = CYLINDRICAL_SURFACE('',#3462,1.475);
#3462 = AXIS2_PLACEMENT_3D('',#3463,#3464,#3465);
#3463 = CARTESIAN_POINT('',(46.803799,0.,0.));
#3464 = DIRECTION('',(0.,0.,-1.));
#3465 = DIRECTION('',(1.,0.,0.));
#3466 = DEFINITIONAL_REPRESENTATION('',(#3467),#3471);
#3467 = LINE('',#3468,#3469);
#3468 = CARTESIAN_POINT('',(0.,0.));
#3469 = VECTOR('',#3470,1.);
#3470 = DIRECTION('',(1.,0.));
#3471 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3472 = ADVANCED_FACE('',(#3473,#3573,#3599,#3625,#3651),#649,.T.);
#3473 = FACE_BOUND('',#3474,.T.);
#3474 = EDGE_LOOP('',(#3475,#3505,#3526,#3527,#3528,#3552));
#3475 = ORIENTED_EDGE('',*,*,#3476,.T.);
#3476 = EDGE_CURVE('',#3477,#3479,#3481,.T.);
#3477 = VERTEX_POINT('',#3478);
#3478 = CARTESIAN_POINT('',(-51.,6.821226,15.));
#3479 = VERTEX_POINT('',#3480);
#3480 = CARTESIAN_POINT('',(51.,6.821226,15.));
#3481 = SURFACE_CURVE('',#3482,(#3486,#3493),.PCURVE_S1.);
#3482 = LINE('',#3483,#3484);
#3483 = CARTESIAN_POINT('',(-60.179985,6.821226,15.));
#3484 = VECTOR('',#3485,1.);
#3485 = DIRECTION('',(1.,0.,0.));
#3486 = PCURVE('',#649,#3487);
#3487 = DEFINITIONAL_REPRESENTATION('',(#3488),#3492);
#3488 = LINE('',#3489,#3490);
#3489 = CARTESIAN_POINT('',(-60.179985,6.821226));
#3490 = VECTOR('',#3491,1.);
#3491 = DIRECTION('',(1.,0.));
#3492 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3493 = PCURVE('',#3494,#3499);
#3494 = PLANE('',#3495);
#3495 = AXIS2_PLACEMENT_3D('',#3496,#3497,#3498);
#3496 = CARTESIAN_POINT('',(-3.21E-15,4.2E-15,15.));
#3497 = DIRECTION('',(0.,0.,1.));
#3498 = DIRECTION('',(1.,0.,0.));
#3499 = DEFINITIONAL_REPRESENTATION('',(#3500),#3504);
#3500 = LINE('',#3501,#3502);
#3501 = CARTESIAN_POINT('',(-60.179985,6.821226));
#3502 = VECTOR('',#3503,1.);
#3503 = DIRECTION('',(1.,0.));
#3504 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3505 = ORIENTED_EDGE('',*,*,#3506,.T.);
#3506 = EDGE_CURVE('',#3479,#607,#3507,.T.);
#3507 = SURFACE_CURVE('',#3508,(#3512,#3519),.PCURVE_S1.);
#3508 = LINE('',#3509,#3510);
#3509 = CARTESIAN_POINT('',(51.,-50.,15.));
#3510 = VECTOR('',#3511,1.);
#3511 = DIRECTION('',(0.,1.,0.));
#3512 = PCURVE('',#649,#3513);
#3513 = DEFINITIONAL_REPRESENTATION('',(#3514),#3518);
#3514 = LINE('',#3515,#3516);
#3515 = CARTESIAN_POINT('',(51.,-50.));
#3516 = VECTOR('',#3517,1.);
#3517 = DIRECTION('',(0.,1.));
#3518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3519 = PCURVE('',#622,#3520);
#3520 = DEFINITIONAL_REPRESENTATION('',(#3521),#3525);
#3521 = LINE('',#3522,#3523);
#3522 = CARTESIAN_POINT('',(0.,0.));
#3523 = VECTOR('',#3524,1.);
#3524 = DIRECTION('',(1.,0.));
#3525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3526 = ORIENTED_EDGE('',*,*,#634,.T.);
#3527 = ORIENTED_EDGE('',*,*,#666,.T.);
#3528 = ORIENTED_EDGE('',*,*,#3529,.T.);
#3529 = EDGE_CURVE('',#667,#3530,#3532,.T.);
#3530 = VERTEX_POINT('',#3531);
#3531 = CARTESIAN_POINT('',(-51.,19.215878850576,15.));
#3532 = SURFACE_CURVE('',#3533,(#3538,#3545),.PCURVE_S1.);
#3533 = CIRCLE('',#3534,54.5);
#3534 = AXIS2_PLACEMENT_3D('',#3535,#3536,#3537);
#3535 = CARTESIAN_POINT('',(0.,0.,15.));
#3536 = DIRECTION('',(0.,0.,1.));
#3537 = DIRECTION('',(1.,0.,0.));
#3538 = PCURVE('',#649,#3539);
#3539 = DEFINITIONAL_REPRESENTATION('',(#3540),#3544);
#3540 = CIRCLE('',#3541,54.5);
#3541 = AXIS2_PLACEMENT_2D('',#3542,#3543);
#3542 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3543 = DIRECTION('',(1.,0.));
#3544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3545 = PCURVE('',#2268,#3546);
#3546 = DEFINITIONAL_REPRESENTATION('',(#3547),#3551);
#3547 = LINE('',#3548,#3549);
#3548 = CARTESIAN_POINT('',(-0.,-15.));
#3549 = VECTOR('',#3550,1.);
#3550 = DIRECTION('',(-1.,0.));
#3551 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3552 = ORIENTED_EDGE('',*,*,#3553,.T.);
#3553 = EDGE_CURVE('',#3530,#3477,#3554,.T.);
#3554 = SURFACE_CURVE('',#3555,(#3559,#3566),.PCURVE_S1.);
#3555 = LINE('',#3556,#3557);
#3556 = CARTESIAN_POINT('',(-51.,50.,15.));
#3557 = VECTOR('',#3558,1.);
#3558 = DIRECTION('',(0.,-1.,0.));
#3559 = PCURVE('',#649,#3560);
#3560 = DEFINITIONAL_REPRESENTATION('',(#3561),#3565);
#3561 = LINE('',#3562,#3563);
#3562 = CARTESIAN_POINT('',(-51.,50.));
#3563 = VECTOR('',#3564,1.);
#3564 = DIRECTION('',(0.,-1.));
#3565 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3566 = PCURVE('',#2683,#3567);
#3567 = DEFINITIONAL_REPRESENTATION('',(#3568),#3572);
#3568 = LINE('',#3569,#3570);
#3569 = CARTESIAN_POINT('',(0.,0.));
#3570 = VECTOR('',#3571,1.);
#3571 = DIRECTION('',(1.,0.));
#3572 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3573 = FACE_BOUND('',#3574,.T.);
#3574 = EDGE_LOOP('',(#3575));
#3575 = ORIENTED_EDGE('',*,*,#3576,.F.);
#3576 = EDGE_CURVE('',#3577,#3577,#3579,.T.);
#3577 = VERTEX_POINT('',#3578);
#3578 = CARTESIAN_POINT('',(35.,33.,15.));
#3579 = SURFACE_CURVE('',#3580,(#3585,#3592),.PCURVE_S1.);
#3580 = CIRCLE('',#3581,2.);
#3581 = AXIS2_PLACEMENT_3D('',#3582,#3583,#3584);
#3582 = CARTESIAN_POINT('',(33.,33.,15.));
#3583 = DIRECTION('',(0.,0.,1.));
#3584 = DIRECTION('',(1.,0.,0.));
#3585 = PCURVE('',#649,#3586);
#3586 = DEFINITIONAL_REPRESENTATION('',(#3587),#3591);
#3587 = CIRCLE('',#3588,2.);
#3588 = AXIS2_PLACEMENT_2D('',#3589,#3590);
#3589 = CARTESIAN_POINT('',(33.,33.));
#3590 = DIRECTION('',(1.,0.));
#3591 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3592 = PCURVE('',#3131,#3593);
#3593 = DEFINITIONAL_REPRESENTATION('',(#3594),#3598);
#3594 = LINE('',#3595,#3596);
#3595 = CARTESIAN_POINT('',(0.,0.));
#3596 = VECTOR('',#3597,1.);
#3597 = DIRECTION('',(1.,0.));
#3598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3599 = FACE_BOUND('',#3600,.T.);
#3600 = EDGE_LOOP('',(#3601));
#3601 = ORIENTED_EDGE('',*,*,#3602,.F.);
#3602 = EDGE_CURVE('',#3603,#3603,#3605,.T.);
#3603 = VERTEX_POINT('',#3604);
#3604 = CARTESIAN_POINT('',(31.5,22.,15.));
#3605 = SURFACE_CURVE('',#3606,(#3611,#3618),.PCURVE_S1.);
#3606 = CIRCLE('',#3607,10.);
#3607 = AXIS2_PLACEMENT_3D('',#3608,#3609,#3610);
#3608 = CARTESIAN_POINT('',(21.5,22.,15.));
#3609 = DIRECTION('',(0.,0.,1.));
#3610 = DIRECTION('',(1.,0.,0.));
#3611 = PCURVE('',#649,#3612);
#3612 = DEFINITIONAL_REPRESENTATION('',(#3613),#3617);
#3613 = CIRCLE('',#3614,10.);
#3614 = AXIS2_PLACEMENT_2D('',#3615,#3616);
#3615 = CARTESIAN_POINT('',(21.5,22.));
#3616 = DIRECTION('',(1.,0.));
#3617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3618 = PCURVE('',#2963,#3619);
#3619 = DEFINITIONAL_REPRESENTATION('',(#3620),#3624);
#3620 = LINE('',#3621,#3622);
#3621 = CARTESIAN_POINT('',(0.,0.));
#3622 = VECTOR('',#3623,1.);
#3623 = DIRECTION('',(1.,0.));
#3624 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3625 = FACE_BOUND('',#3626,.T.);
#3626 = EDGE_LOOP('',(#3627));
#3627 = ORIENTED_EDGE('',*,*,#3628,.F.);
#3628 = EDGE_CURVE('',#3629,#3629,#3631,.T.);
#3629 = VERTEX_POINT('',#3630);
#3630 = CARTESIAN_POINT('',(-11.5,22.,15.));
#3631 = SURFACE_CURVE('',#3632,(#3637,#3644),.PCURVE_S1.);
#3632 = CIRCLE('',#3633,10.);
#3633 = AXIS2_PLACEMENT_3D('',#3634,#3635,#3636);
#3634 = CARTESIAN_POINT('',(-21.5,22.,15.));
#3635 = DIRECTION('',(0.,0.,1.));
#3636 = DIRECTION('',(1.,0.,0.));
#3637 = PCURVE('',#649,#3638);
#3638 = DEFINITIONAL_REPRESENTATION('',(#3639),#3643);
#3639 = CIRCLE('',#3640,10.);
#3640 = AXIS2_PLACEMENT_2D('',#3641,#3642);
#3641 = CARTESIAN_POINT('',(-21.5,22.));
#3642 = DIRECTION('',(1.,0.));
#3643 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3644 = PCURVE('',#2850,#3645);
#3645 = DEFINITIONAL_REPRESENTATION('',(#3646),#3650);
#3646 = LINE('',#3647,#3648);
#3647 = CARTESIAN_POINT('',(0.,0.));
#3648 = VECTOR('',#3649,1.);
#3649 = DIRECTION('',(1.,0.));
#3650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3651 = FACE_BOUND('',#3652,.T.);
#3652 = EDGE_LOOP('',(#3653));
#3653 = ORIENTED_EDGE('',*,*,#3654,.F.);
#3654 = EDGE_CURVE('',#3655,#3655,#3657,.T.);
#3655 = VERTEX_POINT('',#3656);
#3656 = CARTESIAN_POINT('',(-31.,33.,15.));
#3657 = SURFACE_CURVE('',#3658,(#3663,#3670),.PCURVE_S1.);
#3658 = CIRCLE('',#3659,2.);
#3659 = AXIS2_PLACEMENT_3D('',#3660,#3661,#3662);
#3660 = CARTESIAN_POINT('',(-33.,33.,15.));
#3661 = DIRECTION('',(0.,0.,1.));
#3662 = DIRECTION('',(1.,0.,0.));
#3663 = PCURVE('',#649,#3664);
#3664 = DEFINITIONAL_REPRESENTATION('',(#3665),#3669);
#3665 = CIRCLE('',#3666,2.);
#3666 = AXIS2_PLACEMENT_2D('',#3667,#3668);
#3667 = CARTESIAN_POINT('',(-33.,33.));
#3668 = DIRECTION('',(1.,0.));
#3669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3670 = PCURVE('',#3100,#3671);
#3671 = DEFINITIONAL_REPRESENTATION('',(#3672),#3676);
#3672 = LINE('',#3673,#3674);
#3673 = CARTESIAN_POINT('',(0.,0.));
#3674 = VECTOR('',#3675,1.);
#3675 = DIRECTION('',(1.,0.));
#3676 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3677 = ADVANCED_FACE('',(#3678),#622,.T.);
#3678 = FACE_BOUND('',#3679,.T.);
#3679 = EDGE_LOOP('',(#3680,#3710,#3731,#3732,#3733,#3734));
#3680 = ORIENTED_EDGE('',*,*,#3681,.F.);
#3681 = EDGE_CURVE('',#3682,#3684,#3686,.T.);
#3682 = VERTEX_POINT('',#3683);
#3683 = CARTESIAN_POINT('',(51.,-19.21587885057,15.));
#3684 = VERTEX_POINT('',#3685);
#3685 = CARTESIAN_POINT('',(51.,-7.509772,15.));
#3686 = SURFACE_CURVE('',#3687,(#3691,#3698),.PCURVE_S1.);
#3687 = LINE('',#3688,#3689);
#3688 = CARTESIAN_POINT('',(51.,-50.,15.));
#3689 = VECTOR('',#3690,1.);
#3690 = DIRECTION('',(0.,1.,0.));
#3691 = PCURVE('',#622,#3692);
#3692 = DEFINITIONAL_REPRESENTATION('',(#3693),#3697);
#3693 = LINE('',#3694,#3695);
#3694 = CARTESIAN_POINT('',(0.,0.));
#3695 = VECTOR('',#3696,1.);
#3696 = DIRECTION('',(1.,0.));
#3697 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3698 = PCURVE('',#3699,#3704);
#3699 = PLANE('',#3700);
#3700 = AXIS2_PLACEMENT_3D('',#3701,#3702,#3703);
#3701 = CARTESIAN_POINT('',(-3.21E-15,4.2E-15,15.));
#3702 = DIRECTION('',(0.,0.,1.));
#3703 = DIRECTION('',(1.,0.,0.));
#3704 = DEFINITIONAL_REPRESENTATION('',(#3705),#3709);
#3705 = LINE('',#3706,#3707);
#3706 = CARTESIAN_POINT('',(51.,-50.));
#3707 = VECTOR('',#3708,1.);
#3708 = DIRECTION('',(0.,1.));
#3709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3710 = ORIENTED_EDGE('',*,*,#3711,.T.);
#3711 = EDGE_CURVE('',#3682,#2591,#3712,.T.);
#3712 = SURFACE_CURVE('',#3713,(#3717,#3724),.PCURVE_S1.);
#3713 = LINE('',#3714,#3715);
#3714 = CARTESIAN_POINT('',(51.,-19.21587885057,0.));
#3715 = VECTOR('',#3716,1.);
#3716 = DIRECTION('',(-0.,-0.,-1.));
#3717 = PCURVE('',#622,#3718);
#3718 = DEFINITIONAL_REPRESENTATION('',(#3719),#3723);
#3719 = LINE('',#3720,#3721);
#3720 = CARTESIAN_POINT('',(30.784121149424,-15.));
#3721 = VECTOR('',#3722,1.);
#3722 = DIRECTION('',(0.,-1.));
#3723 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3724 = PCURVE('',#2607,#3725);
#3725 = DEFINITIONAL_REPRESENTATION('',(#3726),#3730);
#3726 = LINE('',#3727,#3728);
#3727 = CARTESIAN_POINT('',(-5.922853305643,0.));
#3728 = VECTOR('',#3729,1.);
#3729 = DIRECTION('',(-0.,1.));
#3730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3731 = ORIENTED_EDGE('',*,*,#2619,.T.);
#3732 = ORIENTED_EDGE('',*,*,#606,.F.);
#3733 = ORIENTED_EDGE('',*,*,#3506,.F.);
#3734 = ORIENTED_EDGE('',*,*,#3735,.F.);
#3735 = EDGE_CURVE('',#3684,#3479,#3736,.T.);
#3736 = SURFACE_CURVE('',#3737,(#3741,#3748),.PCURVE_S1.);
#3737 = LINE('',#3738,#3739);
#3738 = CARTESIAN_POINT('',(51.,-50.,15.));
#3739 = VECTOR('',#3740,1.);
#3740 = DIRECTION('',(0.,1.,0.));
#3741 = PCURVE('',#622,#3742);
#3742 = DEFINITIONAL_REPRESENTATION('',(#3743),#3747);
#3743 = LINE('',#3744,#3745);
#3744 = CARTESIAN_POINT('',(0.,0.));
#3745 = VECTOR('',#3746,1.);
#3746 = DIRECTION('',(1.,0.));
#3747 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3748 = PCURVE('',#3494,#3749);
#3749 = DEFINITIONAL_REPRESENTATION('',(#3750),#3754);
#3750 = LINE('',#3751,#3752);
#3751 = CARTESIAN_POINT('',(51.,-50.));
#3752 = VECTOR('',#3753,1.);
#3753 = DIRECTION('',(0.,1.));
#3754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3755 = ADVANCED_FACE('',(#3756),#1313,.F.);
#3756 = FACE_BOUND('',#3757,.F.);
#3757 = EDGE_LOOP('',(#3758,#3759,#3782,#3809));
#3758 = ORIENTED_EDGE('',*,*,#1075,.F.);
#3759 = ORIENTED_EDGE('',*,*,#3760,.T.);
#3760 = EDGE_CURVE('',#1076,#3761,#3763,.T.);
#3761 = VERTEX_POINT('',#3762);
#3762 = CARTESIAN_POINT('',(12.5,58.,41.));
#3763 = SEAM_CURVE('',#3764,(#3768,#3775),.PCURVE_S1.);
#3764 = LINE('',#3765,#3766);
#3765 = CARTESIAN_POINT('',(12.5,-9.1E-15,41.));
#3766 = VECTOR('',#3767,1.);
#3767 = DIRECTION('',(0.,1.,2.2E-16));
#3768 = PCURVE('',#1313,#3769);
#3769 = DEFINITIONAL_REPRESENTATION('',(#3770),#3774);
#3770 = LINE('',#3771,#3772);
#3771 = CARTESIAN_POINT('',(6.28318530718,0.));
#3772 = VECTOR('',#3773,1.);
#3773 = DIRECTION('',(0.,-1.));
#3774 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3775 = PCURVE('',#1313,#3776);
#3776 = DEFINITIONAL_REPRESENTATION('',(#3777),#3781);
#3777 = LINE('',#3778,#3779);
#3778 = CARTESIAN_POINT('',(0.,0.));
#3779 = VECTOR('',#3780,1.);
#3780 = DIRECTION('',(0.,-1.));
#3781 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3782 = ORIENTED_EDGE('',*,*,#3783,.T.);
#3783 = EDGE_CURVE('',#3761,#3761,#3784,.T.);
#3784 = SURFACE_CURVE('',#3785,(#3790,#3797),.PCURVE_S1.);
#3785 = CIRCLE('',#3786,12.5);
#3786 = AXIS2_PLACEMENT_3D('',#3787,#3788,#3789);
#3787 = CARTESIAN_POINT('',(0.,58.,41.));
#3788 = DIRECTION('',(0.,-1.,0.));
#3789 = DIRECTION('',(1.,0.,0.));
#3790 = PCURVE('',#1313,#3791);
#3791 = DEFINITIONAL_REPRESENTATION('',(#3792),#3796);
#3792 = LINE('',#3793,#3794);
#3793 = CARTESIAN_POINT('',(0.,-58.));
#3794 = VECTOR('',#3795,1.);
#3795 = DIRECTION('',(1.,0.));
#3796 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3797 = PCURVE('',#3798,#3803);
#3798 = PLANE('',#3799);
#3799 = AXIS2_PLACEMENT_3D('',#3800,#3801,#3802);
#3800 = CARTESIAN_POINT('',(-1.31E-15,58.,41.));
#3801 = DIRECTION('',(0.,-1.,-2.2E-16));
#3802 = DIRECTION('',(0.,2.2E-16,-1.));
#3803 = DEFINITIONAL_REPRESENTATION('',(#3804),#3808);
#3804 = CIRCLE('',#3805,12.5);
#3805 = AXIS2_PLACEMENT_2D('',#3806,#3807);
#3806 = CARTESIAN_POINT('',(7.105427357601E-15,1.31E-15));
#3807 = DIRECTION('',(0.,1.));
#3808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3809 = ORIENTED_EDGE('',*,*,#3760,.F.);
#3810 = ADVANCED_FACE('',(#3811),#1596,.F.);
#3811 = FACE_BOUND('',#3812,.F.);
#3812 = EDGE_LOOP('',(#3813,#3814,#3837,#3859));
#3813 = ORIENTED_EDGE('',*,*,#1438,.F.);
#3814 = ORIENTED_EDGE('',*,*,#3815,.T.);
#3815 = EDGE_CURVE('',#1439,#3816,#3818,.T.);
#3816 = VERTEX_POINT('',#3817);
#3817 = CARTESIAN_POINT('',(20.320508075689,58.,31.));
#3818 = SEAM_CURVE('',#3819,(#3823,#3830),.PCURVE_S1.);
#3819 = LINE('',#3820,#3821);
#3820 = CARTESIAN_POINT('',(20.320508075689,-6.88E-15,31.));
#3821 = VECTOR('',#3822,1.);
#3822 = DIRECTION('',(0.,1.,2.2E-16));
#3823 = PCURVE('',#1596,#3824);
#3824 = DEFINITIONAL_REPRESENTATION('',(#3825),#3829);
#3825 = LINE('',#3826,#3827);
#3826 = CARTESIAN_POINT('',(6.28318530718,0.));
#3827 = VECTOR('',#3828,1.);
#3828 = DIRECTION('',(0.,-1.));
#3829 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3830 = PCURVE('',#1596,#3831);
#3831 = DEFINITIONAL_REPRESENTATION('',(#3832),#3836);
#3832 = LINE('',#3833,#3834);
#3833 = CARTESIAN_POINT('',(0.,0.));
#3834 = VECTOR('',#3835,1.);
#3835 = DIRECTION('',(0.,-1.));
#3836 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3837 = ORIENTED_EDGE('',*,*,#3838,.T.);
#3838 = EDGE_CURVE('',#3816,#3816,#3839,.T.);
#3839 = SURFACE_CURVE('',#3840,(#3845,#3852),.PCURVE_S1.);
#3840 = CIRCLE('',#3841,3.);
#3841 = AXIS2_PLACEMENT_3D('',#3842,#3843,#3844);
#3842 = CARTESIAN_POINT('',(17.320508075689,58.,31.));
#3843 = DIRECTION('',(0.,-1.,0.));
#3844 = DIRECTION('',(1.,0.,0.));
#3845 = PCURVE('',#1596,#3846);
#3846 = DEFINITIONAL_REPRESENTATION('',(#3847),#3851);
#3847 = LINE('',#3848,#3849);
#3848 = CARTESIAN_POINT('',(0.,-58.));
#3849 = VECTOR('',#3850,1.);
#3850 = DIRECTION('',(1.,0.));
#3851 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3852 = PCURVE('',#2322,#3853);
#3853 = DEFINITIONAL_REPRESENTATION('',(#3854),#3858);
#3854 = CIRCLE('',#3855,3.);
#3855 = AXIS2_PLACEMENT_2D('',#3856,#3857);
#3856 = CARTESIAN_POINT('',(3.552713678801E-15,0.));
#3857 = DIRECTION('',(0.,1.));
#3858 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3859 = ORIENTED_EDGE('',*,*,#3815,.F.);
#3860 = ADVANCED_FACE('',(#3861),#1839,.F.);
#3861 = FACE_BOUND('',#3862,.F.);
#3862 = EDGE_LOOP('',(#3863,#3864,#3887,#3909));
#3863 = ORIENTED_EDGE('',*,*,#1681,.F.);
#3864 = ORIENTED_EDGE('',*,*,#3865,.T.);
#3865 = EDGE_CURVE('',#1682,#3866,#3868,.T.);
#3866 = VERTEX_POINT('',#3867);
#3867 = CARTESIAN_POINT('',(-14.32050807568,58.,31.));
#3868 = SEAM_CURVE('',#3869,(#3873,#3880),.PCURVE_S1.);
#3869 = LINE('',#3870,#3871);
#3870 = CARTESIAN_POINT('',(-14.32050807568,-6.88E-15,31.));
#3871 = VECTOR('',#3872,1.);
#3872 = DIRECTION('',(0.,1.,2.2E-16));
#3873 = PCURVE('',#1839,#3874);
#3874 = DEFINITIONAL_REPRESENTATION('',(#3875),#3879);
#3875 = LINE('',#3876,#3877);
#3876 = CARTESIAN_POINT('',(6.28318530718,0.));
#3877 = VECTOR('',#3878,1.);
#3878 = DIRECTION('',(0.,-1.));
#3879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3880 = PCURVE('',#1839,#3881);
#3881 = DEFINITIONAL_REPRESENTATION('',(#3882),#3886);
#3882 = LINE('',#3883,#3884);
#3883 = CARTESIAN_POINT('',(0.,0.));
#3884 = VECTOR('',#3885,1.);
#3885 = DIRECTION('',(0.,-1.));
#3886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3887 = ORIENTED_EDGE('',*,*,#3888,.T.);
#3888 = EDGE_CURVE('',#3866,#3866,#3889,.T.);
#3889 = SURFACE_CURVE('',#3890,(#3895,#3902),.PCURVE_S1.);
#3890 = CIRCLE('',#3891,3.);
#3891 = AXIS2_PLACEMENT_3D('',#3892,#3893,#3894);
#3892 = CARTESIAN_POINT('',(-17.32050807568,58.,31.));
#3893 = DIRECTION('',(0.,-1.,0.));
#3894 = DIRECTION('',(1.,0.,0.));
#3895 = PCURVE('',#1839,#3896);
#3896 = DEFINITIONAL_REPRESENTATION('',(#3897),#3901);
#3897 = LINE('',#3898,#3899);
#3898 = CARTESIAN_POINT('',(0.,-58.));
#3899 = VECTOR('',#3900,1.);
#3900 = DIRECTION('',(1.,0.));
#3901 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3902 = PCURVE('',#2487,#3903);
#3903 = DEFINITIONAL_REPRESENTATION('',(#3904),#3908);
#3904 = CIRCLE('',#3905,3.);
#3905 = AXIS2_PLACEMENT_2D('',#3906,#3907);
#3906 = CARTESIAN_POINT('',(-3.552713678801E-15,0.));
#3907 = DIRECTION('',(0.,1.));
#3908 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3909 = ORIENTED_EDGE('',*,*,#3865,.F.);
#3910 = ADVANCED_FACE('',(#3911),#2082,.F.);
#3911 = FACE_BOUND('',#3912,.F.);
#3912 = EDGE_LOOP('',(#3913,#3914,#3937,#3959));
#3913 = ORIENTED_EDGE('',*,*,#1924,.F.);
#3914 = ORIENTED_EDGE('',*,*,#3915,.T.);
#3915 = EDGE_CURVE('',#1925,#3916,#3918,.T.);
#3916 = VERTEX_POINT('',#3917);
#3917 = CARTESIAN_POINT('',(3.,58.,61.));
#3918 = SEAM_CURVE('',#3919,(#3923,#3930),.PCURVE_S1.);
#3919 = LINE('',#3920,#3921);
#3920 = CARTESIAN_POINT('',(3.,-1.354E-14,61.));
#3921 = VECTOR('',#3922,1.);
#3922 = DIRECTION('',(0.,1.,2.2E-16));
#3923 = PCURVE('',#2082,#3924);
#3924 = DEFINITIONAL_REPRESENTATION('',(#3925),#3929);
#3925 = LINE('',#3926,#3927);
#3926 = CARTESIAN_POINT('',(6.28318530718,0.));
#3927 = VECTOR('',#3928,1.);
#3928 = DIRECTION('',(0.,-1.));
#3929 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3930 = PCURVE('',#2082,#3931);
#3931 = DEFINITIONAL_REPRESENTATION('',(#3932),#3936);
#3932 = LINE('',#3933,#3934);
#3933 = CARTESIAN_POINT('',(0.,0.));
#3934 = VECTOR('',#3935,1.);
#3935 = DIRECTION('',(0.,-1.));
#3936 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3937 = ORIENTED_EDGE('',*,*,#3938,.T.);
#3938 = EDGE_CURVE('',#3916,#3916,#3939,.T.);
#3939 = SURFACE_CURVE('',#3940,(#3945,#3952),.PCURVE_S1.);
#3940 = CIRCLE('',#3941,3.);
#3941 = AXIS2_PLACEMENT_3D('',#3942,#3943,#3944);
#3942 = CARTESIAN_POINT('',(0.,58.,61.));
#3943 = DIRECTION('',(0.,-1.,0.));
#3944 = DIRECTION('',(1.,0.,0.));
#3945 = PCURVE('',#2082,#3946);
#3946 = DEFINITIONAL_REPRESENTATION('',(#3947),#3951);
#3947 = LINE('',#3948,#3949);
#3948 = CARTESIAN_POINT('',(0.,-58.));
#3949 = VECTOR('',#3950,1.);
#3950 = DIRECTION('',(1.,0.));
#3951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3952 = PCURVE('',#2432,#3953);
#3953 = DEFINITIONAL_REPRESENTATION('',(#3954),#3958);
#3954 = CIRCLE('',#3955,3.);
#3955 = AXIS2_PLACEMENT_2D('',#3956,#3957);
#3956 = CARTESIAN_POINT('',(7.105427357601E-15,2.3E-16));
#3957 = DIRECTION('',(0.,1.));
#3958 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3959 = ORIENTED_EDGE('',*,*,#3915,.F.);
#3960 = ADVANCED_FACE('',(#3961),#2268,.T.);
#3961 = FACE_BOUND('',#3962,.F.);
#3962 = EDGE_LOOP('',(#3963,#3964,#3985,#3986));
#3963 = ORIENTED_EDGE('',*,*,#3529,.T.);
#3964 = ORIENTED_EDGE('',*,*,#3965,.T.);
#3965 = EDGE_CURVE('',#3530,#2644,#3966,.T.);
#3966 = SURFACE_CURVE('',#3967,(#3971,#3978),.PCURVE_S1.);
#3967 = LINE('',#3968,#3969);
#3968 = CARTESIAN_POINT('',(-51.,19.215878850576,0.));
#3969 = VECTOR('',#3970,1.);
#3970 = DIRECTION('',(-0.,-0.,-1.));
#3971 = PCURVE('',#2268,#3972);
#3972 = DEFINITIONAL_REPRESENTATION('',(#3973),#3977);
#3973 = LINE('',#3974,#3975);
#3974 = CARTESIAN_POINT('',(-2.781260652053,0.));
#3975 = VECTOR('',#3976,1.);
#3976 = DIRECTION('',(-0.,1.));
#3977 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3978 = PCURVE('',#2683,#3979);
#3979 = DEFINITIONAL_REPRESENTATION('',(#3980),#3984);
#3980 = LINE('',#3981,#3982);
#3981 = CARTESIAN_POINT('',(30.784121149424,-15.));
#3982 = VECTOR('',#3983,1.);
#3983 = DIRECTION('',(0.,-1.));
#3984 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3985 = ORIENTED_EDGE('',*,*,#2643,.F.);
#3986 = ORIENTED_EDGE('',*,*,#2252,.T.);
#3987 = ADVANCED_FACE('',(#3988,#3991),#2322,.T.);
#3988 = FACE_BOUND('',#3989,.T.);
#3989 = EDGE_LOOP('',(#3990));
#3990 = ORIENTED_EDGE('',*,*,#3838,.T.);
#3991 = FACE_BOUND('',#3992,.T.);
#3992 = EDGE_LOOP('',(#3993));
#3993 = ORIENTED_EDGE('',*,*,#2305,.F.);
#3994 = ADVANCED_FACE('',(#3995,#3998),#2398,.T.);
#3995 = FACE_BOUND('',#3996,.T.);
#3996 = EDGE_LOOP('',(#3997));
#3997 = ORIENTED_EDGE('',*,*,#2383,.T.);
#3998 = FACE_BOUND('',#3999,.T.);
#3999 = EDGE_LOOP('',(#4000));
#4000 = ORIENTED_EDGE('',*,*,#4001,.T.);
#4001 = EDGE_CURVE('',#4002,#4002,#4004,.T.);
#4002 = VERTEX_POINT('',#4003);
#4003 = CARTESIAN_POINT('',(6.55,62.7,41.));
#4004 = SURFACE_CURVE('',#4005,(#4010,#4021),.PCURVE_S1.);
#4005 = CIRCLE('',#4006,6.55);
#4006 = AXIS2_PLACEMENT_3D('',#4007,#4008,#4009);
#4007 = CARTESIAN_POINT('',(0.,62.7,41.));
#4008 = DIRECTION('',(0.,-1.,-2.2E-16));
#4009 = DIRECTION('',(1.,0.,0.));
#4010 = PCURVE('',#2398,#4011);
#4011 = DEFINITIONAL_REPRESENTATION('',(#4012),#4020);
#4012 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4013,#4014,#4015,#4016,
#4017,#4018,#4019),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#4013 = CARTESIAN_POINT('',(-7.105427357601E-15,6.55));
#4014 = CARTESIAN_POINT('',(11.344932789576,6.55));
#4015 = CARTESIAN_POINT('',(5.672466394788,-3.275));
#4016 = CARTESIAN_POINT('',(-5.501140050718E-15,-13.1));
#4017 = CARTESIAN_POINT('',(-5.672466394788,-3.275));
#4018 = CARTESIAN_POINT('',(-11.34493278957,6.55));
#4019 = CARTESIAN_POINT('',(-7.105427357601E-15,6.55));
#4020 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4021 = PCURVE('',#4022,#4027);
#4022 = CYLINDRICAL_SURFACE('',#4023,6.55);
#4023 = AXIS2_PLACEMENT_3D('',#4024,#4025,#4026);
#4024 = CARTESIAN_POINT('',(0.,-9.1E-15,41.));
#4025 = DIRECTION('',(0.,-1.,-2.2E-16));
#4026 = DIRECTION('',(1.,0.,0.));
#4027 = DEFINITIONAL_REPRESENTATION('',(#4028),#4032);
#4028 = LINE('',#4029,#4030);
#4029 = CARTESIAN_POINT('',(0.,-62.7));
#4030 = VECTOR('',#4031,1.);
#4031 = DIRECTION('',(1.,0.));
#4032 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4033 = ADVANCED_FACE('',(#4034,#4037),#2432,.T.);
#4034 = FACE_BOUND('',#4035,.T.);
#4035 = EDGE_LOOP('',(#4036));
#4036 = ORIENTED_EDGE('',*,*,#3938,.T.);
#4037 = FACE_BOUND('',#4038,.T.);
#4038 = EDGE_LOOP('',(#4039));
#4039 = ORIENTED_EDGE('',*,*,#2415,.F.);
#4040 = ADVANCED_FACE('',(#4041,#4044),#2487,.T.);
#4041 = FACE_BOUND('',#4042,.T.);
#4042 = EDGE_LOOP('',(#4043));
#4043 = ORIENTED_EDGE('',*,*,#3888,.T.);
#4044 = FACE_BOUND('',#4045,.T.);
#4045 = EDGE_LOOP('',(#4046));
#4046 = ORIENTED_EDGE('',*,*,#2470,.F.);
#4047 = ADVANCED_FACE('',(#4048),#2578,.T.);
#4048 = FACE_BOUND('',#4049,.T.);
#4049 = EDGE_LOOP('',(#4050,#4075,#4096,#4097,#4120,#4148,#4169,#4170));
#4050 = ORIENTED_EDGE('',*,*,#4051,.F.);
#4051 = EDGE_CURVE('',#4052,#4054,#4056,.T.);
#4052 = VERTEX_POINT('',#4053);
#4053 = CARTESIAN_POINT('',(-21.68524844219,-50.,15.));
#4054 = VERTEX_POINT('',#4055);
#4055 = CARTESIAN_POINT('',(21.685248442202,-50.,15.));
#4056 = SURFACE_CURVE('',#4057,(#4061,#4068),.PCURVE_S1.);
#4057 = LINE('',#4058,#4059);
#4058 = CARTESIAN_POINT('',(-45.,-50.,15.));
#4059 = VECTOR('',#4060,1.);
#4060 = DIRECTION('',(1.,0.,0.));
#4061 = PCURVE('',#2578,#4062);
#4062 = DEFINITIONAL_REPRESENTATION('',(#4063),#4067);
#4063 = LINE('',#4064,#4065);
#4064 = CARTESIAN_POINT('',(0.,0.));
#4065 = VECTOR('',#4066,1.);
#4066 = DIRECTION('',(1.,0.));
#4067 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4068 = PCURVE('',#3699,#4069);
#4069 = DEFINITIONAL_REPRESENTATION('',(#4070),#4074);
#4070 = LINE('',#4071,#4072);
#4071 = CARTESIAN_POINT('',(-45.,-50.));
#4072 = VECTOR('',#4073,1.);
#4073 = DIRECTION('',(1.,0.));
#4074 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4075 = ORIENTED_EDGE('',*,*,#4076,.T.);
#4076 = EDGE_CURVE('',#4052,#2696,#4077,.T.);
#4077 = SURFACE_CURVE('',#4078,(#4082,#4089),.PCURVE_S1.);
#4078 = LINE('',#4079,#4080);
#4079 = CARTESIAN_POINT('',(-21.6852484422,-50.,0.));
#4080 = VECTOR('',#4081,1.);
#4081 = DIRECTION('',(-0.,-0.,-1.));
#4082 = PCURVE('',#2578,#4083);
#4083 = DEFINITIONAL_REPRESENTATION('',(#4084),#4088);
#4084 = LINE('',#4085,#4086);
#4085 = CARTESIAN_POINT('',(23.314751557799,-15.));
#4086 = VECTOR('',#4087,1.);
#4087 = DIRECTION('',(0.,-1.));
#4088 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4089 = PCURVE('',#2712,#4090);
#4090 = DEFINITIONAL_REPRESENTATION('',(#4091),#4095);
#4091 = LINE('',#4092,#4093);
#4092 = CARTESIAN_POINT('',(-4.303168310829,0.));
#4093 = VECTOR('',#4094,1.);
#4094 = DIRECTION('',(-0.,1.));
#4095 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4096 = ORIENTED_EDGE('',*,*,#2724,.T.);
#4097 = ORIENTED_EDGE('',*,*,#4098,.F.);
#4098 = EDGE_CURVE('',#4099,#2725,#4101,.T.);
#4099 = VERTEX_POINT('',#4100);
#4100 = CARTESIAN_POINT('',(-15.5,-50.,10.));
#4101 = SURFACE_CURVE('',#4102,(#4106,#4113),.PCURVE_S1.);
#4102 = LINE('',#4103,#4104);
#4103 = CARTESIAN_POINT('',(-15.5,-50.,7.5));
#4104 = VECTOR('',#4105,1.);
#4105 = DIRECTION('',(0.,0.,-1.));
#4106 = PCURVE('',#2578,#4107);
#4107 = DEFINITIONAL_REPRESENTATION('',(#4108),#4112);
#4108 = LINE('',#4109,#4110);
#4109 = CARTESIAN_POINT('',(29.5,-7.5));
#4110 = VECTOR('',#4111,1.);
#4111 = DIRECTION('',(0.,-1.));
#4112 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4113 = PCURVE('',#2763,#4114);
#4114 = DEFINITIONAL_REPRESENTATION('',(#4115),#4119);
#4115 = LINE('',#4116,#4117);
#4116 = CARTESIAN_POINT('',(40.,-7.5));
#4117 = VECTOR('',#4118,1.);
#4118 = DIRECTION('',(0.,1.));
#4119 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4120 = ORIENTED_EDGE('',*,*,#4121,.T.);
#4121 = EDGE_CURVE('',#4099,#4122,#4124,.T.);
#4122 = VERTEX_POINT('',#4123);
#4123 = CARTESIAN_POINT('',(15.5,-50.,10.));
#4124 = SURFACE_CURVE('',#4125,(#4129,#4136),.PCURVE_S1.);
#4125 = LINE('',#4126,#4127);
#4126 = CARTESIAN_POINT('',(-22.5,-50.,10.));
#4127 = VECTOR('',#4128,1.);
#4128 = DIRECTION('',(1.,0.,0.));
#4129 = PCURVE('',#2578,#4130);
#4130 = DEFINITIONAL_REPRESENTATION('',(#4131),#4135);
#4131 = LINE('',#4132,#4133);
#4132 = CARTESIAN_POINT('',(22.5,-5.));
#4133 = VECTOR('',#4134,1.);
#4134 = DIRECTION('',(1.,0.));
#4135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4136 = PCURVE('',#4137,#4142);
#4137 = PLANE('',#4138);
#4138 = AXIS2_PLACEMENT_3D('',#4139,#4140,#4141);
#4139 = CARTESIAN_POINT('',(0.,-35.,10.));
#4140 = DIRECTION('',(0.,0.,1.));
#4141 = DIRECTION('',(1.,0.,0.));
#4142 = DEFINITIONAL_REPRESENTATION('',(#4143),#4147);
#4143 = LINE('',#4144,#4145);
#4144 = CARTESIAN_POINT('',(-22.5,-15.));
#4145 = VECTOR('',#4146,1.);
#4146 = DIRECTION('',(1.,0.));
#4147 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4148 = ORIENTED_EDGE('',*,*,#4149,.F.);
#4149 = EDGE_CURVE('',#2561,#4122,#4150,.T.);
#4150 = SURFACE_CURVE('',#4151,(#4155,#4162),.PCURVE_S1.);
#4151 = LINE('',#4152,#4153);
#4152 = CARTESIAN_POINT('',(15.5,-50.,7.5));
#4153 = VECTOR('',#4154,1.);
#4154 = DIRECTION('',(0.,0.,1.));
#4155 = PCURVE('',#2578,#4156);
#4156 = DEFINITIONAL_REPRESENTATION('',(#4157),#4161);
#4157 = LINE('',#4158,#4159);
#4158 = CARTESIAN_POINT('',(60.5,-7.5));
#4159 = VECTOR('',#4160,1.);
#4160 = DIRECTION('',(0.,1.));
#4161 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4162 = PCURVE('',#2817,#4163);
#4163 = DEFINITIONAL_REPRESENTATION('',(#4164),#4168);
#4164 = LINE('',#4165,#4166);
#4165 = CARTESIAN_POINT('',(10.,-7.5));
#4166 = VECTOR('',#4167,1.);
#4167 = DIRECTION('',(0.,-1.));
#4168 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4169 = ORIENTED_EDGE('',*,*,#2560,.T.);
#4170 = ORIENTED_EDGE('',*,*,#4171,.F.);
#4171 = EDGE_CURVE('',#4054,#2563,#4172,.T.);
#4172 = SURFACE_CURVE('',#4173,(#4177,#4184),.PCURVE_S1.);
#4173 = LINE('',#4174,#4175);
#4174 = CARTESIAN_POINT('',(21.685248442201,-50.,0.));
#4175 = VECTOR('',#4176,1.);
#4176 = DIRECTION('',(-0.,-0.,-1.));
#4177 = PCURVE('',#2578,#4178);
#4178 = DEFINITIONAL_REPRESENTATION('',(#4179),#4183);
#4179 = LINE('',#4180,#4181);
#4180 = CARTESIAN_POINT('',(66.685248442201,-15.));
#4181 = VECTOR('',#4182,1.);
#4182 = DIRECTION('',(0.,-1.));
#4183 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4184 = PCURVE('',#2607,#4185);
#4185 = DEFINITIONAL_REPRESENTATION('',(#4186),#4190);
#4186 = LINE('',#4187,#4188);
#4187 = CARTESIAN_POINT('',(-5.12160964994,0.));
#4188 = VECTOR('',#4189,1.);
#4189 = DIRECTION('',(-0.,1.));
#4190 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4191 = ADVANCED_FACE('',(#4192),#2817,.T.);
#4192 = FACE_BOUND('',#4193,.T.);
#4193 = EDGE_LOOP('',(#4194,#4195,#4196,#4219));
#4194 = ORIENTED_EDGE('',*,*,#2803,.F.);
#4195 = ORIENTED_EDGE('',*,*,#4149,.T.);
#4196 = ORIENTED_EDGE('',*,*,#4197,.T.);
#4197 = EDGE_CURVE('',#4122,#4198,#4200,.T.);
#4198 = VERTEX_POINT('',#4199);
#4199 = CARTESIAN_POINT('',(15.5,-10.,10.));
#4200 = SURFACE_CURVE('',#4201,(#4205,#4212),.PCURVE_S1.);
#4201 = LINE('',#4202,#4203);
#4202 = CARTESIAN_POINT('',(15.5,-60.,10.));
#4203 = VECTOR('',#4204,1.);
#4204 = DIRECTION('',(0.,1.,0.));
#4205 = PCURVE('',#2817,#4206);
#4206 = DEFINITIONAL_REPRESENTATION('',(#4207),#4211);
#4207 = LINE('',#4208,#4209);
#4208 = CARTESIAN_POINT('',(0.,-10.));
#4209 = VECTOR('',#4210,1.);
#4210 = DIRECTION('',(1.,0.));
#4211 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4212 = PCURVE('',#4137,#4213);
#4213 = DEFINITIONAL_REPRESENTATION('',(#4214),#4218);
#4214 = LINE('',#4215,#4216);
#4215 = CARTESIAN_POINT('',(15.5,-25.));
#4216 = VECTOR('',#4217,1.);
#4217 = DIRECTION('',(0.,1.));
#4218 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4219 = ORIENTED_EDGE('',*,*,#4220,.F.);
#4220 = EDGE_CURVE('',#2776,#4198,#4221,.T.);
#4221 = SURFACE_CURVE('',#4222,(#4226,#4233),.PCURVE_S1.);
#4222 = LINE('',#4223,#4224);
#4223 = CARTESIAN_POINT('',(15.5,-10.,0.));
#4224 = VECTOR('',#4225,1.);
#4225 = DIRECTION('',(0.,0.,1.));
#4226 = PCURVE('',#2817,#4227);
#4227 = DEFINITIONAL_REPRESENTATION('',(#4228),#4232);
#4228 = LINE('',#4229,#4230);
#4229 = CARTESIAN_POINT('',(50.,0.));
#4230 = VECTOR('',#4231,1.);
#4231 = DIRECTION('',(0.,-1.));
#4232 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4233 = PCURVE('',#2791,#4234);
#4234 = DEFINITIONAL_REPRESENTATION('',(#4235),#4239);
#4235 = LINE('',#4236,#4237);
#4236 = CARTESIAN_POINT('',(0.,-0.));
#4237 = VECTOR('',#4238,1.);
#4238 = DIRECTION('',(0.,-1.));
#4239 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4240 = ADVANCED_FACE('',(#4241),#2607,.T.);
#4241 = FACE_BOUND('',#4242,.F.);
#4242 = EDGE_LOOP('',(#4243,#4244,#4245,#4246));
#4243 = ORIENTED_EDGE('',*,*,#3711,.T.);
#4244 = ORIENTED_EDGE('',*,*,#2590,.F.);
#4245 = ORIENTED_EDGE('',*,*,#4171,.F.);
#4246 = ORIENTED_EDGE('',*,*,#4247,.T.);
#4247 = EDGE_CURVE('',#4054,#3682,#4248,.T.);
#4248 = SURFACE_CURVE('',#4249,(#4254,#4261),.PCURVE_S1.);
#4249 = CIRCLE('',#4250,54.5);
#4250 = AXIS2_PLACEMENT_3D('',#4251,#4252,#4253);
#4251 = CARTESIAN_POINT('',(0.,0.,15.));
#4252 = DIRECTION('',(0.,0.,1.));
#4253 = DIRECTION('',(1.,0.,0.));
#4254 = PCURVE('',#2607,#4255);
#4255 = DEFINITIONAL_REPRESENTATION('',(#4256),#4260);
#4256 = LINE('',#4257,#4258);
#4257 = CARTESIAN_POINT('',(-0.,-15.));
#4258 = VECTOR('',#4259,1.);
#4259 = DIRECTION('',(-1.,0.));
#4260 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4261 = PCURVE('',#3699,#4262);
#4262 = DEFINITIONAL_REPRESENTATION('',(#4263),#4267);
#4263 = CIRCLE('',#4264,54.5);
#4264 = AXIS2_PLACEMENT_2D('',#4265,#4266);
#4265 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#4266 = DIRECTION('',(1.,0.));
#4267 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4268 = ADVANCED_FACE('',(#4269),#2791,.T.);
#4269 = FACE_BOUND('',#4270,.T.);
#4270 = EDGE_LOOP('',(#4271,#4272,#4295,#4316));
#4271 = ORIENTED_EDGE('',*,*,#4220,.T.);
#4272 = ORIENTED_EDGE('',*,*,#4273,.T.);
#4273 = EDGE_CURVE('',#4198,#4274,#4276,.T.);
#4274 = VERTEX_POINT('',#4275);
#4275 = CARTESIAN_POINT('',(-15.5,-10.,10.));
#4276 = SURFACE_CURVE('',#4277,(#4281,#4288),.PCURVE_S1.);
#4277 = LINE('',#4278,#4279);
#4278 = CARTESIAN_POINT('',(15.5,-10.,10.));
#4279 = VECTOR('',#4280,1.);
#4280 = DIRECTION('',(-1.,0.,0.));
#4281 = PCURVE('',#2791,#4282);
#4282 = DEFINITIONAL_REPRESENTATION('',(#4283),#4287);
#4283 = LINE('',#4284,#4285);
#4284 = CARTESIAN_POINT('',(0.,-10.));
#4285 = VECTOR('',#4286,1.);
#4286 = DIRECTION('',(1.,0.));
#4287 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4288 = PCURVE('',#4137,#4289);
#4289 = DEFINITIONAL_REPRESENTATION('',(#4290),#4294);
#4290 = LINE('',#4291,#4292);
#4291 = CARTESIAN_POINT('',(15.5,25.));
#4292 = VECTOR('',#4293,1.);
#4293 = DIRECTION('',(-1.,0.));
#4294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4295 = ORIENTED_EDGE('',*,*,#4296,.F.);
#4296 = EDGE_CURVE('',#2748,#4274,#4297,.T.);
#4297 = SURFACE_CURVE('',#4298,(#4302,#4309),.PCURVE_S1.);
#4298 = LINE('',#4299,#4300);
#4299 = CARTESIAN_POINT('',(-15.5,-10.,0.));
#4300 = VECTOR('',#4301,1.);
#4301 = DIRECTION('',(0.,0.,1.));
#4302 = PCURVE('',#2791,#4303);
#4303 = DEFINITIONAL_REPRESENTATION('',(#4304),#4308);
#4304 = LINE('',#4305,#4306);
#4305 = CARTESIAN_POINT('',(31.,0.));
#4306 = VECTOR('',#4307,1.);
#4307 = DIRECTION('',(0.,-1.));
#4308 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4309 = PCURVE('',#2763,#4310);
#4310 = DEFINITIONAL_REPRESENTATION('',(#4311),#4315);
#4311 = LINE('',#4312,#4313);
#4312 = CARTESIAN_POINT('',(0.,0.));
#4313 = VECTOR('',#4314,1.);
#4314 = DIRECTION('',(0.,-1.));
#4315 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4316 = ORIENTED_EDGE('',*,*,#2775,.F.);
#4317 = ADVANCED_FACE('',(#4318),#2763,.T.);
#4318 = FACE_BOUND('',#4319,.T.);
#4319 = EDGE_LOOP('',(#4320,#4321,#4322,#4343));
#4320 = ORIENTED_EDGE('',*,*,#2747,.F.);
#4321 = ORIENTED_EDGE('',*,*,#4296,.T.);
#4322 = ORIENTED_EDGE('',*,*,#4323,.T.);
#4323 = EDGE_CURVE('',#4274,#4099,#4324,.T.);
#4324 = SURFACE_CURVE('',#4325,(#4329,#4336),.PCURVE_S1.);
#4325 = LINE('',#4326,#4327);
#4326 = CARTESIAN_POINT('',(-15.5,-10.,10.));
#4327 = VECTOR('',#4328,1.);
#4328 = DIRECTION('',(0.,-1.,0.));
#4329 = PCURVE('',#2763,#4330);
#4330 = DEFINITIONAL_REPRESENTATION('',(#4331),#4335);
#4331 = LINE('',#4332,#4333);
#4332 = CARTESIAN_POINT('',(0.,-10.));
#4333 = VECTOR('',#4334,1.);
#4334 = DIRECTION('',(1.,0.));
#4335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4336 = PCURVE('',#4137,#4337);
#4337 = DEFINITIONAL_REPRESENTATION('',(#4338),#4342);
#4338 = LINE('',#4339,#4340);
#4339 = CARTESIAN_POINT('',(-15.5,25.));
#4340 = VECTOR('',#4341,1.);
#4341 = DIRECTION('',(0.,-1.));
#4342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4343 = ORIENTED_EDGE('',*,*,#4098,.T.);
#4344 = ADVANCED_FACE('',(#4345),#2712,.T.);
#4345 = FACE_BOUND('',#4346,.F.);
#4346 = EDGE_LOOP('',(#4347,#4371,#4372,#4373));
#4347 = ORIENTED_EDGE('',*,*,#4348,.T.);
#4348 = EDGE_CURVE('',#4349,#4052,#4351,.T.);
#4349 = VERTEX_POINT('',#4350);
#4350 = CARTESIAN_POINT('',(-51.,-19.21587885057,15.));
#4351 = SURFACE_CURVE('',#4352,(#4357,#4364),.PCURVE_S1.);
#4352 = CIRCLE('',#4353,54.5);
#4353 = AXIS2_PLACEMENT_3D('',#4354,#4355,#4356);
#4354 = CARTESIAN_POINT('',(0.,0.,15.));
#4355 = DIRECTION('',(0.,0.,1.));
#4356 = DIRECTION('',(1.,0.,0.));
#4357 = PCURVE('',#2712,#4358);
#4358 = DEFINITIONAL_REPRESENTATION('',(#4359),#4363);
#4359 = LINE('',#4360,#4361);
#4360 = CARTESIAN_POINT('',(-0.,-15.));
#4361 = VECTOR('',#4362,1.);
#4362 = DIRECTION('',(-1.,0.));
#4363 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4364 = PCURVE('',#3699,#4365);
#4365 = DEFINITIONAL_REPRESENTATION('',(#4366),#4370);
#4366 = CIRCLE('',#4367,54.5);
#4367 = AXIS2_PLACEMENT_2D('',#4368,#4369);
#4368 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#4369 = DIRECTION('',(1.,0.));
#4370 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4371 = ORIENTED_EDGE('',*,*,#4076,.T.);
#4372 = ORIENTED_EDGE('',*,*,#2695,.F.);
#4373 = ORIENTED_EDGE('',*,*,#4374,.F.);
#4374 = EDGE_CURVE('',#4349,#2668,#4375,.T.);
#4375 = SURFACE_CURVE('',#4376,(#4380,#4387),.PCURVE_S1.);
#4376 = LINE('',#4377,#4378);
#4377 = CARTESIAN_POINT('',(-51.,-19.21587885057,0.));
#4378 = VECTOR('',#4379,1.);
#4379 = DIRECTION('',(-0.,-0.,-1.));
#4380 = PCURVE('',#2712,#4381);
#4381 = DEFINITIONAL_REPRESENTATION('',(#4382),#4386);
#4382 = LINE('',#4383,#4384);
#4383 = CARTESIAN_POINT('',(-3.501924655126,0.));
#4384 = VECTOR('',#4385,1.);
#4385 = DIRECTION('',(-0.,1.));
#4386 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4387 = PCURVE('',#2683,#4388);
#4388 = DEFINITIONAL_REPRESENTATION('',(#4389),#4393);
#4389 = LINE('',#4390,#4391);
#4390 = CARTESIAN_POINT('',(69.215878850576,-15.));
#4391 = VECTOR('',#4392,1.);
#4392 = DIRECTION('',(0.,-1.));
#4393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4394 = ADVANCED_FACE('',(#4395),#2683,.T.);
#4395 = FACE_BOUND('',#4396,.T.);
#4396 = EDGE_LOOP('',(#4397,#4398,#4399,#4400,#4401,#4424));
#4397 = ORIENTED_EDGE('',*,*,#3553,.F.);
#4398 = ORIENTED_EDGE('',*,*,#3965,.T.);
#4399 = ORIENTED_EDGE('',*,*,#2667,.T.);
#4400 = ORIENTED_EDGE('',*,*,#4374,.F.);
#4401 = ORIENTED_EDGE('',*,*,#4402,.F.);
#4402 = EDGE_CURVE('',#4403,#4349,#4405,.T.);
#4403 = VERTEX_POINT('',#4404);
#4404 = CARTESIAN_POINT('',(-51.,-7.509772,15.));
#4405 = SURFACE_CURVE('',#4406,(#4410,#4417),.PCURVE_S1.);
#4406 = LINE('',#4407,#4408);
#4407 = CARTESIAN_POINT('',(-51.,50.,15.));
#4408 = VECTOR('',#4409,1.);
#4409 = DIRECTION('',(0.,-1.,0.));
#4410 = PCURVE('',#2683,#4411);
#4411 = DEFINITIONAL_REPRESENTATION('',(#4412),#4416);
#4412 = LINE('',#4413,#4414);
#4413 = CARTESIAN_POINT('',(0.,0.));
#4414 = VECTOR('',#4415,1.);
#4415 = DIRECTION('',(1.,0.));
#4416 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4417 = PCURVE('',#3699,#4418);
#4418 = DEFINITIONAL_REPRESENTATION('',(#4419),#4423);
#4419 = LINE('',#4420,#4421);
#4420 = CARTESIAN_POINT('',(-51.,50.));
#4421 = VECTOR('',#4422,1.);
#4422 = DIRECTION('',(0.,-1.));
#4423 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4424 = ORIENTED_EDGE('',*,*,#4425,.F.);
#4425 = EDGE_CURVE('',#3477,#4403,#4426,.T.);
#4426 = SURFACE_CURVE('',#4427,(#4431,#4438),.PCURVE_S1.);
#4427 = LINE('',#4428,#4429);
#4428 = CARTESIAN_POINT('',(-51.,50.,15.));
#4429 = VECTOR('',#4430,1.);
#4430 = DIRECTION('',(0.,-1.,0.));
#4431 = PCURVE('',#2683,#4432);
#4432 = DEFINITIONAL_REPRESENTATION('',(#4433),#4437);
#4433 = LINE('',#4434,#4435);
#4434 = CARTESIAN_POINT('',(0.,0.));
#4435 = VECTOR('',#4436,1.);
#4436 = DIRECTION('',(1.,0.));
#4437 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4438 = PCURVE('',#3494,#4439);
#4439 = DEFINITIONAL_REPRESENTATION('',(#4440),#4444);
#4440 = LINE('',#4441,#4442);
#4441 = CARTESIAN_POINT('',(-51.,50.));
#4442 = VECTOR('',#4443,1.);
#4443 = DIRECTION('',(0.,-1.));
#4444 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4445 = ADVANCED_FACE('',(#4446),#2850,.F.);
#4446 = FACE_BOUND('',#4447,.F.);
#4447 = EDGE_LOOP('',(#4448,#4471,#4472,#4473,#4502,#4523,#4524,#4547));
#4448 = ORIENTED_EDGE('',*,*,#4449,.F.);
#4449 = EDGE_CURVE('',#3629,#4450,#4452,.T.);
#4450 = VERTEX_POINT('',#4451);
#4451 = CARTESIAN_POINT('',(-11.5,22.,5.));
#4452 = SEAM_CURVE('',#4453,(#4457,#4464),.PCURVE_S1.);
#4453 = LINE('',#4454,#4455);
#4454 = CARTESIAN_POINT('',(-11.5,22.,15.));
#4455 = VECTOR('',#4456,1.);
#4456 = DIRECTION('',(-0.,-0.,-1.));
#4457 = PCURVE('',#2850,#4458);
#4458 = DEFINITIONAL_REPRESENTATION('',(#4459),#4463);
#4459 = LINE('',#4460,#4461);
#4460 = CARTESIAN_POINT('',(6.28318530718,0.));
#4461 = VECTOR('',#4462,1.);
#4462 = DIRECTION('',(0.,-1.));
#4463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4464 = PCURVE('',#2850,#4465);
#4465 = DEFINITIONAL_REPRESENTATION('',(#4466),#4470);
#4466 = LINE('',#4467,#4468);
#4467 = CARTESIAN_POINT('',(0.,0.));
#4468 = VECTOR('',#4469,1.);
#4469 = DIRECTION('',(0.,-1.));
#4470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4471 = ORIENTED_EDGE('',*,*,#3628,.F.);
#4472 = ORIENTED_EDGE('',*,*,#4449,.T.);
#4473 = ORIENTED_EDGE('',*,*,#4474,.T.);
#4474 = EDGE_CURVE('',#4450,#4475,#4477,.T.);
#4475 = VERTEX_POINT('',#4476);
#4476 = CARTESIAN_POINT('',(-15.5,30.,5.));
#4477 = SURFACE_CURVE('',#4478,(#4483,#4490),.PCURVE_S1.);
#4478 = CIRCLE('',#4479,10.);
#4479 = AXIS2_PLACEMENT_3D('',#4480,#4481,#4482);
#4480 = CARTESIAN_POINT('',(-21.5,22.,5.));
#4481 = DIRECTION('',(0.,0.,1.));
#4482 = DIRECTION('',(1.,0.,0.));
#4483 = PCURVE('',#2850,#4484);
#4484 = DEFINITIONAL_REPRESENTATION('',(#4485),#4489);
#4485 = LINE('',#4486,#4487);
#4486 = CARTESIAN_POINT('',(0.,-10.));
#4487 = VECTOR('',#4488,1.);
#4488 = DIRECTION('',(1.,0.));
#4489 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4490 = PCURVE('',#4491,#4496);
#4491 = PLANE('',#4492);
#4492 = AXIS2_PLACEMENT_3D('',#4493,#4494,#4495);
#4493 = CARTESIAN_POINT('',(0.,25.5,5.));
#4494 = DIRECTION('',(0.,0.,1.));
#4495 = DIRECTION('',(1.,0.,0.));
#4496 = DEFINITIONAL_REPRESENTATION('',(#4497),#4501);
#4497 = CIRCLE('',#4498,10.);
#4498 = AXIS2_PLACEMENT_2D('',#4499,#4500);
#4499 = CARTESIAN_POINT('',(-21.5,-3.5));
#4500 = DIRECTION('',(1.,0.));
#4501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4502 = ORIENTED_EDGE('',*,*,#4503,.F.);
#4503 = EDGE_CURVE('',#2832,#4475,#4504,.T.);
#4504 = SURFACE_CURVE('',#4505,(#4509,#4516),.PCURVE_S1.);
#4505 = LINE('',#4506,#4507);
#4506 = CARTESIAN_POINT('',(-15.5,30.,15.));
#4507 = VECTOR('',#4508,1.);
#4508 = DIRECTION('',(0.,0.,1.));
#4509 = PCURVE('',#2850,#4510);
#4510 = DEFINITIONAL_REPRESENTATION('',(#4511),#4515);
#4511 = LINE('',#4512,#4513);
#4512 = CARTESIAN_POINT('',(0.927295218002,0.));
#4513 = VECTOR('',#4514,1.);
#4514 = DIRECTION('',(0.,1.));
#4515 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4516 = PCURVE('',#2878,#4517);
#4517 = DEFINITIONAL_REPRESENTATION('',(#4518),#4522);
#4518 = LINE('',#4519,#4520);
#4519 = CARTESIAN_POINT('',(16.,-15.));
#4520 = VECTOR('',#4521,1.);
#4521 = DIRECTION('',(0.,-1.));
#4522 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4523 = ORIENTED_EDGE('',*,*,#2831,.T.);
#4524 = ORIENTED_EDGE('',*,*,#4525,.T.);
#4525 = EDGE_CURVE('',#2834,#4526,#4528,.T.);
#4526 = VERTEX_POINT('',#4527);
#4527 = CARTESIAN_POINT('',(-15.5,14.,5.));
#4528 = SURFACE_CURVE('',#4529,(#4533,#4540),.PCURVE_S1.);
#4529 = LINE('',#4530,#4531);
#4530 = CARTESIAN_POINT('',(-15.5,14.,15.));
#4531 = VECTOR('',#4532,1.);
#4532 = DIRECTION('',(0.,0.,1.));
#4533 = PCURVE('',#2850,#4534);
#4534 = DEFINITIONAL_REPRESENTATION('',(#4535),#4539);
#4535 = LINE('',#4536,#4537);
#4536 = CARTESIAN_POINT('',(5.355890089178,0.));
#4537 = VECTOR('',#4538,1.);
#4538 = DIRECTION('',(0.,1.));
#4539 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4540 = PCURVE('',#3069,#4541);
#4541 = DEFINITIONAL_REPRESENTATION('',(#4542),#4546);
#4542 = LINE('',#4543,#4544);
#4543 = CARTESIAN_POINT('',(32.,-15.));
#4544 = VECTOR('',#4545,1.);
#4545 = DIRECTION('',(0.,-1.));
#4546 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4547 = ORIENTED_EDGE('',*,*,#4548,.T.);
#4548 = EDGE_CURVE('',#4526,#4450,#4549,.T.);
#4549 = SURFACE_CURVE('',#4550,(#4555,#4562),.PCURVE_S1.);
#4550 = CIRCLE('',#4551,10.);
#4551 = AXIS2_PLACEMENT_3D('',#4552,#4553,#4554);
#4552 = CARTESIAN_POINT('',(-21.5,22.,5.));
#4553 = DIRECTION('',(0.,0.,1.));
#4554 = DIRECTION('',(1.,0.,0.));
#4555 = PCURVE('',#2850,#4556);
#4556 = DEFINITIONAL_REPRESENTATION('',(#4557),#4561);
#4557 = LINE('',#4558,#4559);
#4558 = CARTESIAN_POINT('',(0.,-10.));
#4559 = VECTOR('',#4560,1.);
#4560 = DIRECTION('',(1.,0.));
#4561 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4562 = PCURVE('',#4491,#4563);
#4563 = DEFINITIONAL_REPRESENTATION('',(#4564),#4568);
#4564 = CIRCLE('',#4565,10.);
#4565 = AXIS2_PLACEMENT_2D('',#4566,#4567);
#4566 = CARTESIAN_POINT('',(-21.5,-3.5));
#4567 = DIRECTION('',(1.,0.));
#4568 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4569 = ADVANCED_FACE('',(#4570),#2878,.T.);
#4570 = FACE_BOUND('',#4571,.T.);
#4571 = EDGE_LOOP('',(#4572,#4573,#4596,#4617));
#4572 = ORIENTED_EDGE('',*,*,#2862,.F.);
#4573 = ORIENTED_EDGE('',*,*,#4574,.T.);
#4574 = EDGE_CURVE('',#2863,#4575,#4577,.T.);
#4575 = VERTEX_POINT('',#4576);
#4576 = CARTESIAN_POINT('',(-15.5,46.,5.));
#4577 = SURFACE_CURVE('',#4578,(#4582,#4589),.PCURVE_S1.);
#4578 = LINE('',#4579,#4580);
#4579 = CARTESIAN_POINT('',(-15.5,46.,0.));
#4580 = VECTOR('',#4581,1.);
#4581 = DIRECTION('',(0.,0.,1.));
#4582 = PCURVE('',#2878,#4583);
#4583 = DEFINITIONAL_REPRESENTATION('',(#4584),#4588);
#4584 = LINE('',#4585,#4586);
#4585 = CARTESIAN_POINT('',(0.,0.));
#4586 = VECTOR('',#4587,1.);
#4587 = DIRECTION('',(0.,-1.));
#4588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4589 = PCURVE('',#2906,#4590);
#4590 = DEFINITIONAL_REPRESENTATION('',(#4591),#4595);
#4591 = LINE('',#4592,#4593);
#4592 = CARTESIAN_POINT('',(31.,0.));
#4593 = VECTOR('',#4594,1.);
#4594 = DIRECTION('',(0.,-1.));
#4595 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4596 = ORIENTED_EDGE('',*,*,#4597,.T.);
#4597 = EDGE_CURVE('',#4575,#4475,#4598,.T.);
#4598 = SURFACE_CURVE('',#4599,(#4603,#4610),.PCURVE_S1.);
#4599 = LINE('',#4600,#4601);
#4600 = CARTESIAN_POINT('',(-15.5,46.,5.));
#4601 = VECTOR('',#4602,1.);
#4602 = DIRECTION('',(0.,-1.,0.));
#4603 = PCURVE('',#2878,#4604);
#4604 = DEFINITIONAL_REPRESENTATION('',(#4605),#4609);
#4605 = LINE('',#4606,#4607);
#4606 = CARTESIAN_POINT('',(0.,-5.));
#4607 = VECTOR('',#4608,1.);
#4608 = DIRECTION('',(1.,0.));
#4609 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4610 = PCURVE('',#4491,#4611);
#4611 = DEFINITIONAL_REPRESENTATION('',(#4612),#4616);
#4612 = LINE('',#4613,#4614);
#4613 = CARTESIAN_POINT('',(-15.5,20.5));
#4614 = VECTOR('',#4615,1.);
#4615 = DIRECTION('',(0.,-1.));
#4616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4617 = ORIENTED_EDGE('',*,*,#4503,.F.);
#4618 = ADVANCED_FACE('',(#4619),#3069,.T.);
#4619 = FACE_BOUND('',#4620,.T.);
#4620 = EDGE_LOOP('',(#4621,#4622,#4623,#4646));
#4621 = ORIENTED_EDGE('',*,*,#3055,.F.);
#4622 = ORIENTED_EDGE('',*,*,#4525,.T.);
#4623 = ORIENTED_EDGE('',*,*,#4624,.T.);
#4624 = EDGE_CURVE('',#4526,#4625,#4627,.T.);
#4625 = VERTEX_POINT('',#4626);
#4626 = CARTESIAN_POINT('',(-15.5,5.,5.));
#4627 = SURFACE_CURVE('',#4628,(#4632,#4639),.PCURVE_S1.);
#4628 = LINE('',#4629,#4630);
#4629 = CARTESIAN_POINT('',(-15.5,46.,5.));
#4630 = VECTOR('',#4631,1.);
#4631 = DIRECTION('',(0.,-1.,0.));
#4632 = PCURVE('',#3069,#4633);
#4633 = DEFINITIONAL_REPRESENTATION('',(#4634),#4638);
#4634 = LINE('',#4635,#4636);
#4635 = CARTESIAN_POINT('',(0.,-5.));
#4636 = VECTOR('',#4637,1.);
#4637 = DIRECTION('',(1.,0.));
#4638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4639 = PCURVE('',#4491,#4640);
#4640 = DEFINITIONAL_REPRESENTATION('',(#4641),#4645);
#4641 = LINE('',#4642,#4643);
#4642 = CARTESIAN_POINT('',(-15.5,20.5));
#4643 = VECTOR('',#4644,1.);
#4644 = DIRECTION('',(0.,-1.));
#4645 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4646 = ORIENTED_EDGE('',*,*,#4647,.F.);
#4647 = EDGE_CURVE('',#3028,#4625,#4648,.T.);
#4648 = SURFACE_CURVE('',#4649,(#4653,#4660),.PCURVE_S1.);
#4649 = LINE('',#4650,#4651);
#4650 = CARTESIAN_POINT('',(-15.5,5.,0.));
#4651 = VECTOR('',#4652,1.);
#4652 = DIRECTION('',(0.,0.,1.));
#4653 = PCURVE('',#3069,#4654);
#4654 = DEFINITIONAL_REPRESENTATION('',(#4655),#4659);
#4655 = LINE('',#4656,#4657);
#4656 = CARTESIAN_POINT('',(41.,0.));
#4657 = VECTOR('',#4658,1.);
#4658 = DIRECTION('',(0.,-1.));
#4659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4660 = PCURVE('',#3043,#4661);
#4661 = DEFINITIONAL_REPRESENTATION('',(#4662),#4666);
#4662 = LINE('',#4663,#4664);
#4663 = CARTESIAN_POINT('',(0.,0.));
#4664 = VECTOR('',#4665,1.);
#4665 = DIRECTION('',(0.,-1.));
#4666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4667 = ADVANCED_FACE('',(#4668),#2906,.T.);
#4668 = FACE_BOUND('',#4669,.T.);
#4669 = EDGE_LOOP('',(#4670,#4693,#4714,#4715));
#4670 = ORIENTED_EDGE('',*,*,#4671,.T.);
#4671 = EDGE_CURVE('',#2891,#4672,#4674,.T.);
#4672 = VERTEX_POINT('',#4673);
#4673 = CARTESIAN_POINT('',(15.5,46.,5.));
#4674 = SURFACE_CURVE('',#4675,(#4679,#4686),.PCURVE_S1.);
#4675 = LINE('',#4676,#4677);
#4676 = CARTESIAN_POINT('',(15.5,46.,0.));
#4677 = VECTOR('',#4678,1.);
#4678 = DIRECTION('',(0.,0.,1.));
#4679 = PCURVE('',#2906,#4680);
#4680 = DEFINITIONAL_REPRESENTATION('',(#4681),#4685);
#4681 = LINE('',#4682,#4683);
#4682 = CARTESIAN_POINT('',(0.,-0.));
#4683 = VECTOR('',#4684,1.);
#4684 = DIRECTION('',(0.,-1.));
#4685 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4686 = PCURVE('',#2934,#4687);
#4687 = DEFINITIONAL_REPRESENTATION('',(#4688),#4692);
#4688 = LINE('',#4689,#4690);
#4689 = CARTESIAN_POINT('',(41.,0.));
#4690 = VECTOR('',#4691,1.);
#4691 = DIRECTION('',(0.,-1.));
#4692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4693 = ORIENTED_EDGE('',*,*,#4694,.T.);
#4694 = EDGE_CURVE('',#4672,#4575,#4695,.T.);
#4695 = SURFACE_CURVE('',#4696,(#4700,#4707),.PCURVE_S1.);
#4696 = LINE('',#4697,#4698);
#4697 = CARTESIAN_POINT('',(15.5,46.,5.));
#4698 = VECTOR('',#4699,1.);
#4699 = DIRECTION('',(-1.,0.,0.));
#4700 = PCURVE('',#2906,#4701);
#4701 = DEFINITIONAL_REPRESENTATION('',(#4702),#4706);
#4702 = LINE('',#4703,#4704);
#4703 = CARTESIAN_POINT('',(0.,-5.));
#4704 = VECTOR('',#4705,1.);
#4705 = DIRECTION('',(1.,0.));
#4706 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4707 = PCURVE('',#4491,#4708);
#4708 = DEFINITIONAL_REPRESENTATION('',(#4709),#4713);
#4709 = LINE('',#4710,#4711);
#4710 = CARTESIAN_POINT('',(15.5,20.5));
#4711 = VECTOR('',#4712,1.);
#4712 = DIRECTION('',(-1.,0.));
#4713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4714 = ORIENTED_EDGE('',*,*,#4574,.F.);
#4715 = ORIENTED_EDGE('',*,*,#2890,.F.);
#4716 = ADVANCED_FACE('',(#4717),#3043,.T.);
#4717 = FACE_BOUND('',#4718,.T.);
#4718 = EDGE_LOOP('',(#4719,#4720,#4743,#4764));
#4719 = ORIENTED_EDGE('',*,*,#4647,.T.);
#4720 = ORIENTED_EDGE('',*,*,#4721,.T.);
#4721 = EDGE_CURVE('',#4625,#4722,#4724,.T.);
#4722 = VERTEX_POINT('',#4723);
#4723 = CARTESIAN_POINT('',(15.5,5.,5.));
#4724 = SURFACE_CURVE('',#4725,(#4729,#4736),.PCURVE_S1.);
#4725 = LINE('',#4726,#4727);
#4726 = CARTESIAN_POINT('',(-15.5,5.,5.));
#4727 = VECTOR('',#4728,1.);
#4728 = DIRECTION('',(1.,0.,0.));
#4729 = PCURVE('',#3043,#4730);
#4730 = DEFINITIONAL_REPRESENTATION('',(#4731),#4735);
#4731 = LINE('',#4732,#4733);
#4732 = CARTESIAN_POINT('',(0.,-5.));
#4733 = VECTOR('',#4734,1.);
#4734 = DIRECTION('',(1.,0.));
#4735 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4736 = PCURVE('',#4491,#4737);
#4737 = DEFINITIONAL_REPRESENTATION('',(#4738),#4742);
#4738 = LINE('',#4739,#4740);
#4739 = CARTESIAN_POINT('',(-15.5,-20.5));
#4740 = VECTOR('',#4741,1.);
#4741 = DIRECTION('',(1.,0.));
#4742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4743 = ORIENTED_EDGE('',*,*,#4744,.F.);
#4744 = EDGE_CURVE('',#3000,#4722,#4745,.T.);
#4745 = SURFACE_CURVE('',#4746,(#4750,#4757),.PCURVE_S1.);
#4746 = LINE('',#4747,#4748);
#4747 = CARTESIAN_POINT('',(15.5,5.,0.));
#4748 = VECTOR('',#4749,1.);
#4749 = DIRECTION('',(0.,0.,1.));
#4750 = PCURVE('',#3043,#4751);
#4751 = DEFINITIONAL_REPRESENTATION('',(#4752),#4756);
#4752 = LINE('',#4753,#4754);
#4753 = CARTESIAN_POINT('',(31.,0.));
#4754 = VECTOR('',#4755,1.);
#4755 = DIRECTION('',(0.,-1.));
#4756 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4757 = PCURVE('',#3015,#4758);
#4758 = DEFINITIONAL_REPRESENTATION('',(#4759),#4763);
#4759 = LINE('',#4760,#4761);
#4760 = CARTESIAN_POINT('',(0.,0.));
#4761 = VECTOR('',#4762,1.);
#4762 = DIRECTION('',(0.,-1.));
#4763 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4764 = ORIENTED_EDGE('',*,*,#3027,.F.);
#4765 = ADVANCED_FACE('',(#4766),#2934,.T.);
#4766 = FACE_BOUND('',#4767,.T.);
#4767 = EDGE_LOOP('',(#4768,#4769,#4792,#4813));
#4768 = ORIENTED_EDGE('',*,*,#2918,.F.);
#4769 = ORIENTED_EDGE('',*,*,#4770,.T.);
#4770 = EDGE_CURVE('',#2919,#4771,#4773,.T.);
#4771 = VERTEX_POINT('',#4772);
#4772 = CARTESIAN_POINT('',(15.5,30.,5.));
#4773 = SURFACE_CURVE('',#4774,(#4778,#4785),.PCURVE_S1.);
#4774 = LINE('',#4775,#4776);
#4775 = CARTESIAN_POINT('',(15.5,30.,15.));
#4776 = VECTOR('',#4777,1.);
#4777 = DIRECTION('',(0.,0.,1.));
#4778 = PCURVE('',#2934,#4779);
#4779 = DEFINITIONAL_REPRESENTATION('',(#4780),#4784);
#4780 = LINE('',#4781,#4782);
#4781 = CARTESIAN_POINT('',(25.,-15.));
#4782 = VECTOR('',#4783,1.);
#4783 = DIRECTION('',(0.,-1.));
#4784 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4785 = PCURVE('',#2963,#4786);
#4786 = DEFINITIONAL_REPRESENTATION('',(#4787),#4791);
#4787 = LINE('',#4788,#4789);
#4788 = CARTESIAN_POINT('',(2.214297435588,0.));
#4789 = VECTOR('',#4790,1.);
#4790 = DIRECTION('',(0.,1.));
#4791 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4792 = ORIENTED_EDGE('',*,*,#4793,.T.);
#4793 = EDGE_CURVE('',#4771,#4672,#4794,.T.);
#4794 = SURFACE_CURVE('',#4795,(#4799,#4806),.PCURVE_S1.);
#4795 = LINE('',#4796,#4797);
#4796 = CARTESIAN_POINT('',(15.5,5.,5.));
#4797 = VECTOR('',#4798,1.);
#4798 = DIRECTION('',(0.,1.,0.));
#4799 = PCURVE('',#2934,#4800);
#4800 = DEFINITIONAL_REPRESENTATION('',(#4801),#4805);
#4801 = LINE('',#4802,#4803);
#4802 = CARTESIAN_POINT('',(0.,-5.));
#4803 = VECTOR('',#4804,1.);
#4804 = DIRECTION('',(1.,0.));
#4805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4806 = PCURVE('',#4491,#4807);
#4807 = DEFINITIONAL_REPRESENTATION('',(#4808),#4812);
#4808 = LINE('',#4809,#4810);
#4809 = CARTESIAN_POINT('',(15.5,-20.5));
#4810 = VECTOR('',#4811,1.);
#4811 = DIRECTION('',(0.,1.));
#4812 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4813 = ORIENTED_EDGE('',*,*,#4671,.F.);
#4814 = ADVANCED_FACE('',(#4815),#3015,.T.);
#4815 = FACE_BOUND('',#4816,.T.);
#4816 = EDGE_LOOP('',(#4817,#4818,#4819,#4842));
#4817 = ORIENTED_EDGE('',*,*,#2999,.F.);
#4818 = ORIENTED_EDGE('',*,*,#4744,.T.);
#4819 = ORIENTED_EDGE('',*,*,#4820,.T.);
#4820 = EDGE_CURVE('',#4722,#4821,#4823,.T.);
#4821 = VERTEX_POINT('',#4822);
#4822 = CARTESIAN_POINT('',(15.5,14.,5.));
#4823 = SURFACE_CURVE('',#4824,(#4828,#4835),.PCURVE_S1.);
#4824 = LINE('',#4825,#4826);
#4825 = CARTESIAN_POINT('',(15.5,5.,5.));
#4826 = VECTOR('',#4827,1.);
#4827 = DIRECTION('',(0.,1.,0.));
#4828 = PCURVE('',#3015,#4829);
#4829 = DEFINITIONAL_REPRESENTATION('',(#4830),#4834);
#4830 = LINE('',#4831,#4832);
#4831 = CARTESIAN_POINT('',(0.,-5.));
#4832 = VECTOR('',#4833,1.);
#4833 = DIRECTION('',(1.,0.));
#4834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4835 = PCURVE('',#4491,#4836);
#4836 = DEFINITIONAL_REPRESENTATION('',(#4837),#4841);
#4837 = LINE('',#4838,#4839);
#4838 = CARTESIAN_POINT('',(15.5,-20.5));
#4839 = VECTOR('',#4840,1.);
#4840 = DIRECTION('',(0.,1.));
#4841 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4842 = ORIENTED_EDGE('',*,*,#4843,.F.);
#4843 = EDGE_CURVE('',#2976,#4821,#4844,.T.);
#4844 = SURFACE_CURVE('',#4845,(#4849,#4856),.PCURVE_S1.);
#4845 = LINE('',#4846,#4847);
#4846 = CARTESIAN_POINT('',(15.5,14.,15.));
#4847 = VECTOR('',#4848,1.);
#4848 = DIRECTION('',(0.,0.,1.));
#4849 = PCURVE('',#3015,#4850);
#4850 = DEFINITIONAL_REPRESENTATION('',(#4851),#4855);
#4851 = LINE('',#4852,#4853);
#4852 = CARTESIAN_POINT('',(9.,-15.));
#4853 = VECTOR('',#4854,1.);
#4854 = DIRECTION('',(0.,-1.));
#4855 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4856 = PCURVE('',#2963,#4857);
#4857 = DEFINITIONAL_REPRESENTATION('',(#4858),#4862);
#4858 = LINE('',#4859,#4860);
#4859 = CARTESIAN_POINT('',(4.068887871591,0.));
#4860 = VECTOR('',#4861,1.);
#4861 = DIRECTION('',(0.,1.));
#4862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4863 = ADVANCED_FACE('',(#4864),#2963,.F.);
#4864 = FACE_BOUND('',#4865,.F.);
#4865 = EDGE_LOOP('',(#4866,#4887,#4888,#4889,#4890,#4891,#4913,#4914));
#4866 = ORIENTED_EDGE('',*,*,#4867,.F.);
#4867 = EDGE_CURVE('',#3603,#2947,#4868,.T.);
#4868 = SEAM_CURVE('',#4869,(#4873,#4880),.PCURVE_S1.);
#4869 = LINE('',#4870,#4871);
#4870 = CARTESIAN_POINT('',(31.5,22.,15.));
#4871 = VECTOR('',#4872,1.);
#4872 = DIRECTION('',(-0.,-0.,-1.));
#4873 = PCURVE('',#2963,#4874);
#4874 = DEFINITIONAL_REPRESENTATION('',(#4875),#4879);
#4875 = LINE('',#4876,#4877);
#4876 = CARTESIAN_POINT('',(6.28318530718,0.));
#4877 = VECTOR('',#4878,1.);
#4878 = DIRECTION('',(0.,-1.));
#4879 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4880 = PCURVE('',#2963,#4881);
#4881 = DEFINITIONAL_REPRESENTATION('',(#4882),#4886);
#4882 = LINE('',#4883,#4884);
#4883 = CARTESIAN_POINT('',(0.,0.));
#4884 = VECTOR('',#4885,1.);
#4885 = DIRECTION('',(0.,-1.));
#4886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4887 = ORIENTED_EDGE('',*,*,#3602,.F.);
#4888 = ORIENTED_EDGE('',*,*,#4867,.T.);
#4889 = ORIENTED_EDGE('',*,*,#2946,.T.);
#4890 = ORIENTED_EDGE('',*,*,#4770,.T.);
#4891 = ORIENTED_EDGE('',*,*,#4892,.T.);
#4892 = EDGE_CURVE('',#4771,#4821,#4893,.T.);
#4893 = SURFACE_CURVE('',#4894,(#4899,#4906),.PCURVE_S1.);
#4894 = CIRCLE('',#4895,10.);
#4895 = AXIS2_PLACEMENT_3D('',#4896,#4897,#4898);
#4896 = CARTESIAN_POINT('',(21.5,22.,5.));
#4897 = DIRECTION('',(0.,0.,1.));
#4898 = DIRECTION('',(1.,0.,0.));
#4899 = PCURVE('',#2963,#4900);
#4900 = DEFINITIONAL_REPRESENTATION('',(#4901),#4905);
#4901 = LINE('',#4902,#4903);
#4902 = CARTESIAN_POINT('',(0.,-10.));
#4903 = VECTOR('',#4904,1.);
#4904 = DIRECTION('',(1.,0.));
#4905 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4906 = PCURVE('',#4491,#4907);
#4907 = DEFINITIONAL_REPRESENTATION('',(#4908),#4912);
#4908 = CIRCLE('',#4909,10.);
#4909 = AXIS2_PLACEMENT_2D('',#4910,#4911);
#4910 = CARTESIAN_POINT('',(21.5,-3.5));
#4911 = DIRECTION('',(1.,0.));
#4912 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4913 = ORIENTED_EDGE('',*,*,#4843,.F.);
#4914 = ORIENTED_EDGE('',*,*,#2975,.T.);
#4915 = ADVANCED_FACE('',(#4916),#3100,.F.);
#4916 = FACE_BOUND('',#4917,.F.);
#4917 = EDGE_LOOP('',(#4918,#4919,#4940,#4941));
#4918 = ORIENTED_EDGE('',*,*,#3654,.F.);
#4919 = ORIENTED_EDGE('',*,*,#4920,.T.);
#4920 = EDGE_CURVE('',#3655,#3084,#4921,.T.);
#4921 = SEAM_CURVE('',#4922,(#4926,#4933),.PCURVE_S1.);
#4922 = LINE('',#4923,#4924);
#4923 = CARTESIAN_POINT('',(-31.,33.,15.));
#4924 = VECTOR('',#4925,1.);
#4925 = DIRECTION('',(-0.,-0.,-1.));
#4926 = PCURVE('',#3100,#4927);
#4927 = DEFINITIONAL_REPRESENTATION('',(#4928),#4932);
#4928 = LINE('',#4929,#4930);
#4929 = CARTESIAN_POINT('',(6.28318530718,0.));
#4930 = VECTOR('',#4931,1.);
#4931 = DIRECTION('',(0.,-1.));
#4932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4933 = PCURVE('',#3100,#4934);
#4934 = DEFINITIONAL_REPRESENTATION('',(#4935),#4939);
#4935 = LINE('',#4936,#4937);
#4936 = CARTESIAN_POINT('',(0.,0.));
#4937 = VECTOR('',#4938,1.);
#4938 = DIRECTION('',(0.,-1.));
#4939 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4940 = ORIENTED_EDGE('',*,*,#3083,.T.);
#4941 = ORIENTED_EDGE('',*,*,#4920,.F.);
#4942 = ADVANCED_FACE('',(#4943),#3131,.F.);
#4943 = FACE_BOUND('',#4944,.F.);
#4944 = EDGE_LOOP('',(#4945,#4946,#4967,#4968));
#4945 = ORIENTED_EDGE('',*,*,#3576,.F.);
#4946 = ORIENTED_EDGE('',*,*,#4947,.T.);
#4947 = EDGE_CURVE('',#3577,#3115,#4948,.T.);
#4948 = SEAM_CURVE('',#4949,(#4953,#4960),.PCURVE_S1.);
#4949 = LINE('',#4950,#4951);
#4950 = CARTESIAN_POINT('',(35.,33.,15.));
#4951 = VECTOR('',#4952,1.);
#4952 = DIRECTION('',(-0.,-0.,-1.));
#4953 = PCURVE('',#3131,#4954);
#4954 = DEFINITIONAL_REPRESENTATION('',(#4955),#4959);
#4955 = LINE('',#4956,#4957);
#4956 = CARTESIAN_POINT('',(6.28318530718,0.));
#4957 = VECTOR('',#4958,1.);
#4958 = DIRECTION('',(0.,-1.));
#4959 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4960 = PCURVE('',#3131,#4961);
#4961 = DEFINITIONAL_REPRESENTATION('',(#4962),#4966);
#4962 = LINE('',#4963,#4964);
#4963 = CARTESIAN_POINT('',(0.,0.));
#4964 = VECTOR('',#4965,1.);
#4965 = DIRECTION('',(0.,-1.));
#4966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4967 = ORIENTED_EDGE('',*,*,#3114,.T.);
#4968 = ORIENTED_EDGE('',*,*,#4947,.F.);
#4969 = ADVANCED_FACE('',(#4970),#3162,.F.);
#4970 = FACE_BOUND('',#4971,.T.);
#4971 = EDGE_LOOP('',(#4972,#4995,#5017,#5018));
#4972 = ORIENTED_EDGE('',*,*,#4973,.T.);
#4973 = EDGE_CURVE('',#3146,#4974,#4976,.T.);
#4974 = VERTEX_POINT('',#4975);
#4975 = CARTESIAN_POINT('',(3.25,0.,15.));
#4976 = SEAM_CURVE('',#4977,(#4981,#4988),.PCURVE_S1.);
#4977 = LINE('',#4978,#4979);
#4978 = CARTESIAN_POINT('',(3.25,0.,0.));
#4979 = VECTOR('',#4980,1.);
#4980 = DIRECTION('',(0.,0.,1.));
#4981 = PCURVE('',#3162,#4982);
#4982 = DEFINITIONAL_REPRESENTATION('',(#4983),#4987);
#4983 = LINE('',#4984,#4985);
#4984 = CARTESIAN_POINT('',(-0.,0.));
#4985 = VECTOR('',#4986,1.);
#4986 = DIRECTION('',(-0.,-1.));
#4987 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4988 = PCURVE('',#3162,#4989);
#4989 = DEFINITIONAL_REPRESENTATION('',(#4990),#4994);
#4990 = LINE('',#4991,#4992);
#4991 = CARTESIAN_POINT('',(-6.28318530718,0.));
#4992 = VECTOR('',#4993,1.);
#4993 = DIRECTION('',(-0.,-1.));
#4994 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4995 = ORIENTED_EDGE('',*,*,#4996,.T.);
#4996 = EDGE_CURVE('',#4974,#4974,#4997,.T.);
#4997 = SURFACE_CURVE('',#4998,(#5003,#5010),.PCURVE_S1.);
#4998 = CIRCLE('',#4999,3.25);
#4999 = AXIS2_PLACEMENT_3D('',#5000,#5001,#5002);
#5000 = CARTESIAN_POINT('',(0.,0.,15.));
#5001 = DIRECTION('',(0.,0.,1.));
#5002 = DIRECTION('',(1.,0.,0.));
#5003 = PCURVE('',#3162,#5004);
#5004 = DEFINITIONAL_REPRESENTATION('',(#5005),#5009);
#5005 = LINE('',#5006,#5007);
#5006 = CARTESIAN_POINT('',(-0.,-15.));
#5007 = VECTOR('',#5008,1.);
#5008 = DIRECTION('',(-1.,0.));
#5009 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5010 = PCURVE('',#3494,#5011);
#5011 = DEFINITIONAL_REPRESENTATION('',(#5012),#5016);
#5012 = CIRCLE('',#5013,3.25);
#5013 = AXIS2_PLACEMENT_2D('',#5014,#5015);
#5014 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#5015 = DIRECTION('',(1.,0.));
#5016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5017 = ORIENTED_EDGE('',*,*,#4973,.F.);
#5018 = ORIENTED_EDGE('',*,*,#3145,.F.);
#5019 = ADVANCED_FACE('',(#5020),#3197,.F.);
#5020 = FACE_BOUND('',#5021,.F.);
#5021 = EDGE_LOOP('',(#5022,#5045,#5072,#5073));
#5022 = ORIENTED_EDGE('',*,*,#5023,.T.);
#5023 = EDGE_CURVE('',#3177,#5024,#5026,.T.);
#5024 = VERTEX_POINT('',#5025);
#5025 = CARTESIAN_POINT('',(33.556276,-39.287605,4.));
#5026 = SEAM_CURVE('',#5027,(#5031,#5038),.PCURVE_S1.);
#5027 = LINE('',#5028,#5029);
#5028 = CARTESIAN_POINT('',(33.556276,-39.287605,0.));
#5029 = VECTOR('',#5030,1.);
#5030 = DIRECTION('',(0.,0.,1.));
#5031 = PCURVE('',#3197,#5032);
#5032 = DEFINITIONAL_REPRESENTATION('',(#5033),#5037);
#5033 = LINE('',#5034,#5035);
#5034 = CARTESIAN_POINT('',(0.,0.));
#5035 = VECTOR('',#5036,1.);
#5036 = DIRECTION('',(0.,-1.));
#5037 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5038 = PCURVE('',#3197,#5039);
#5039 = DEFINITIONAL_REPRESENTATION('',(#5040),#5044);
#5040 = LINE('',#5041,#5042);
#5041 = CARTESIAN_POINT('',(6.28318530718,0.));
#5042 = VECTOR('',#5043,1.);
#5043 = DIRECTION('',(0.,-1.));
#5044 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5045 = ORIENTED_EDGE('',*,*,#5046,.T.);
#5046 = EDGE_CURVE('',#5024,#5024,#5047,.T.);
#5047 = SURFACE_CURVE('',#5048,(#5053,#5060),.PCURVE_S1.);
#5048 = CIRCLE('',#5049,1.475);
#5049 = AXIS2_PLACEMENT_3D('',#5050,#5051,#5052);
#5050 = CARTESIAN_POINT('',(32.081276,-39.287605,4.));
#5051 = DIRECTION('',(0.,0.,-1.));
#5052 = DIRECTION('',(1.,0.,0.));
#5053 = PCURVE('',#3197,#5054);
#5054 = DEFINITIONAL_REPRESENTATION('',(#5055),#5059);
#5055 = LINE('',#5056,#5057);
#5056 = CARTESIAN_POINT('',(0.,-4.));
#5057 = VECTOR('',#5058,1.);
#5058 = DIRECTION('',(1.,0.));
#5059 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5060 = PCURVE('',#5061,#5066);
#5061 = PLANE('',#5062);
#5062 = AXIS2_PLACEMENT_3D('',#5063,#5064,#5065);
#5063 = CARTESIAN_POINT('',(32.081276,-39.287605,4.));
#5064 = DIRECTION('',(-0.,-0.,-1.));
#5065 = DIRECTION('',(-1.,0.,0.));
#5066 = DEFINITIONAL_REPRESENTATION('',(#5067),#5071);
#5067 = CIRCLE('',#5068,1.475);
#5068 = AXIS2_PLACEMENT_2D('',#5069,#5070);
#5069 = CARTESIAN_POINT('',(0.,0.));
#5070 = DIRECTION('',(-1.,0.));
#5071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5072 = ORIENTED_EDGE('',*,*,#5023,.F.);
#5073 = ORIENTED_EDGE('',*,*,#3176,.F.);
#5074 = ADVANCED_FACE('',(#5075),#3228,.F.);
#5075 = FACE_BOUND('',#5076,.F.);
#5076 = EDGE_LOOP('',(#5077,#5101,#5122,#5123));
#5077 = ORIENTED_EDGE('',*,*,#5078,.F.);
#5078 = EDGE_CURVE('',#5079,#5079,#5081,.T.);
#5079 = VERTEX_POINT('',#5080);
#5080 = CARTESIAN_POINT('',(35.,-33.,15.));
#5081 = SURFACE_CURVE('',#5082,(#5087,#5094),.PCURVE_S1.);
#5082 = CIRCLE('',#5083,2.);
#5083 = AXIS2_PLACEMENT_3D('',#5084,#5085,#5086);
#5084 = CARTESIAN_POINT('',(33.,-33.,15.));
#5085 = DIRECTION('',(0.,0.,1.));
#5086 = DIRECTION('',(1.,0.,0.));
#5087 = PCURVE('',#3228,#5088);
#5088 = DEFINITIONAL_REPRESENTATION('',(#5089),#5093);
#5089 = LINE('',#5090,#5091);
#5090 = CARTESIAN_POINT('',(0.,0.));
#5091 = VECTOR('',#5092,1.);
#5092 = DIRECTION('',(1.,0.));
#5093 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5094 = PCURVE('',#3699,#5095);
#5095 = DEFINITIONAL_REPRESENTATION('',(#5096),#5100);
#5096 = CIRCLE('',#5097,2.);
#5097 = AXIS2_PLACEMENT_2D('',#5098,#5099);
#5098 = CARTESIAN_POINT('',(33.,-33.));
#5099 = DIRECTION('',(1.,0.));
#5100 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5101 = ORIENTED_EDGE('',*,*,#5102,.T.);
#5102 = EDGE_CURVE('',#5079,#3212,#5103,.T.);
#5103 = SEAM_CURVE('',#5104,(#5108,#5115),.PCURVE_S1.);
#5104 = LINE('',#5105,#5106);
#5105 = CARTESIAN_POINT('',(35.,-33.,15.));
#5106 = VECTOR('',#5107,1.);
#5107 = DIRECTION('',(-0.,-0.,-1.));
#5108 = PCURVE('',#3228,#5109);
#5109 = DEFINITIONAL_REPRESENTATION('',(#5110),#5114);
#5110 = LINE('',#5111,#5112);
#5111 = CARTESIAN_POINT('',(6.28318530718,0.));
#5112 = VECTOR('',#5113,1.);
#5113 = DIRECTION('',(0.,-1.));
#5114 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5115 = PCURVE('',#3228,#5116);
#5116 = DEFINITIONAL_REPRESENTATION('',(#5117),#5121);
#5117 = LINE('',#5118,#5119);
#5118 = CARTESIAN_POINT('',(0.,0.));
#5119 = VECTOR('',#5120,1.);
#5120 = DIRECTION('',(0.,-1.));
#5121 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5122 = ORIENTED_EDGE('',*,*,#3211,.T.);
#5123 = ORIENTED_EDGE('',*,*,#5102,.F.);
#5124 = ADVANCED_FACE('',(#5125),#3259,.F.);
#5125 = FACE_BOUND('',#5126,.F.);
#5126 = EDGE_LOOP('',(#5127,#5151,#5172,#5173));
#5127 = ORIENTED_EDGE('',*,*,#5128,.F.);
#5128 = EDGE_CURVE('',#5129,#5129,#5131,.T.);
#5129 = VERTEX_POINT('',#5130);
#5130 = CARTESIAN_POINT('',(-31.,-33.,15.));
#5131 = SURFACE_CURVE('',#5132,(#5137,#5144),.PCURVE_S1.);
#5132 = CIRCLE('',#5133,2.);
#5133 = AXIS2_PLACEMENT_3D('',#5134,#5135,#5136);
#5134 = CARTESIAN_POINT('',(-33.,-33.,15.));
#5135 = DIRECTION('',(0.,0.,1.));
#5136 = DIRECTION('',(1.,0.,0.));
#5137 = PCURVE('',#3259,#5138);
#5138 = DEFINITIONAL_REPRESENTATION('',(#5139),#5143);
#5139 = LINE('',#5140,#5141);
#5140 = CARTESIAN_POINT('',(0.,0.));
#5141 = VECTOR('',#5142,1.);
#5142 = DIRECTION('',(1.,0.));
#5143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5144 = PCURVE('',#3699,#5145);
#5145 = DEFINITIONAL_REPRESENTATION('',(#5146),#5150);
#5146 = CIRCLE('',#5147,2.);
#5147 = AXIS2_PLACEMENT_2D('',#5148,#5149);
#5148 = CARTESIAN_POINT('',(-33.,-33.));
#5149 = DIRECTION('',(1.,0.));
#5150 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5151 = ORIENTED_EDGE('',*,*,#5152,.T.);
#5152 = EDGE_CURVE('',#5129,#3243,#5153,.T.);
#5153 = SEAM_CURVE('',#5154,(#5158,#5165),.PCURVE_S1.);
#5154 = LINE('',#5155,#5156);
#5155 = CARTESIAN_POINT('',(-31.,-33.,15.));
#5156 = VECTOR('',#5157,1.);
#5157 = DIRECTION('',(-0.,-0.,-1.));
#5158 = PCURVE('',#3259,#5159);
#5159 = DEFINITIONAL_REPRESENTATION('',(#5160),#5164);
#5160 = LINE('',#5161,#5162);
#5161 = CARTESIAN_POINT('',(6.28318530718,0.));
#5162 = VECTOR('',#5163,1.);
#5163 = DIRECTION('',(0.,-1.));
#5164 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5165 = PCURVE('',#3259,#5166);
#5166 = DEFINITIONAL_REPRESENTATION('',(#5167),#5171);
#5167 = LINE('',#5168,#5169);
#5168 = CARTESIAN_POINT('',(0.,0.));
#5169 = VECTOR('',#5170,1.);
#5170 = DIRECTION('',(0.,-1.));
#5171 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5172 = ORIENTED_EDGE('',*,*,#3242,.T.);
#5173 = ORIENTED_EDGE('',*,*,#5152,.F.);
#5174 = ADVANCED_FACE('',(#5175),#3294,.F.);
#5175 = FACE_BOUND('',#5176,.F.);
#5176 = EDGE_LOOP('',(#5177,#5200,#5227,#5228));
#5177 = ORIENTED_EDGE('',*,*,#5178,.T.);
#5178 = EDGE_CURVE('',#3274,#5179,#5181,.T.);
#5179 = VERTEX_POINT('',#5180);
#5180 = CARTESIAN_POINT('',(-30.606276,-39.287605,4.));
#5181 = SEAM_CURVE('',#5182,(#5186,#5193),.PCURVE_S1.);
#5182 = LINE('',#5183,#5184);
#5183 = CARTESIAN_POINT('',(-30.606276,-39.287605,0.));
#5184 = VECTOR('',#5185,1.);
#5185 = DIRECTION('',(0.,0.,1.));
#5186 = PCURVE('',#3294,#5187);
#5187 = DEFINITIONAL_REPRESENTATION('',(#5188),#5192);
#5188 = LINE('',#5189,#5190);
#5189 = CARTESIAN_POINT('',(0.,0.));
#5190 = VECTOR('',#5191,1.);
#5191 = DIRECTION('',(0.,-1.));
#5192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5193 = PCURVE('',#3294,#5194);
#5194 = DEFINITIONAL_REPRESENTATION('',(#5195),#5199);
#5195 = LINE('',#5196,#5197);
#5196 = CARTESIAN_POINT('',(6.28318530718,0.));
#5197 = VECTOR('',#5198,1.);
#5198 = DIRECTION('',(0.,-1.));
#5199 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5200 = ORIENTED_EDGE('',*,*,#5201,.T.);
#5201 = EDGE_CURVE('',#5179,#5179,#5202,.T.);
#5202 = SURFACE_CURVE('',#5203,(#5208,#5215),.PCURVE_S1.);
#5203 = CIRCLE('',#5204,1.475);
#5204 = AXIS2_PLACEMENT_3D('',#5205,#5206,#5207);
#5205 = CARTESIAN_POINT('',(-32.081276,-39.287605,4.));
#5206 = DIRECTION('',(0.,0.,-1.));
#5207 = DIRECTION('',(1.,0.,0.));
#5208 = PCURVE('',#3294,#5209);
#5209 = DEFINITIONAL_REPRESENTATION('',(#5210),#5214);
#5210 = LINE('',#5211,#5212);
#5211 = CARTESIAN_POINT('',(0.,-4.));
#5212 = VECTOR('',#5213,1.);
#5213 = DIRECTION('',(1.,0.));
#5214 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5215 = PCURVE('',#5216,#5221);
#5216 = PLANE('',#5217);
#5217 = AXIS2_PLACEMENT_3D('',#5218,#5219,#5220);
#5218 = CARTESIAN_POINT('',(-32.081276,-39.287605,4.));
#5219 = DIRECTION('',(-0.,-0.,-1.));
#5220 = DIRECTION('',(-1.,0.,0.));
#5221 = DEFINITIONAL_REPRESENTATION('',(#5222),#5226);
#5222 = CIRCLE('',#5223,1.475);
#5223 = AXIS2_PLACEMENT_2D('',#5224,#5225);
#5224 = CARTESIAN_POINT('',(0.,-7.105427357601E-15));
#5225 = DIRECTION('',(-1.,0.));
#5226 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5227 = ORIENTED_EDGE('',*,*,#5178,.F.);
#5228 = ORIENTED_EDGE('',*,*,#3273,.F.);
#5229 = ADVANCED_FACE('',(#5230),#3325,.F.);
#5230 = FACE_BOUND('',#5231,.F.);
#5231 = EDGE_LOOP('',(#5232,#5255,#5279,#5303,#5325,#5326));
#5232 = ORIENTED_EDGE('',*,*,#5233,.F.);
#5233 = EDGE_CURVE('',#5234,#3309,#5236,.T.);
#5234 = VERTEX_POINT('',#5235);
#5235 = CARTESIAN_POINT('',(-21.5,-17.,15.));
#5236 = SEAM_CURVE('',#5237,(#5241,#5248),.PCURVE_S1.);
#5237 = LINE('',#5238,#5239);
#5238 = CARTESIAN_POINT('',(-21.5,-17.,15.));
#5239 = VECTOR('',#5240,1.);
#5240 = DIRECTION('',(-0.,-0.,-1.));
#5241 = PCURVE('',#3325,#5242);
#5242 = DEFINITIONAL_REPRESENTATION('',(#5243),#5247);
#5243 = LINE('',#5244,#5245);
#5244 = CARTESIAN_POINT('',(6.28318530718,0.));
#5245 = VECTOR('',#5246,1.);
#5246 = DIRECTION('',(0.,-1.));
#5247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5248 = PCURVE('',#3325,#5249);
#5249 = DEFINITIONAL_REPRESENTATION('',(#5250),#5254);
#5250 = LINE('',#5251,#5252);
#5251 = CARTESIAN_POINT('',(0.,0.));
#5252 = VECTOR('',#5253,1.);
#5253 = DIRECTION('',(0.,-1.));
#5254 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5255 = ORIENTED_EDGE('',*,*,#5256,.F.);
#5256 = EDGE_CURVE('',#5257,#5234,#5259,.T.);
#5257 = VERTEX_POINT('',#5258);
#5258 = CARTESIAN_POINT('',(-38.06197559397,-7.509772,15.));
#5259 = SURFACE_CURVE('',#5260,(#5265,#5272),.PCURVE_S1.);
#5260 = CIRCLE('',#5261,11.);
#5261 = AXIS2_PLACEMENT_3D('',#5262,#5263,#5264);
#5262 = CARTESIAN_POINT('',(-32.5,-17.,15.));
#5263 = DIRECTION('',(0.,0.,1.));
#5264 = DIRECTION('',(1.,0.,0.));
#5265 = PCURVE('',#3325,#5266);
#5266 = DEFINITIONAL_REPRESENTATION('',(#5267),#5271);
#5267 = LINE('',#5268,#5269);
#5268 = CARTESIAN_POINT('',(0.,0.));
#5269 = VECTOR('',#5270,1.);
#5270 = DIRECTION('',(1.,0.));
#5271 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5272 = PCURVE('',#3699,#5273);
#5273 = DEFINITIONAL_REPRESENTATION('',(#5274),#5278);
#5274 = CIRCLE('',#5275,11.);
#5275 = AXIS2_PLACEMENT_2D('',#5276,#5277);
#5276 = CARTESIAN_POINT('',(-32.5,-17.));
#5277 = DIRECTION('',(1.,0.));
#5278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5279 = ORIENTED_EDGE('',*,*,#5280,.F.);
#5280 = EDGE_CURVE('',#5281,#5257,#5283,.T.);
#5281 = VERTEX_POINT('',#5282);
#5282 = CARTESIAN_POINT('',(-26.93802440602,-7.509772,15.));
#5283 = SURFACE_CURVE('',#5284,(#5289,#5296),.PCURVE_S1.);
#5284 = CIRCLE('',#5285,11.);
#5285 = AXIS2_PLACEMENT_3D('',#5286,#5287,#5288);
#5286 = CARTESIAN_POINT('',(-32.5,-17.,15.));
#5287 = DIRECTION('',(0.,0.,1.));
#5288 = DIRECTION('',(1.,0.,0.));
#5289 = PCURVE('',#3325,#5290);
#5290 = DEFINITIONAL_REPRESENTATION('',(#5291),#5295);
#5291 = LINE('',#5292,#5293);
#5292 = CARTESIAN_POINT('',(0.,0.));
#5293 = VECTOR('',#5294,1.);
#5294 = DIRECTION('',(1.,0.));
#5295 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5296 = PCURVE('',#3494,#5297);
#5297 = DEFINITIONAL_REPRESENTATION('',(#5298),#5302);
#5298 = CIRCLE('',#5299,11.);
#5299 = AXIS2_PLACEMENT_2D('',#5300,#5301);
#5300 = CARTESIAN_POINT('',(-32.5,-17.));
#5301 = DIRECTION('',(1.,0.));
#5302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5303 = ORIENTED_EDGE('',*,*,#5304,.F.);
#5304 = EDGE_CURVE('',#5234,#5281,#5305,.T.);
#5305 = SURFACE_CURVE('',#5306,(#5311,#5318),.PCURVE_S1.);
#5306 = CIRCLE('',#5307,11.);
#5307 = AXIS2_PLACEMENT_3D('',#5308,#5309,#5310);
#5308 = CARTESIAN_POINT('',(-32.5,-17.,15.));
#5309 = DIRECTION('',(0.,0.,1.));
#5310 = DIRECTION('',(1.,0.,0.));
#5311 = PCURVE('',#3325,#5312);
#5312 = DEFINITIONAL_REPRESENTATION('',(#5313),#5317);
#5313 = LINE('',#5314,#5315);
#5314 = CARTESIAN_POINT('',(0.,0.));
#5315 = VECTOR('',#5316,1.);
#5316 = DIRECTION('',(1.,0.));
#5317 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5318 = PCURVE('',#3699,#5319);
#5319 = DEFINITIONAL_REPRESENTATION('',(#5320),#5324);
#5320 = CIRCLE('',#5321,11.);
#5321 = AXIS2_PLACEMENT_2D('',#5322,#5323);
#5322 = CARTESIAN_POINT('',(-32.5,-17.));
#5323 = DIRECTION('',(1.,0.));
#5324 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5325 = ORIENTED_EDGE('',*,*,#5233,.T.);
#5326 = ORIENTED_EDGE('',*,*,#3308,.T.);
#5327 = ADVANCED_FACE('',(#5328),#3356,.F.);
#5328 = FACE_BOUND('',#5329,.F.);
#5329 = EDGE_LOOP('',(#5330,#5353,#5377,#5401,#5423,#5424));
#5330 = ORIENTED_EDGE('',*,*,#5331,.F.);
#5331 = EDGE_CURVE('',#5332,#3340,#5334,.T.);
#5332 = VERTEX_POINT('',#5333);
#5333 = CARTESIAN_POINT('',(43.5,-17.,15.));
#5334 = SEAM_CURVE('',#5335,(#5339,#5346),.PCURVE_S1.);
#5335 = LINE('',#5336,#5337);
#5336 = CARTESIAN_POINT('',(43.5,-17.,15.));
#5337 = VECTOR('',#5338,1.);
#5338 = DIRECTION('',(-0.,-0.,-1.));
#5339 = PCURVE('',#3356,#5340);
#5340 = DEFINITIONAL_REPRESENTATION('',(#5341),#5345);
#5341 = LINE('',#5342,#5343);
#5342 = CARTESIAN_POINT('',(6.28318530718,0.));
#5343 = VECTOR('',#5344,1.);
#5344 = DIRECTION('',(0.,-1.));
#5345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5346 = PCURVE('',#3356,#5347);
#5347 = DEFINITIONAL_REPRESENTATION('',(#5348),#5352);
#5348 = LINE('',#5349,#5350);
#5349 = CARTESIAN_POINT('',(0.,0.));
#5350 = VECTOR('',#5351,1.);
#5351 = DIRECTION('',(0.,-1.));
#5352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5353 = ORIENTED_EDGE('',*,*,#5354,.F.);
#5354 = EDGE_CURVE('',#5355,#5332,#5357,.T.);
#5355 = VERTEX_POINT('',#5356);
#5356 = CARTESIAN_POINT('',(26.938024406021,-7.509772,15.));
#5357 = SURFACE_CURVE('',#5358,(#5363,#5370),.PCURVE_S1.);
#5358 = CIRCLE('',#5359,11.);
#5359 = AXIS2_PLACEMENT_3D('',#5360,#5361,#5362);
#5360 = CARTESIAN_POINT('',(32.5,-17.,15.));
#5361 = DIRECTION('',(0.,0.,1.));
#5362 = DIRECTION('',(1.,0.,0.));
#5363 = PCURVE('',#3356,#5364);
#5364 = DEFINITIONAL_REPRESENTATION('',(#5365),#5369);
#5365 = LINE('',#5366,#5367);
#5366 = CARTESIAN_POINT('',(0.,0.));
#5367 = VECTOR('',#5368,1.);
#5368 = DIRECTION('',(1.,0.));
#5369 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5370 = PCURVE('',#3699,#5371);
#5371 = DEFINITIONAL_REPRESENTATION('',(#5372),#5376);
#5372 = CIRCLE('',#5373,11.);
#5373 = AXIS2_PLACEMENT_2D('',#5374,#5375);
#5374 = CARTESIAN_POINT('',(32.5,-17.));
#5375 = DIRECTION('',(1.,0.));
#5376 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5377 = ORIENTED_EDGE('',*,*,#5378,.F.);
#5378 = EDGE_CURVE('',#5379,#5355,#5381,.T.);
#5379 = VERTEX_POINT('',#5380);
#5380 = CARTESIAN_POINT('',(38.061975593979,-7.509772,15.));
#5381 = SURFACE_CURVE('',#5382,(#5387,#5394),.PCURVE_S1.);
#5382 = CIRCLE('',#5383,11.);
#5383 = AXIS2_PLACEMENT_3D('',#5384,#5385,#5386);
#5384 = CARTESIAN_POINT('',(32.5,-17.,15.));
#5385 = DIRECTION('',(0.,0.,1.));
#5386 = DIRECTION('',(1.,0.,0.));
#5387 = PCURVE('',#3356,#5388);
#5388 = DEFINITIONAL_REPRESENTATION('',(#5389),#5393);
#5389 = LINE('',#5390,#5391);
#5390 = CARTESIAN_POINT('',(0.,0.));
#5391 = VECTOR('',#5392,1.);
#5392 = DIRECTION('',(1.,0.));
#5393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5394 = PCURVE('',#3494,#5395);
#5395 = DEFINITIONAL_REPRESENTATION('',(#5396),#5400);
#5396 = CIRCLE('',#5397,11.);
#5397 = AXIS2_PLACEMENT_2D('',#5398,#5399);
#5398 = CARTESIAN_POINT('',(32.5,-17.));
#5399 = DIRECTION('',(1.,0.));
#5400 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5401 = ORIENTED_EDGE('',*,*,#5402,.F.);
#5402 = EDGE_CURVE('',#5332,#5379,#5403,.T.);
#5403 = SURFACE_CURVE('',#5404,(#5409,#5416),.PCURVE_S1.);
#5404 = CIRCLE('',#5405,11.);
#5405 = AXIS2_PLACEMENT_3D('',#5406,#5407,#5408);
#5406 = CARTESIAN_POINT('',(32.5,-17.,15.));
#5407 = DIRECTION('',(0.,0.,1.));
#5408 = DIRECTION('',(1.,0.,0.));
#5409 = PCURVE('',#3356,#5410);
#5410 = DEFINITIONAL_REPRESENTATION('',(#5411),#5415);
#5411 = LINE('',#5412,#5413);
#5412 = CARTESIAN_POINT('',(0.,0.));
#5413 = VECTOR('',#5414,1.);
#5414 = DIRECTION('',(1.,0.));
#5415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5416 = PCURVE('',#3699,#5417);
#5417 = DEFINITIONAL_REPRESENTATION('',(#5418),#5422);
#5418 = CIRCLE('',#5419,11.);
#5419 = AXIS2_PLACEMENT_2D('',#5420,#5421);
#5420 = CARTESIAN_POINT('',(32.5,-17.));
#5421 = DIRECTION('',(1.,0.));
#5422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5423 = ORIENTED_EDGE('',*,*,#5331,.T.);
#5424 = ORIENTED_EDGE('',*,*,#3339,.T.);
#5425 = ADVANCED_FACE('',(#5426),#3391,.F.);
#5426 = FACE_BOUND('',#5427,.F.);
#5427 = EDGE_LOOP('',(#5428,#5451,#5478,#5479));
#5428 = ORIENTED_EDGE('',*,*,#5429,.T.);
#5429 = EDGE_CURVE('',#3371,#5430,#5432,.T.);
#5430 = VERTEX_POINT('',#5431);
#5431 = CARTESIAN_POINT('',(1.475,50.726503,4.));
#5432 = SEAM_CURVE('',#5433,(#5437,#5444),.PCURVE_S1.);
#5433 = LINE('',#5434,#5435);
#5434 = CARTESIAN_POINT('',(1.475,50.726503,0.));
#5435 = VECTOR('',#5436,1.);
#5436 = DIRECTION('',(0.,0.,1.));
#5437 = PCURVE('',#3391,#5438);
#5438 = DEFINITIONAL_REPRESENTATION('',(#5439),#5443);
#5439 = LINE('',#5440,#5441);
#5440 = CARTESIAN_POINT('',(0.,0.));
#5441 = VECTOR('',#5442,1.);
#5442 = DIRECTION('',(0.,-1.));
#5443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5444 = PCURVE('',#3391,#5445);
#5445 = DEFINITIONAL_REPRESENTATION('',(#5446),#5450);
#5446 = LINE('',#5447,#5448);
#5447 = CARTESIAN_POINT('',(6.28318530718,0.));
#5448 = VECTOR('',#5449,1.);
#5449 = DIRECTION('',(0.,-1.));
#5450 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5451 = ORIENTED_EDGE('',*,*,#5452,.T.);
#5452 = EDGE_CURVE('',#5430,#5430,#5453,.T.);
#5453 = SURFACE_CURVE('',#5454,(#5459,#5466),.PCURVE_S1.);
#5454 = CIRCLE('',#5455,1.475);
#5455 = AXIS2_PLACEMENT_3D('',#5456,#5457,#5458);
#5456 = CARTESIAN_POINT('',(0.,50.726503,4.));
#5457 = DIRECTION('',(0.,0.,-1.));
#5458 = DIRECTION('',(1.,0.,0.));
#5459 = PCURVE('',#3391,#5460);
#5460 = DEFINITIONAL_REPRESENTATION('',(#5461),#5465);
#5461 = LINE('',#5462,#5463);
#5462 = CARTESIAN_POINT('',(0.,-4.));
#5463 = VECTOR('',#5464,1.);
#5464 = DIRECTION('',(1.,0.));
#5465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5466 = PCURVE('',#5467,#5472);
#5467 = PLANE('',#5468);
#5468 = AXIS2_PLACEMENT_3D('',#5469,#5470,#5471);
#5469 = CARTESIAN_POINT('',(-3.5E-16,50.726503,4.));
#5470 = DIRECTION('',(-0.,-0.,-1.));
#5471 = DIRECTION('',(-1.,0.,0.));
#5472 = DEFINITIONAL_REPRESENTATION('',(#5473),#5477);
#5473 = CIRCLE('',#5474,1.475);
#5474 = AXIS2_PLACEMENT_2D('',#5475,#5476);
#5475 = CARTESIAN_POINT('',(-3.5E-16,-7.105427357601E-15));
#5476 = DIRECTION('',(-1.,0.));
#5477 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5478 = ORIENTED_EDGE('',*,*,#5429,.F.);
#5479 = ORIENTED_EDGE('',*,*,#3370,.F.);
#5480 = ADVANCED_FACE('',(#5481),#3426,.F.);
#5481 = FACE_BOUND('',#5482,.F.);
#5482 = EDGE_LOOP('',(#5483,#5506,#5533,#5534));
#5483 = ORIENTED_EDGE('',*,*,#5484,.T.);
#5484 = EDGE_CURVE('',#3406,#5485,#5487,.T.);
#5485 = VERTEX_POINT('',#5486);
#5486 = CARTESIAN_POINT('',(-45.328799,0.,4.));
#5487 = SEAM_CURVE('',#5488,(#5492,#5499),.PCURVE_S1.);
#5488 = LINE('',#5489,#5490);
#5489 = CARTESIAN_POINT('',(-45.328799,0.,0.));
#5490 = VECTOR('',#5491,1.);
#5491 = DIRECTION('',(0.,0.,1.));
#5492 = PCURVE('',#3426,#5493);
#5493 = DEFINITIONAL_REPRESENTATION('',(#5494),#5498);
#5494 = LINE('',#5495,#5496);
#5495 = CARTESIAN_POINT('',(0.,0.));
#5496 = VECTOR('',#5497,1.);
#5497 = DIRECTION('',(0.,-1.));
#5498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5499 = PCURVE('',#3426,#5500);
#5500 = DEFINITIONAL_REPRESENTATION('',(#5501),#5505);
#5501 = LINE('',#5502,#5503);
#5502 = CARTESIAN_POINT('',(6.28318530718,0.));
#5503 = VECTOR('',#5504,1.);
#5504 = DIRECTION('',(0.,-1.));
#5505 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5506 = ORIENTED_EDGE('',*,*,#5507,.T.);
#5507 = EDGE_CURVE('',#5485,#5485,#5508,.T.);
#5508 = SURFACE_CURVE('',#5509,(#5514,#5521),.PCURVE_S1.);
#5509 = CIRCLE('',#5510,1.475);
#5510 = AXIS2_PLACEMENT_3D('',#5511,#5512,#5513);
#5511 = CARTESIAN_POINT('',(-46.803799,0.,4.));
#5512 = DIRECTION('',(0.,0.,-1.));
#5513 = DIRECTION('',(1.,0.,0.));
#5514 = PCURVE('',#3426,#5515);
#5515 = DEFINITIONAL_REPRESENTATION('',(#5516),#5520);
#5516 = LINE('',#5517,#5518);
#5517 = CARTESIAN_POINT('',(0.,-4.));
#5518 = VECTOR('',#5519,1.);
#5519 = DIRECTION('',(1.,0.));
#5520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5521 = PCURVE('',#5522,#5527);
#5522 = PLANE('',#5523);
#5523 = AXIS2_PLACEMENT_3D('',#5524,#5525,#5526);
#5524 = CARTESIAN_POINT('',(-46.803799,-1.1E-16,4.));
#5525 = DIRECTION('',(-0.,-0.,-1.));
#5526 = DIRECTION('',(-1.,0.,0.));
#5527 = DEFINITIONAL_REPRESENTATION('',(#5528),#5532);
#5528 = CIRCLE('',#5529,1.475);
#5529 = AXIS2_PLACEMENT_2D('',#5530,#5531);
#5530 = CARTESIAN_POINT('',(0.,1.1E-16));
#5531 = DIRECTION('',(-1.,0.));
#5532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5533 = ORIENTED_EDGE('',*,*,#5484,.F.);
#5534 = ORIENTED_EDGE('',*,*,#3405,.F.);
#5535 = ADVANCED_FACE('',(#5536),#3461,.F.);
#5536 = FACE_BOUND('',#5537,.F.);
#5537 = EDGE_LOOP('',(#5538,#5561,#5588,#5589));
#5538 = ORIENTED_EDGE('',*,*,#5539,.T.);
#5539 = EDGE_CURVE('',#3441,#5540,#5542,.T.);
#5540 = VERTEX_POINT('',#5541);
#5541 = CARTESIAN_POINT('',(48.278799,0.,4.));
#5542 = SEAM_CURVE('',#5543,(#5547,#5554),.PCURVE_S1.);
#5543 = LINE('',#5544,#5545);
#5544 = CARTESIAN_POINT('',(48.278799,0.,0.));
#5545 = VECTOR('',#5546,1.);
#5546 = DIRECTION('',(0.,0.,1.));
#5547 = PCURVE('',#3461,#5548);
#5548 = DEFINITIONAL_REPRESENTATION('',(#5549),#5553);
#5549 = LINE('',#5550,#5551);
#5550 = CARTESIAN_POINT('',(0.,0.));
#5551 = VECTOR('',#5552,1.);
#5552 = DIRECTION('',(0.,-1.));
#5553 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5554 = PCURVE('',#3461,#5555);
#5555 = DEFINITIONAL_REPRESENTATION('',(#5556),#5560);
#5556 = LINE('',#5557,#5558);
#5557 = CARTESIAN_POINT('',(6.28318530718,0.));
#5558 = VECTOR('',#5559,1.);
#5559 = DIRECTION('',(0.,-1.));
#5560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5561 = ORIENTED_EDGE('',*,*,#5562,.T.);
#5562 = EDGE_CURVE('',#5540,#5540,#5563,.T.);
#5563 = SURFACE_CURVE('',#5564,(#5569,#5576),.PCURVE_S1.);
#5564 = CIRCLE('',#5565,1.475);
#5565 = AXIS2_PLACEMENT_3D('',#5566,#5567,#5568);
#5566 = CARTESIAN_POINT('',(46.803799,0.,4.));
#5567 = DIRECTION('',(0.,0.,-1.));
#5568 = DIRECTION('',(1.,0.,0.));
#5569 = PCURVE('',#3461,#5570);
#5570 = DEFINITIONAL_REPRESENTATION('',(#5571),#5575);
#5571 = LINE('',#5572,#5573);
#5572 = CARTESIAN_POINT('',(0.,-4.));
#5573 = VECTOR('',#5574,1.);
#5574 = DIRECTION('',(1.,0.));
#5575 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5576 = PCURVE('',#5577,#5582);
#5577 = PLANE('',#5578);
#5578 = AXIS2_PLACEMENT_3D('',#5579,#5580,#5581);
#5579 = CARTESIAN_POINT('',(46.803799,-1.1E-16,4.));
#5580 = DIRECTION('',(-0.,-0.,-1.));
#5581 = DIRECTION('',(-1.,0.,0.));
#5582 = DEFINITIONAL_REPRESENTATION('',(#5583),#5587);
#5583 = CIRCLE('',#5584,1.475);
#5584 = AXIS2_PLACEMENT_2D('',#5585,#5586);
#5585 = CARTESIAN_POINT('',(7.105427357601E-15,1.1E-16));
#5586 = DIRECTION('',(-1.,0.));
#5587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5588 = ORIENTED_EDGE('',*,*,#5539,.F.);
#5589 = ORIENTED_EDGE('',*,*,#3440,.F.);
#5590 = ADVANCED_FACE('',(#5591,#5661,#5664,#5699),#3494,.T.);
#5591 = FACE_BOUND('',#5592,.T.);
#5592 = EDGE_LOOP('',(#5593,#5614,#5615,#5616,#5617,#5638,#5639,#5660));
#5593 = ORIENTED_EDGE('',*,*,#5594,.F.);
#5594 = EDGE_CURVE('',#3684,#5379,#5595,.T.);
#5595 = SURFACE_CURVE('',#5596,(#5600,#5607),.PCURVE_S1.);
#5596 = LINE('',#5597,#5598);
#5597 = CARTESIAN_POINT('',(61.63348,-7.509772,15.));
#5598 = VECTOR('',#5599,1.);
#5599 = DIRECTION('',(-1.,0.,0.));
#5600 = PCURVE('',#3494,#5601);
#5601 = DEFINITIONAL_REPRESENTATION('',(#5602),#5606);
#5602 = LINE('',#5603,#5604);
#5603 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5604 = VECTOR('',#5605,1.);
#5605 = DIRECTION('',(-1.,0.));
#5606 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5607 = PCURVE('',#3699,#5608);
#5608 = DEFINITIONAL_REPRESENTATION('',(#5609),#5613);
#5609 = LINE('',#5610,#5611);
#5610 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5611 = VECTOR('',#5612,1.);
#5612 = DIRECTION('',(-1.,0.));
#5613 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5614 = ORIENTED_EDGE('',*,*,#3735,.T.);
#5615 = ORIENTED_EDGE('',*,*,#3476,.F.);
#5616 = ORIENTED_EDGE('',*,*,#4425,.T.);
#5617 = ORIENTED_EDGE('',*,*,#5618,.F.);
#5618 = EDGE_CURVE('',#5257,#4403,#5619,.T.);
#5619 = SURFACE_CURVE('',#5620,(#5624,#5631),.PCURVE_S1.);
#5620 = LINE('',#5621,#5622);
#5621 = CARTESIAN_POINT('',(61.63348,-7.509772,15.));
#5622 = VECTOR('',#5623,1.);
#5623 = DIRECTION('',(-1.,0.,0.));
#5624 = PCURVE('',#3494,#5625);
#5625 = DEFINITIONAL_REPRESENTATION('',(#5626),#5630);
#5626 = LINE('',#5627,#5628);
#5627 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5628 = VECTOR('',#5629,1.);
#5629 = DIRECTION('',(-1.,0.));
#5630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5631 = PCURVE('',#3699,#5632);
#5632 = DEFINITIONAL_REPRESENTATION('',(#5633),#5637);
#5633 = LINE('',#5634,#5635);
#5634 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5635 = VECTOR('',#5636,1.);
#5636 = DIRECTION('',(-1.,0.));
#5637 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5638 = ORIENTED_EDGE('',*,*,#5280,.F.);
#5639 = ORIENTED_EDGE('',*,*,#5640,.F.);
#5640 = EDGE_CURVE('',#5355,#5281,#5641,.T.);
#5641 = SURFACE_CURVE('',#5642,(#5646,#5653),.PCURVE_S1.);
#5642 = LINE('',#5643,#5644);
#5643 = CARTESIAN_POINT('',(61.63348,-7.509772,15.));
#5644 = VECTOR('',#5645,1.);
#5645 = DIRECTION('',(-1.,0.,0.));
#5646 = PCURVE('',#3494,#5647);
#5647 = DEFINITIONAL_REPRESENTATION('',(#5648),#5652);
#5648 = LINE('',#5649,#5650);
#5649 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5650 = VECTOR('',#5651,1.);
#5651 = DIRECTION('',(-1.,0.));
#5652 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5653 = PCURVE('',#3699,#5654);
#5654 = DEFINITIONAL_REPRESENTATION('',(#5655),#5659);
#5655 = LINE('',#5656,#5657);
#5656 = CARTESIAN_POINT('',(61.63348,-7.509772));
#5657 = VECTOR('',#5658,1.);
#5658 = DIRECTION('',(-1.,0.));
#5659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5660 = ORIENTED_EDGE('',*,*,#5378,.F.);
#5661 = FACE_BOUND('',#5662,.T.);
#5662 = EDGE_LOOP('',(#5663));
#5663 = ORIENTED_EDGE('',*,*,#4996,.F.);
#5664 = FACE_BOUND('',#5665,.T.);
#5665 = EDGE_LOOP('',(#5666));
#5666 = ORIENTED_EDGE('',*,*,#5667,.F.);
#5667 = EDGE_CURVE('',#5668,#5668,#5670,.T.);
#5668 = VERTEX_POINT('',#5669);
#5669 = CARTESIAN_POINT('',(-46.175,0.,15.));
#5670 = SURFACE_CURVE('',#5671,(#5676,#5683),.PCURVE_S1.);
#5671 = CIRCLE('',#5672,1.825);
#5672 = AXIS2_PLACEMENT_3D('',#5673,#5674,#5675);
#5673 = CARTESIAN_POINT('',(-48.,0.,15.));
#5674 = DIRECTION('',(0.,0.,1.));
#5675 = DIRECTION('',(1.,0.,0.));
#5676 = PCURVE('',#3494,#5677);
#5677 = DEFINITIONAL_REPRESENTATION('',(#5678),#5682);
#5678 = CIRCLE('',#5679,1.825);
#5679 = AXIS2_PLACEMENT_2D('',#5680,#5681);
#5680 = CARTESIAN_POINT('',(-48.,-4.2E-15));
#5681 = DIRECTION('',(1.,0.));
#5682 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5683 = PCURVE('',#5684,#5689);
#5684 = PLANE('',#5685);
#5685 = AXIS2_PLACEMENT_3D('',#5686,#5687,#5688);
#5686 = CARTESIAN_POINT('',(0.7267475,-0.344273,15.));
#5687 = DIRECTION('',(-0.,-0.,-1.));
#5688 = DIRECTION('',(-1.,0.,0.));
#5689 = DEFINITIONAL_REPRESENTATION('',(#5690),#5698);
#5690 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5691,#5692,#5693,#5694,
#5695,#5696,#5697),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#5691 = CARTESIAN_POINT('',(46.9017475,0.344273));
#5692 = CARTESIAN_POINT('',(46.9017475,3.505265723813));
#5693 = CARTESIAN_POINT('',(49.6392475,1.924769361907));
#5694 = CARTESIAN_POINT('',(52.3767475,0.344273));
#5695 = CARTESIAN_POINT('',(49.6392475,-1.236223361907));
#5696 = CARTESIAN_POINT('',(46.9017475,-2.816719723813));
#5697 = CARTESIAN_POINT('',(46.9017475,0.344273));
#5698 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5699 = FACE_BOUND('',#5700,.T.);
#5700 = EDGE_LOOP('',(#5701));
#5701 = ORIENTED_EDGE('',*,*,#5702,.F.);
#5702 = EDGE_CURVE('',#5703,#5703,#5705,.T.);
#5703 = VERTEX_POINT('',#5704);
#5704 = CARTESIAN_POINT('',(49.825,0.,15.));
#5705 = SURFACE_CURVE('',#5706,(#5711,#5718),.PCURVE_S1.);
#5706 = CIRCLE('',#5707,1.825);
#5707 = AXIS2_PLACEMENT_3D('',#5708,#5709,#5710);
#5708 = CARTESIAN_POINT('',(48.,0.,15.));
#5709 = DIRECTION('',(0.,0.,1.));
#5710 = DIRECTION('',(1.,0.,0.));
#5711 = PCURVE('',#3494,#5712);
#5712 = DEFINITIONAL_REPRESENTATION('',(#5713),#5717);
#5713 = CIRCLE('',#5714,1.825);
#5714 = AXIS2_PLACEMENT_2D('',#5715,#5716);
#5715 = CARTESIAN_POINT('',(48.,-4.2E-15));
#5716 = DIRECTION('',(1.,0.));
#5717 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5718 = PCURVE('',#5719,#5724);
#5719 = PLANE('',#5720);
#5720 = AXIS2_PLACEMENT_3D('',#5721,#5722,#5723);
#5721 = CARTESIAN_POINT('',(0.7267475,-0.344273,15.));
#5722 = DIRECTION('',(-0.,-0.,-1.));
#5723 = DIRECTION('',(-1.,0.,0.));
#5724 = DEFINITIONAL_REPRESENTATION('',(#5725),#5733);
#5725 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#5726,#5727,#5728,#5729,
#5730,#5731,#5732),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#5726 = CARTESIAN_POINT('',(-49.0982525,0.344273));
#5727 = CARTESIAN_POINT('',(-49.0982525,3.505265723813));
#5728 = CARTESIAN_POINT('',(-46.3607525,1.924769361907));
#5729 = CARTESIAN_POINT('',(-43.6232525,0.344273));
#5730 = CARTESIAN_POINT('',(-46.3607525,-1.236223361907));
#5731 = CARTESIAN_POINT('',(-49.0982525,-2.816719723813));
#5732 = CARTESIAN_POINT('',(-49.0982525,0.344273));
#5733 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5734 = ADVANCED_FACE('',(#5735,#5749,#5752,#5783),#3699,.T.);
#5735 = FACE_BOUND('',#5736,.T.);
#5736 = EDGE_LOOP('',(#5737,#5738,#5739,#5740,#5741,#5742,#5743,#5744,
    #5745,#5746,#5747,#5748));
#5737 = ORIENTED_EDGE('',*,*,#4247,.T.);
#5738 = ORIENTED_EDGE('',*,*,#3681,.T.);
#5739 = ORIENTED_EDGE('',*,*,#5594,.T.);
#5740 = ORIENTED_EDGE('',*,*,#5402,.F.);
#5741 = ORIENTED_EDGE('',*,*,#5354,.F.);
#5742 = ORIENTED_EDGE('',*,*,#5640,.T.);
#5743 = ORIENTED_EDGE('',*,*,#5304,.F.);
#5744 = ORIENTED_EDGE('',*,*,#5256,.F.);
#5745 = ORIENTED_EDGE('',*,*,#5618,.T.);
#5746 = ORIENTED_EDGE('',*,*,#4402,.T.);
#5747 = ORIENTED_EDGE('',*,*,#4348,.T.);
#5748 = ORIENTED_EDGE('',*,*,#4051,.T.);
#5749 = FACE_BOUND('',#5750,.T.);
#5750 = EDGE_LOOP('',(#5751));
#5751 = ORIENTED_EDGE('',*,*,#5128,.F.);
#5752 = FACE_BOUND('',#5753,.T.);
#5753 = EDGE_LOOP('',(#5754));
#5754 = ORIENTED_EDGE('',*,*,#5755,.F.);
#5755 = EDGE_CURVE('',#5756,#5756,#5758,.T.);
#5756 = VERTEX_POINT('',#5757);
#5757 = CARTESIAN_POINT('',(11.,-30.,15.));
#5758 = SURFACE_CURVE('',#5759,(#5764,#5771),.PCURVE_S1.);
#5759 = CIRCLE('',#5760,11.);
#5760 = AXIS2_PLACEMENT_3D('',#5761,#5762,#5763);
#5761 = CARTESIAN_POINT('',(0.,-30.,15.));
#5762 = DIRECTION('',(0.,0.,1.));
#5763 = DIRECTION('',(1.,0.,0.));
#5764 = PCURVE('',#3699,#5765);
#5765 = DEFINITIONAL_REPRESENTATION('',(#5766),#5770);
#5766 = CIRCLE('',#5767,11.);
#5767 = AXIS2_PLACEMENT_2D('',#5768,#5769);
#5768 = CARTESIAN_POINT('',(3.21E-15,-30.));
#5769 = DIRECTION('',(1.,0.));
#5770 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5771 = PCURVE('',#5772,#5777);
#5772 = CYLINDRICAL_SURFACE('',#5773,11.);
#5773 = AXIS2_PLACEMENT_3D('',#5774,#5775,#5776);
#5774 = CARTESIAN_POINT('',(0.,-30.,15.));
#5775 = DIRECTION('',(0.,0.,1.));
#5776 = DIRECTION('',(1.,0.,0.));
#5777 = DEFINITIONAL_REPRESENTATION('',(#5778),#5782);
#5778 = LINE('',#5779,#5780);
#5779 = CARTESIAN_POINT('',(0.,0.));
#5780 = VECTOR('',#5781,1.);
#5781 = DIRECTION('',(1.,0.));
#5782 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5783 = FACE_BOUND('',#5784,.T.);
#5784 = EDGE_LOOP('',(#5785));
#5785 = ORIENTED_EDGE('',*,*,#5078,.F.);
#5786 = ADVANCED_FACE('',(#5787,#5790),#3798,.T.);
#5787 = FACE_BOUND('',#5788,.T.);
#5788 = EDGE_LOOP('',(#5789));
#5789 = ORIENTED_EDGE('',*,*,#3783,.T.);
#5790 = FACE_BOUND('',#5791,.T.);
#5791 = EDGE_LOOP('',(#5792));
#5792 = ORIENTED_EDGE('',*,*,#5793,.F.);
#5793 = EDGE_CURVE('',#5794,#5794,#5796,.T.);
#5794 = VERTEX_POINT('',#5795);
#5795 = CARTESIAN_POINT('',(6.55,58.,41.));
#5796 = SURFACE_CURVE('',#5797,(#5802,#5809),.PCURVE_S1.);
#5797 = CIRCLE('',#5798,6.55);
#5798 = AXIS2_PLACEMENT_3D('',#5799,#5800,#5801);
#5799 = CARTESIAN_POINT('',(0.,58.,41.));
#5800 = DIRECTION('',(0.,-1.,-2.2E-16));
#5801 = DIRECTION('',(1.,0.,0.));
#5802 = PCURVE('',#3798,#5803);
#5803 = DEFINITIONAL_REPRESENTATION('',(#5804),#5808);
#5804 = CIRCLE('',#5805,6.55);
#5805 = AXIS2_PLACEMENT_2D('',#5806,#5807);
#5806 = CARTESIAN_POINT('',(7.105427357601E-15,1.31E-15));
#5807 = DIRECTION('',(0.,1.));
#5808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5809 = PCURVE('',#4022,#5810);
#5810 = DEFINITIONAL_REPRESENTATION('',(#5811),#5815);
#5811 = LINE('',#5812,#5813);
#5812 = CARTESIAN_POINT('',(0.,-58.));
#5813 = VECTOR('',#5814,1.);
#5814 = DIRECTION('',(1.,0.));
#5815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5816 = ADVANCED_FACE('',(#5817),#4022,.F.);
#5817 = FACE_BOUND('',#5818,.F.);
#5818 = EDGE_LOOP('',(#5819,#5840,#5841,#5842));
#5819 = ORIENTED_EDGE('',*,*,#5820,.F.);
#5820 = EDGE_CURVE('',#5794,#4002,#5821,.T.);
#5821 = SEAM_CURVE('',#5822,(#5826,#5833),.PCURVE_S1.);
#5822 = LINE('',#5823,#5824);
#5823 = CARTESIAN_POINT('',(6.55,-9.1E-15,41.));
#5824 = VECTOR('',#5825,1.);
#5825 = DIRECTION('',(0.,1.,2.2E-16));
#5826 = PCURVE('',#4022,#5827);
#5827 = DEFINITIONAL_REPRESENTATION('',(#5828),#5832);
#5828 = LINE('',#5829,#5830);
#5829 = CARTESIAN_POINT('',(6.28318530718,0.));
#5830 = VECTOR('',#5831,1.);
#5831 = DIRECTION('',(0.,-1.));
#5832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5833 = PCURVE('',#4022,#5834);
#5834 = DEFINITIONAL_REPRESENTATION('',(#5835),#5839);
#5835 = LINE('',#5836,#5837);
#5836 = CARTESIAN_POINT('',(0.,0.));
#5837 = VECTOR('',#5838,1.);
#5838 = DIRECTION('',(0.,-1.));
#5839 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5840 = ORIENTED_EDGE('',*,*,#5793,.F.);
#5841 = ORIENTED_EDGE('',*,*,#5820,.T.);
#5842 = ORIENTED_EDGE('',*,*,#4001,.T.);
#5843 = ADVANCED_FACE('',(#5844,#5850),#4137,.F.);
#5844 = FACE_BOUND('',#5845,.F.);
#5845 = EDGE_LOOP('',(#5846,#5847,#5848,#5849));
#5846 = ORIENTED_EDGE('',*,*,#4273,.T.);
#5847 = ORIENTED_EDGE('',*,*,#4323,.T.);
#5848 = ORIENTED_EDGE('',*,*,#4121,.T.);
#5849 = ORIENTED_EDGE('',*,*,#4197,.T.);
#5850 = FACE_BOUND('',#5851,.F.);
#5851 = EDGE_LOOP('',(#5852));
#5852 = ORIENTED_EDGE('',*,*,#5853,.F.);
#5853 = EDGE_CURVE('',#5854,#5854,#5856,.T.);
#5854 = VERTEX_POINT('',#5855);
#5855 = CARTESIAN_POINT('',(11.,-30.,10.));
#5856 = SURFACE_CURVE('',#5857,(#5862,#5869),.PCURVE_S1.);
#5857 = CIRCLE('',#5858,11.);
#5858 = AXIS2_PLACEMENT_3D('',#5859,#5860,#5861);
#5859 = CARTESIAN_POINT('',(0.,-30.,10.));
#5860 = DIRECTION('',(0.,0.,1.));
#5861 = DIRECTION('',(1.,0.,0.));
#5862 = PCURVE('',#4137,#5863);
#5863 = DEFINITIONAL_REPRESENTATION('',(#5864),#5868);
#5864 = CIRCLE('',#5865,11.);
#5865 = AXIS2_PLACEMENT_2D('',#5866,#5867);
#5866 = CARTESIAN_POINT('',(0.,5.));
#5867 = DIRECTION('',(1.,0.));
#5868 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5869 = PCURVE('',#5772,#5870);
#5870 = DEFINITIONAL_REPRESENTATION('',(#5871),#5875);
#5871 = LINE('',#5872,#5873);
#5872 = CARTESIAN_POINT('',(0.,-5.));
#5873 = VECTOR('',#5874,1.);
#5874 = DIRECTION('',(1.,0.));
#5875 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5876 = ADVANCED_FACE('',(#5877),#4491,.F.);
#5877 = FACE_BOUND('',#5878,.F.);
#5878 = EDGE_LOOP('',(#5879,#5880,#5881,#5882,#5883,#5884,#5885,#5886,
    #5887));
#5879 = ORIENTED_EDGE('',*,*,#4624,.T.);
#5880 = ORIENTED_EDGE('',*,*,#4721,.T.);
#5881 = ORIENTED_EDGE('',*,*,#4820,.T.);
#5882 = ORIENTED_EDGE('',*,*,#4892,.F.);
#5883 = ORIENTED_EDGE('',*,*,#4793,.T.);
#5884 = ORIENTED_EDGE('',*,*,#4694,.T.);
#5885 = ORIENTED_EDGE('',*,*,#4597,.T.);
#5886 = ORIENTED_EDGE('',*,*,#4474,.F.);
#5887 = ORIENTED_EDGE('',*,*,#4548,.F.);
#5888 = ADVANCED_FACE('',(#5889),#5061,.T.);
#5889 = FACE_BOUND('',#5890,.T.);
#5890 = EDGE_LOOP('',(#5891));
#5891 = ORIENTED_EDGE('',*,*,#5046,.T.);
#5892 = ADVANCED_FACE('',(#5893),#5216,.T.);
#5893 = FACE_BOUND('',#5894,.T.);
#5894 = EDGE_LOOP('',(#5895));
#5895 = ORIENTED_EDGE('',*,*,#5201,.T.);
#5896 = ADVANCED_FACE('',(#5897),#5467,.T.);
#5897 = FACE_BOUND('',#5898,.T.);
#5898 = EDGE_LOOP('',(#5899));
#5899 = ORIENTED_EDGE('',*,*,#5452,.T.);
#5900 = ADVANCED_FACE('',(#5901),#5522,.T.);
#5901 = FACE_BOUND('',#5902,.T.);
#5902 = EDGE_LOOP('',(#5903));
#5903 = ORIENTED_EDGE('',*,*,#5507,.T.);
#5904 = ADVANCED_FACE('',(#5905),#5577,.T.);
#5905 = FACE_BOUND('',#5906,.T.);
#5906 = EDGE_LOOP('',(#5907));
#5907 = ORIENTED_EDGE('',*,*,#5562,.T.);
#5908 = ADVANCED_FACE('',(#5909),#5684,.F.);
#5909 = FACE_BOUND('',#5910,.F.);
#5910 = EDGE_LOOP('',(#5911));
#5911 = ORIENTED_EDGE('',*,*,#5667,.F.);
#5912 = ADVANCED_FACE('',(#5913),#5719,.F.);
#5913 = FACE_BOUND('',#5914,.F.);
#5914 = EDGE_LOOP('',(#5915));
#5915 = ORIENTED_EDGE('',*,*,#5702,.F.);
#5916 = ADVANCED_FACE('',(#5917),#5772,.F.);
#5917 = FACE_BOUND('',#5918,.F.);
#5918 = EDGE_LOOP('',(#5919,#5920,#5941,#5942));
#5919 = ORIENTED_EDGE('',*,*,#5755,.F.);
#5920 = ORIENTED_EDGE('',*,*,#5921,.T.);
#5921 = EDGE_CURVE('',#5756,#5854,#5922,.T.);
#5922 = SEAM_CURVE('',#5923,(#5927,#5934),.PCURVE_S1.);
#5923 = LINE('',#5924,#5925);
#5924 = CARTESIAN_POINT('',(11.,-30.,15.));
#5925 = VECTOR('',#5926,1.);
#5926 = DIRECTION('',(-0.,-0.,-1.));
#5927 = PCURVE('',#5772,#5928);
#5928 = DEFINITIONAL_REPRESENTATION('',(#5929),#5933);
#5929 = LINE('',#5930,#5931);
#5930 = CARTESIAN_POINT('',(6.28318530718,0.));
#5931 = VECTOR('',#5932,1.);
#5932 = DIRECTION('',(0.,-1.));
#5933 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5934 = PCURVE('',#5772,#5935);
#5935 = DEFINITIONAL_REPRESENTATION('',(#5936),#5940);
#5936 = LINE('',#5937,#5938);
#5937 = CARTESIAN_POINT('',(0.,0.));
#5938 = VECTOR('',#5939,1.);
#5939 = DIRECTION('',(0.,-1.));
#5940 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5941 = ORIENTED_EDGE('',*,*,#5853,.T.);
#5942 = ORIENTED_EDGE('',*,*,#5921,.F.);
#5943 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5947)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5944,#5945,#5946)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5944 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5945 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5946 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5947 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5944,
  'distance_accuracy_value','confusion accuracy');
#5948 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#5949 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #5950),#5943);
#5950 = STYLED_ITEM('color',(#5951),#15);
#5951 = PRESENTATION_STYLE_ASSIGNMENT((#5952,#5958));
#5952 = SURFACE_STYLE_USAGE(.BOTH.,#5953);
#5953 = SURFACE_SIDE_STYLE('',(#5954));
#5954 = SURFACE_STYLE_FILL_AREA(#5955);
#5955 = FILL_AREA_STYLE('',(#5956));
#5956 = FILL_AREA_STYLE_COLOUR('',#5957);
#5957 = COLOUR_RGB('',0.800000011921,0.800000011921,0.800000011921);
#5958 = CURVE_STYLE('',#5959,POSITIVE_LENGTH_MEASURE(0.1),#5957);
#5959 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
