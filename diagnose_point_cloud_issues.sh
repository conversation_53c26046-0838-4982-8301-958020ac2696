#!/bin/bash

# 点云匹配问题诊断脚本

echo "=========================================="
echo "🔍 点云匹配问题诊断"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行"
    exit 1
fi

echo ""
echo "步骤1: 检查当前GPS约束参数"

echo "🔧 当前GPS约束参数:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 检查关键参数
PARAMS=(
    "/state_estimation_node/gps/plane_constraint_weight"
    "/state_estimation_node/gps/xy_correction_rate"
    "/state_estimation_node/gps/correction_rate"
    "/state_estimation_node/gps/xy_correction_threshold"
    "/enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold"
    "/enhanced_slam_loop_closure_integration/force_search_radius"
    "/enhanced_slam_loop_closure_integration/start_end_score_threshold"
    "/enhanced_slam_loop_closure_integration/voxel_leaf_size"
)

PROBLEMATIC_COUNT=0

for param in "${PARAMS[@]}"; do
    VALUE=$(rosparam get "$param" 2>/dev/null || echo "N/A")
    echo "  $param: $VALUE"
    
    # 检查是否为问题参数
    case "$param" in
        *"plane_constraint_weight")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 0.5" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过高！建议 < 0.5"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
        *"xy_correction_rate")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 0.3" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过高！建议 < 0.3"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
        *"correction_rate")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 0.5" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过高！建议 < 0.5"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
        *"force_search_radius")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 100" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过大！建议 < 100"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
        *"start_end_score_threshold")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 1.0" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过宽松！建议 < 1.0"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
        *"voxel_leaf_size")
            if [ "$VALUE" != "N/A" ] && [ $(echo "$VALUE > 0.5" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
                echo "    ⚠️  过大！建议 < 0.5"
                PROBLEMATIC_COUNT=$((PROBLEMATIC_COUNT + 1))
            fi
            ;;
    esac
done

echo ""
echo "步骤2: 检查系统状态"

# 检查节点状态
echo "🔧 节点运行状态:"
NODES=(
    "/state_estimation_node"
    "/enhanced_gps_loop_closure_optimizer"
    "/enhanced_slam_loop_closure_integration"
)

for node in "${NODES[@]}"; do
    if rosnode info "$node" >/dev/null 2>&1; then
        echo "  ✅ $node"
    else
        echo "  ❌ $node - 未运行"
    fi
done

echo ""
echo "步骤3: 检查话题数据流"

echo "🔧 话题数据状态:"
TOPICS=(
    "/rtk/gnss"
    "/velodyne_points"
    "/cloud_registered"
    "/force_loop_closure"
)

for topic in "${TOPICS[@]}"; do
    if timeout 2 rostopic hz "$topic" >/dev/null 2>&1; then
        HZ=$(timeout 2 rostopic hz "$topic" 2>/dev/null | grep "average rate" | awk '{print $3}')
        echo "  ✅ $topic: ${HZ:-Unknown} Hz"
    else
        echo "  ❌ $topic - 无数据"
    fi
done

echo ""
echo "步骤4: 问题诊断结果"

echo "🎯 诊断结果:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

if [ $PROBLEMATIC_COUNT -gt 0 ]; then
    echo "❌ 发现 $PROBLEMATIC_COUNT 个问题参数"
    echo ""
    echo "🔧 推荐修复方案:"
    echo "1. 立即运行: ./fix_point_cloud_matching.sh"
    echo "2. 或重启平衡模式: roslaunch state_estimation mapping_robosense_balanced_gps.launch"
    echo "3. 或恢复保守模式: ./restore_conservative_gps.sh"
else
    echo "✅ 参数配置正常"
    echo ""
    echo "🔧 其他可能原因:"
    echo "1. 数据质量问题 - 检查GPS信号质量"
    echo "2. 环境因素 - 检查是否有大量动态物体"
    echo "3. 硬件问题 - 检查LiDAR和IMU数据"
fi

echo ""
echo "步骤5: 实时监控建议"

echo "📊 监控命令:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "# 监控GPS约束效果"
echo "rostopic echo /loop_closure_distance"
echo ""
echo "# 监控匹配分数"
echo "rostopic echo /matching_score"
echo ""
echo "# 监控点云质量"
echo "rostopic hz /cloud_registered"
echo ""
echo "# 启动完整监控"
echo "./monitor_gps_performance.sh"

echo ""
echo "🚨 紧急修复命令:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "# 立即修复点云匹配问题"
echo "./fix_point_cloud_matching.sh"
echo ""
echo "# 恢复到保守模式"
echo "./restore_conservative_gps.sh"
echo ""
echo "# 重启平衡模式"
echo "roslaunch state_estimation mapping_robosense_balanced_gps.launch"
