#!/bin/bash

# GPS保守模式恢复脚本
# 当激进模式导致过度校正时使用

echo "=========================================="
echo "🔄 GPS保守模式恢复"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动roscore"
    exit 1
fi

echo ""
echo "恢复GPS约束到保守模式..."

# 恢复GPS回环检测参数
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 10.0
rosparam set /enhanced_gps_loop_closure_optimizer/min_trajectory_length 50.0
rosparam set /enhanced_gps_loop_closure_optimizer/check_interval 1.0
rosparam set /enhanced_gps_loop_closure_optimizer/loop_detection_cooldown 5.0

# 恢复SLAM匹配参数
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 15.0
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.5
rosparam set /enhanced_slam_loop_closure_integration/max_search_candidates 10
rosparam set /enhanced_slam_loop_closure_integration/voxel_leaf_size 0.2

# 恢复GPS约束强度
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.2
rosparam set /state_estimation_node/gps/xy_correction_threshold 0.5
rosparam set /state_estimation_node/gps/xy_correction_rate 0.1
rosparam set /state_estimation_node/gps/correction_rate 0.2
rosparam set /state_estimation_node/gps/height_correction_threshold 0.2

# 恢复ICP参数
rosparam set /state_estimation_node/gps/icp_trigger_distance 25.0
rosparam set /state_estimation_node/gps/icp_fitness_threshold 0.5
rosparam set /state_estimation_node/gps/loop_closure_min_distance 30.0

echo "✅ 已恢复到保守模式"
echo ""
echo "当前参数:"
echo "  GPS回环距离阈值: 10.0m"
echo "  SLAM搜索半径: 15.0m"
echo "  平面约束权重: 0.2"
echo "  XY校正率: 10%"
echo "  高度校正率: 20%"
