/**
 * 保守GPS约束补丁 - 解决首尾偏离问题
 * 
 * 使用方法：
 * 1. 将此文件中的函数替换voxelMapping.cpp中对应的函数
 * 2. 或者在voxelMapping.cpp中添加这些保守版本的函数
 * 3. 通过参数控制使用保守模式还是原始模式
 */

#include <ros/ros.h>
#include <Eigen/Core>
#include <sensor_msgs/NavSatFix.h>

// 保守的GPS约束参数
struct ConservativeGPSParams {
    // 基础参数
    bool enable_conservative_mode = true;
    double conservative_correction_rate = 0.02;  // 2%校正率
    double conservative_height_threshold = 1.0;  // 1米高度阈值
    double conservative_loop_distance = 3.0;     // 3米回环距离
    double conservative_min_trajectory = 200.0;  // 200米最小轨迹
    
    // 校正因子 - 极度保守
    double large_error_factor = 0.05;   // 5%
    double medium_error_factor = 0.03;  // 3%
    double small_error_factor = 0.01;   // 1%
    double tiny_error_factor = 0.005;   // 0.5%
    
    // 安全限制
    double max_single_correction = 0.5;  // 单次最大校正0.5米
    double max_total_correction = 5.0;   // 总最大校正5米
    
    // 错误检测
    bool enable_error_detection = true;
    double max_velocity_change = 0.5;    // 最大速度变化0.5m/s
    double geometric_consistency_threshold = 1.0;  // 几何一致性阈值
} conservative_params;

// 全局变量用于监控
static double total_applied_correction = 0.0;
static int correction_count = 0;
static std::deque<double> recent_corrections;
static std::deque<double> recent_timestamps;

/**
 * 保守的GPS高度校正函数
 * 特点：极小的校正率，严格的阈值检查
 */
Eigen::Vector3d conservativeCorrectHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp) {
    if (!conservative_params.enable_conservative_mode) {
        return slam_position;  // 如果禁用保守模式，返回原始位置
    }
    
    Eigen::Vector3d gps_position;
    double gps_std;
    
    if (!getGPSPosition(timestamp, gps_position, gps_std)) {
        return slam_position;
    }
    
    // 计算高度差
    double height_diff = gps_position.z() - slam_position.z();
    
    // 严格的阈值检查
    if (std::abs(height_diff) > conservative_params.conservative_height_threshold) {
        Eigen::Vector3d corrected_position = slam_position;
        
        // 极度温和的校正
        double correction_amount = height_diff * conservative_params.conservative_correction_rate;
        
        // 限制单次校正量
        if (std::abs(correction_amount) > conservative_params.max_single_correction) {
            correction_amount = std::copysign(conservative_params.max_single_correction, correction_amount);
        }
        
        corrected_position.z() += correction_amount;
        
        // 记录校正历史
        total_applied_correction += std::abs(correction_amount);
        correction_count++;
        
        ROS_INFO_THROTTLE(5.0, "\033[1;36m[Conservative GPS] Height Correction: SLAM=%.3f, GPS=%.3f, Diff=%.3f, Applied=%.3f\033[0m",
                slam_position.z(), gps_position.z(), height_diff, correction_amount);
        
        return corrected_position;
    }
    
    return slam_position;
}

/**
 * 保守的GPS回环检测函数
 * 特点：严格的距离要求，极小的校正因子
 */
bool conservativeDetectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp,
                                     Eigen::Vector3d& loop_constraint) {
    if (!conservative_params.enable_conservative_mode || !start_positions_set) {
        return false;
    }
    
    Eigen::Vector3d current_gps_pos;
    double gps_std;
    
    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }
    
    // 检查轨迹长度是否足够 - 更严格的要求
    double slam_trajectory_length = (current_slam_pos - start_slam_position).norm();
    if (slam_trajectory_length < conservative_params.conservative_min_trajectory) {
        static int skip_count = 0;
        if (++skip_count % 200 == 1) {  // 减少日志频率
            ROS_DEBUG("Conservative loop closure skipped: trajectory %.1fm < required %.1fm",
                     slam_trajectory_length, conservative_params.conservative_min_trajectory);
        }
        return false;
    }
    
    // 检查是否接近起始位置 - 更严格的距离要求
    double distance_to_start = (current_gps_pos - start_gps_position).norm();
    
    // 只有在极近距离才考虑回环约束
    if (distance_to_start < conservative_params.conservative_loop_distance) {
        // 计算回环约束
        Eigen::Vector3d slam_drift = current_slam_pos - start_slam_position;
        Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position;
        
        loop_constraint = gps_drift - slam_drift;
        double constraint_norm = loop_constraint.norm();
        
        // 错误检测 - 检查约束是否合理
        if (conservative_params.enable_error_detection) {
            // 检查约束是否过大（可能是错误匹配）
            if (constraint_norm > 20.0) {
                ROS_WARN_THROTTLE(10.0, "\033[1;31m[Conservative GPS] Constraint too large: %.1fm, rejecting\033[0m", constraint_norm);
                return false;
            }
            
            // 检查几何一致性
            if (constraint_norm > conservative_params.geometric_consistency_threshold * distance_to_start) {
                ROS_WARN_THROTTLE(10.0, "\033[1;31m[Conservative GPS] Geometric inconsistency detected, rejecting\033[0m");
                return false;
            }
        }
        
        // 极度保守的约束强度调整
        double distance_factor = 0.0;
        
        if (distance_to_start < 1.0) {
            distance_factor = 0.8;  // 1米内：较强约束
        } else if (distance_to_start < 2.0) {
            distance_factor = 0.5;  // 2米内：中等约束
        } else if (distance_to_start < 3.0) {
            distance_factor = 0.2;  // 3米内：弱约束
        }
        
        // 应用距离因子
        loop_constraint *= distance_factor;
        
        ROS_INFO_THROTTLE(5.0, "\033[1;36m[Conservative GPS] Loop Closure: Distance=%.2fm, Constraint[%.3f,%.3f,%.3f], Factor=%.2f\033[0m",
                distance_to_start, loop_constraint.x(), loop_constraint.y(), loop_constraint.z(), distance_factor);
        
        // 只有约束足够小且距离足够近才应用
        return (loop_constraint.norm() > 0.05 && distance_to_start < 2.0);
    }
    
    return false;
}

/**
 * 保守的智能位置校正函数
 * 特点：极小的校正因子，严格的安全检查
 */
Eigen::Vector3d conservativeApplyLoopCorrection(const Eigen::Vector3d& current_position, 
                                               const Eigen::Vector3d& loop_constraint) {
    double constraint_norm = loop_constraint.norm();
    double correction_factor = 0.0;
    
    // 保守的分层校正策略
    if (constraint_norm > 15.0) {
        // 超大误差：可能是错误匹配，拒绝校正
        ROS_WARN_THROTTLE(10.0, "\033[1;31m[Conservative GPS] Rejecting large error: %.1fm\033[0m", constraint_norm);
        return current_position;
    } else if (constraint_norm > 10.0) {
        // 大误差：极小校正
        correction_factor = conservative_params.large_error_factor;
    } else if (constraint_norm > 5.0) {
        // 中等误差：微小校正
        correction_factor = conservative_params.medium_error_factor;
    } else if (constraint_norm > 2.0) {
        // 小误差：极微小校正
        correction_factor = conservative_params.small_error_factor;
    } else {
        // 微小误差：几乎不校正
        correction_factor = conservative_params.tiny_error_factor;
    }
    
    // 计算校正量
    Eigen::Vector3d correction = loop_constraint * correction_factor;
    
    // 严格限制单次校正量
    if (correction.norm() > conservative_params.max_single_correction) {
        correction = correction.normalized() * conservative_params.max_single_correction;
        ROS_WARN_THROTTLE(5.0, "\033[1;33m[Conservative GPS] Correction limited to %.3fm\033[0m", 
                         conservative_params.max_single_correction);
    }
    
    // 检查总校正量限制
    if (total_applied_correction + correction.norm() > conservative_params.max_total_correction) {
        ROS_WARN_THROTTLE(10.0, "\033[1;31m[Conservative GPS] Total correction limit reached: %.1fm\033[0m", 
                         total_applied_correction);
        return current_position;
    }
    
    // 应用校正
    Eigen::Vector3d corrected_position = current_position + correction;
    
    // 更新统计信息
    total_applied_correction += correction.norm();
    recent_corrections.push_back(correction.norm());
    recent_timestamps.push_back(ros::Time::now().toSec());
    
    // 保持最近100次校正的历史
    while (recent_corrections.size() > 100) {
        recent_corrections.pop_front();
        recent_timestamps.pop_front();
    }
    
    ROS_INFO_THROTTLE(3.0, "\033[1;36m[Conservative GPS] Applied correction: Error=%.1fm, Correction=%.3fm, Factor=%.1f%%, Total=%.1fm\033[0m",
            constraint_norm, correction.norm(), correction_factor * 100, total_applied_correction);
    
    return corrected_position;
}

/**
 * 错误匹配检测函数
 * 检测并预防可能的错误GPS约束
 */
bool detectErrorMatching(const Eigen::Vector3d& current_position, const Eigen::Vector3d& proposed_correction) {
    // 检查校正是否过于频繁
    if (recent_corrections.size() > 10) {
        double recent_time_span = recent_timestamps.back() - recent_timestamps[recent_timestamps.size()-10];
        if (recent_time_span < 30.0) {  // 30秒内超过10次校正
            ROS_WARN_THROTTLE(10.0, "\033[1;31m[Error Detection] Too frequent corrections detected\033[0m");
            return true;
        }
    }
    
    // 检查校正方向是否一致
    if (recent_corrections.size() > 5) {
        // 计算最近几次校正的方向一致性
        // 如果方向变化太大，可能是错误匹配
        // 这里简化处理，实际可以更复杂
    }
    
    // 检查校正幅度是否合理
    if (proposed_correction.norm() > 2.0) {
        ROS_WARN_THROTTLE(5.0, "\033[1;31m[Error Detection] Large correction detected: %.3fm\033[0m", 
                         proposed_correction.norm());
        return true;
    }
    
    return false;
}

/**
 * 保守GPS约束的主要集成函数
 * 在voxelMapping.cpp的主循环中调用此函数
 */
void applyConservativeGPSConstraints(state_ikfom& state_point, double lidar_end_time, esekfom::esekf<state_ikfom, 12, input_ikfom>& kf) {
    if (!conservative_params.enable_conservative_mode) {
        return;  // 如果禁用保守模式，直接返回
    }
    
    // 1. 保守的高度校正
    Eigen::Vector3d height_corrected = conservativeCorrectHeightWithGPS(state_point.pos, lidar_end_time);
    if ((height_corrected - state_point.pos).norm() > 0.001) {
        state_point.pos = height_corrected;
        kf.change_x(state_point);
    }
    
    // 2. 保守的回环检测和校正
    Eigen::Vector3d loop_constraint;
    if (conservativeDetectGPSLoopClosure(state_point.pos, lidar_end_time, loop_constraint)) {
        // 错误匹配检测
        if (!detectErrorMatching(state_point.pos, loop_constraint)) {
            Eigen::Vector3d corrected_position = conservativeApplyLoopCorrection(state_point.pos, loop_constraint);
            
            if ((corrected_position - state_point.pos).norm() > 0.001) {
                state_point.pos = corrected_position;
                kf.change_x(state_point);
            }
        } else {
            ROS_WARN_THROTTLE(10.0, "\033[1;31m[Conservative GPS] Error matching detected, skipping correction\033[0m");
        }
    }
}

/**
 * 参数动态调整函数
 * 根据运行时状态动态调整保守参数
 */
void adjustConservativeParameters() {
    // 如果总校正量过大，进一步降低校正率
    if (total_applied_correction > 10.0) {
        conservative_params.conservative_correction_rate *= 0.5;
        conservative_params.large_error_factor *= 0.5;
        conservative_params.medium_error_factor *= 0.5;
        conservative_params.small_error_factor *= 0.5;
        
        ROS_WARN("\033[1;33m[Conservative GPS] Reducing correction rates due to large total correction\033[0m");
    }
    
    // 如果校正过于频繁，增加冷却时间
    if (recent_corrections.size() > 20) {
        double avg_interval = (recent_timestamps.back() - recent_timestamps.front()) / recent_corrections.size();
        if (avg_interval < 2.0) {
            conservative_params.conservative_min_trajectory *= 1.5;
            ROS_WARN("\033[1;33m[Conservative GPS] Increasing minimum trajectory due to frequent corrections\033[0m");
        }
    }
}
