#!/bin/bash

# 最终GPS功能编译修复脚本

echo "=========================================="
echo "🔧 最终GPS功能编译修复"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤2: 彻底清理"
rm -rf build devel
echo "✓ 清理完成"

echo ""
echo "步骤3: 安装完整依赖"
sudo apt update
sudo apt install -y \
    build-essential \
    cmake \
    ros-noetic-desktop-full \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    libpcl-dev \
    pcl-tools \
    libeigen3-dev \
    libboost-all-dev

echo ""
echo "步骤4: 检查编译器版本"
gcc_version=$(gcc --version | head -n1)
echo "GCC版本: $gcc_version"

cpp_version=$(g++ --version | head -n1)
echo "G++版本: $cpp_version"

echo ""
echo "步骤5: 尝试编译"
echo "使用Release模式单线程编译..."

# 方法1: 标准编译
catkin_make -DCMAKE_BUILD_TYPE=Release -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 标准编译成功!"
    
    source devel/setup.bash
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "验证编译结果..."
    
    # 检查所有可执行文件
    executables=(
        "devel/lib/state_estimation/state_estimation_node"
        "devel/lib/state_estimation/intensity_preserving_pcd_saver"
        "devel/lib/state_estimation/enhanced_slam_loop_closure_integration"
        "devel/lib/state_estimation/adaptive_parameter_optimizer"
        "devel/lib/state_estimation/force_start_end_loop_matcher"
    )
    
    all_good=true
    for exe in "${executables[@]}"; do
        if [ -f "$exe" ]; then
            echo "✅ $(basename $exe)"
        else
            echo "⚠️  $(basename $exe) (缺失)"
            if [[ "$exe" == *"state_estimation_node"* ]]; then
                all_good=false
            fi
        fi
    done
    
    # 检查Python脚本
    scripts=(
        "src/state_estimation/scripts/intelligent_start_end_detector.py"
        "src/state_estimation/scripts/intelligent_gps_constraint_controller.py"
        "src/state_estimation/scripts/simple_intensity_analyzer.py"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            echo "✅ $(basename $script)"
        else
            echo "⚠️  $(basename $script) (缺失)"
        fi
    done
    
    if [ "$all_good" = true ]; then
        echo ""
        echo "🎉 完整GPS功能编译成功!"
        echo ""
        echo "可用的完整功能启动选项:"
        echo ""
        echo "1. 🎯 解决GPS约束冲突 (推荐):"
        echo "   ./solve_gps_constraint_conflict.sh"
        echo ""
        echo "2. 🔥 解决首尾偏差问题:"
        echo "   ./solve_start_end_offset.sh"
        echo ""
        echo "3. 🚀 智能SLAM系统:"
        echo "   ./intelligent_slam_system.sh"
        echo ""
        echo "4. 🛡️  鲁棒启动系统:"
        echo "   ./start_slam_system_robust.sh"
        echo ""
        echo "5. ⚙️  手动启动完整功能:"
        echo "   roslaunch state_estimation optimized_slam_simple.launch enable_intelligent_detection:=true"
        echo ""
        echo "监控命令:"
        echo "   GPS约束状态: rostopic echo /gps_constraint_control"
        echo "   ICP匹配质量: rostopic echo /icp_fitness_score"
        echo "   智能检测:    rostopic echo /intelligent_detector_status"
        echo "   强制匹配:    rostopic echo /force_match_score"
        echo ""
    else
        echo ""
        echo "⚠️  部分编译成功，核心功能可用"
        echo "可以使用基础功能"
    fi
    
else
    echo ""
    echo "❌ 标准编译失败，尝试兼容性编译..."
    
    # 方法2: 兼容性编译
    echo "使用C++11标准编译..."
    catkin_make -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_STANDARD=11 -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 兼容性编译成功!"
        
        source devel/setup.bash
        chmod +x *.sh
        chmod +x src/state_estimation/scripts/*.py
        
        echo ""
        echo "可用功能 (兼容模式):"
        echo "✅ 基础SLAM"
        echo "✅ 强度保持"
        echo "✅ 智能检测"
        echo "⚠️  GPS约束 (可能有限制)"
        echo ""
        echo "启动命令:"
        echo "   ./start_slam_system_robust.sh"
        
    else
        echo ""
        echo "❌ 兼容性编译也失败，尝试最小化编译..."
        
        # 方法3: 最小化编译
        echo "临时禁用复杂功能..."
        
        # 备份文件
        cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup
        
        # 禁用GPS集成
        sed -i 's/^#define USE_GPS_INTEGRATION/\/\/ #define USE_GPS_INTEGRATION/' src/state_estimation/src/voxelMapping.cpp
        
        # 重新编译
        catkin_make -DCMAKE_BUILD_TYPE=Release -j1
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "✅ 最小化编译成功!"
            
            source devel/setup.bash
            chmod +x *.sh
            chmod +x src/state_estimation/scripts/*.py
            
            echo ""
            echo "可用功能 (最小模式):"
            echo "✅ 基础SLAM"
            echo "✅ 强度保持"
            echo "❌ GPS集成 (已禁用)"
            echo ""
            echo "启动命令:"
            echo "   ./start_basic_slam.sh"
            echo ""
            echo "如需恢复GPS功能:"
            echo "   cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp"
            echo "   然后重新运行此脚本"
            
        else
            echo ""
            echo "❌ 所有编译方法都失败!"
            echo ""
            echo "恢复原文件..."
            if [ -f "src/state_estimation/src/voxelMapping.cpp.backup" ]; then
                cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp
            fi
            
            echo ""
            echo "建议的解决方案:"
            echo "1. 检查系统环境:"
            echo "   cat /etc/os-release"
            echo "   uname -a"
            echo ""
            echo "2. 重新安装ROS:"
            echo "   sudo apt remove ros-noetic-*"
            echo "   sudo apt install ros-noetic-desktop-full"
            echo ""
            echo "3. 检查磁盘空间:"
            echo "   df -h"
            echo ""
            echo "4. 检查内存:"
            echo "   free -h"
            echo ""
            echo "5. 或者使用Docker环境"
            
            exit 1
        fi
    fi
fi

echo ""
echo "=========================================="
echo "GPS功能编译修复完成!"
echo "=========================================="
