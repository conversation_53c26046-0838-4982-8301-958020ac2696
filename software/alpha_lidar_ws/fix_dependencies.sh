#!/bin/bash
# Fix Python dependencies for Enhanced GPS Loop Closure System

set -e

echo "=========================================="
echo "Fixing Python Dependencies"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_step "Step 1: Installing system Python packages"

# Install system-wide Python packages
print_status "Installing Python3 and pip..."
sudo apt-get update
sudo apt-get install -y python3 python3-pip python3-dev

print_step "Step 2: Installing ROS Python dependencies"

# Install ROS-specific Python packages
print_status "Installing ROS Python packages..."
sudo apt-get install -y python3-rospy python3-rospkg python3-catkin-pkg

print_step "Step 3: Installing additional Python packages"

# Install additional Python packages
print_status "Installing PyYAML, numpy, scipy..."
sudo apt-get install -y python3-yaml python3-numpy python3-scipy

# Also install via pip as backup
print_status "Installing via pip3 as backup..."
pip3 install --user PyYAML numpy scipy rospkg catkin_pkg

print_step "Step 4: Verifying installations"

# Verify installations
print_status "Verifying Python package installations..."

python3 -c "import yaml; print('PyYAML: OK')" || print_error "PyYAML failed"
python3 -c "import numpy; print('numpy: OK')" || print_error "numpy failed"
python3 -c "import rospkg; print('rospkg: OK')" || print_error "rospkg failed"
python3 -c "import rospy; print('rospy: OK')" || print_error "rospy failed (this is normal if ROS is not sourced)"

print_step "Step 5: Setting up environment"

# Set environment variables
export PYTHONPATH="/opt/ros/noetic/lib/python3/dist-packages:$PYTHONPATH"
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

print_status "Environment variables set"

print_step "Step 6: Creating Python environment setup script"

# Create a setup script for consistent environment
cat > setup_python_env.sh << 'EOF'
#!/bin/bash
# Python environment setup for Enhanced GPS Loop Closure

# Set Python encoding
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

# Add ROS Python path
export PYTHONPATH="/opt/ros/noetic/lib/python3/dist-packages:$PYTHONPATH"

# Source ROS
source /opt/ros/noetic/setup.bash

# Source workspace if it exists
if [ -f "devel/setup.bash" ]; then
    source devel/setup.bash
fi

echo "Python environment setup complete"
echo "PYTHONPATH: $PYTHONPATH"
echo "Python version: $(python3 --version)"
EOF

chmod +x setup_python_env.sh

print_status "Created setup_python_env.sh script"

echo "=========================================="
print_status "Dependencies installation completed!"
echo "=========================================="

echo ""
echo "Next steps:"
echo "1. Source the environment: source setup_python_env.sh"
echo "2. Build the workspace: catkin_make"
echo "3. Run the system: roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch"
echo ""

print_status "Script completed successfully!"
