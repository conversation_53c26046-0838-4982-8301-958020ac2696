#!/bin/bash

# GPS 100米回环实时监控脚本

echo "=========================================="
echo "🔍 GPS 100米回环实时监控系统"
echo "=========================================="
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "监控内容："
echo "✅ GPS距离起点的实时距离"
echo "✅ 100米回环触发事件"
echo "✅ SLAM匹配增强状态"
echo "✅ 回环检测置信度"
echo "✅ 轨迹长度和离开距离"
echo ""

echo "选择监控模式："
echo "1) 实时距离监控 (推荐)"
echo "2) 回环触发监控"
echo "3) 综合状态监控"
echo "4) 简化监控"
echo ""

read -p "请选择模式 (1-4): " monitor_mode

case $monitor_mode in
    1)
        echo ""
        echo "🔍 启动实时距离监控..."
        echo "显示GPS距离起点的实时变化"
        echo "按 Ctrl+C 停止监控"
        echo ""
        
        # 监控距离变化
        rostopic echo /gps_distance_to_start | while read line; do
            if [[ "$line" == *"data:"* ]]; then
                distance=$(echo "$line" | grep -o '[0-9.]*')
                current_time=$(date '+%H:%M:%S')
                
                if (( $(echo "$distance <= 100" | bc -l) )); then
                    echo "🎯 [$current_time] 距离起点: ${distance}米 ⚠️  已进入100米回环区域!"
                elif (( $(echo "$distance <= 150" | bc -l) )); then
                    echo "📍 [$current_time] 距离起点: ${distance}米 (接近回环区域)"
                elif (( $(echo "$distance <= 200" | bc -l) )); then
                    echo "📊 [$current_time] 距离起点: ${distance}米"
                else
                    echo "🚀 [$current_time] 距离起点: ${distance}米 (远离起点)"
                fi
            fi
        done
        ;;
        
    2)
        echo ""
        echo "🎯 启动回环触发监控..."
        echo "监控100米回环触发事件"
        echo ""
        
        # 监控回环触发
        rostopic echo /gps_100m_loop_trigger | while read line; do
            if [[ "$line" == *"gps_100m_loop_closure"* ]]; then
                echo "🎯 [$(date '+%H:%M:%S')] 100米回环触发!"
            elif [[ "$line" == *"distance_to_start"* ]]; then
                dist=$(echo "$line" | grep -o '[0-9.]*')
                echo "   📍 距离起点: ${dist}米"
            elif [[ "$line" == *"max_distance_traveled"* ]]; then
                max_dist=$(echo "$line" | grep -o '[0-9.]*')
                echo "   🚀 最大离开距离: ${max_dist}米"
            elif [[ "$line" == *"trajectory_length"* ]]; then
                length=$(echo "$line" | grep -o '[0-9.]*')
                echo "   📏 轨迹长度: ${length}米"
            elif [[ "$line" == *"confidence"* ]]; then
                conf=$(echo "$line" | grep -o '[0-9.]*')
                echo "   📊 置信度: $conf"
                echo "   ----------------------------------------"
            fi
        done
        ;;
        
    3)
        echo ""
        echo "📊 启动综合状态监控..."
        echo "监控所有100米回环相关状态"
        echo ""
        
        # 综合监控
        {
            echo "=== 距离监控 ==="
            rostopic echo /gps_distance_to_start | while read line; do
                if [[ "$line" == *"data:"* ]]; then
                    distance=$(echo "$line" | grep -o '[0-9.]*')
                    if (( $(echo "$distance <= 100" | bc -l) )); then
                        echo "🎯 [$(date '+%H:%M:%S')] 距离: ${distance}m ⚠️  回环区域!"
                    fi
                fi
            done
        } &
        
        {
            echo "=== 回环触发监控 ==="
            rostopic echo /gps_100m_loop_trigger | while read line; do
                if [[ "$line" == *"gps_100m_loop_closure"* ]]; then
                    echo "🔥 [$(date '+%H:%M:%S')] 100米回环触发!"
                fi
            done
        } &
        
        {
            echo "=== SLAM增强监控 ==="
            rostopic echo /slam_enhancement_trigger | while read line; do
                if [[ "$line" == *"gps_reference_slam_enhancement"* ]]; then
                    echo "⚙️  [$(date '+%H:%M:%S')] SLAM增强触发!"
                fi
            done
        } &
        
        {
            echo "=== 强制匹配监控 ==="
            rostopic echo /intelligent_force_loop_closure | while read line; do
                if [[ "$line" == *"gps_100m_return_to_start"* ]]; then
                    echo "💎 [$(date '+%H:%M:%S')] 强制匹配触发!"
                fi
            done
        } &
        
        wait
        ;;
        
    4)
        echo ""
        echo "📊 启动简化监控..."
        echo ""
        
        # 简化监控循环
        while true; do
            echo "=== GPS 100米回环状态 [$(date '+%H:%M:%S')] ==="
            
            # 检查节点状态
            if rosnode list | grep -q "gps_100m_loop_detector"; then
                echo "✅ GPS 100米检测器: 运行中"
            else
                echo "❌ GPS 100米检测器: 未运行"
            fi
            
            # 获取当前距离
            current_distance=$(rostopic echo /gps_distance_to_start -n 1 2>/dev/null | grep "data:" | grep -o '[0-9.]*' || echo "未知")
            echo "📍 当前距离起点: ${current_distance}米"
            
            # 检查参数
            threshold=$(rosparam get /gps_100m_loop_detector/start_point_threshold 2>/dev/null || echo "未设置")
            departure=$(rosparam get /gps_100m_loop_detector/min_departure_distance 2>/dev/null || echo "未设置")
            
            echo "⚙️  回环触发阈值: ${threshold}米"
            echo "⚙️  最小离开距离: ${departure}米"
            
            # 判断状态
            if [[ "$current_distance" != "未知" ]] && [[ "$threshold" != "未设置" ]]; then
                if (( $(echo "$current_distance <= $threshold" | bc -l) )); then
                    echo "🎯 状态: 在回环触发区域内!"
                elif (( $(echo "$current_distance <= $departure" | bc -l) )); then
                    echo "📊 状态: 接近回环区域"
                else
                    echo "🚀 状态: 远离起点"
                fi
            fi
            
            echo "----------------------------------------"
            sleep 5
        done
        ;;
        
    *)
        echo "无效选择，使用默认实时距离监控"
        monitor_mode=1
        ;;
esac

echo ""
echo "=========================================="
echo "监控结束"
echo "=========================================="
