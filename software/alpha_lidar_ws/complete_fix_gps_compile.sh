#!/bin/bash

# 完整修复GPS功能编译错误的脚本

echo "=========================================="
echo "🔧 完整修复GPS功能编译错误"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 备份原始文件"
cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup.$(date +%Y%m%d_%H%M%S)
echo "✓ 已备份voxelMapping.cpp"

echo ""
echo "步骤2: 修复代码问题"

# 创建临时修复文件
cat > temp_fix.py << 'EOF'
#!/usr/bin/env python3
import re
import sys

def fix_voxel_mapping_cpp(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 确保包含所有必要的头文件
    if '#include <std_msgs/Bool.h>' not in content:
        content = content.replace('#include <std_msgs/Float64.h>', 
                                '#include <std_msgs/Float64.h>\n#include <std_msgs/Bool.h>')
    
    # 修复2: 移除所有未注释的pub_icp_fitness.publish调用
    content = re.sub(r'^\s*pub_icp_fitness\.publish\([^)]+\);\s*$', 
                     r'    // pub_icp_fitness.publish(fitness_msg);  // 临时注释避免编译错误', 
                     content, flags=re.MULTILINE)
    
    # 修复3: 确保回调函数正确声明
    if 'void gps_constraint_control_callback(const std_msgs::Bool::ConstPtr& msg)' not in content:
        # 在GPS起始位置记录后添加回调函数
        callback_func = '''
// GPS约束控制回调函数
void gps_constraint_control_callback(const std_msgs::Bool::ConstPtr& msg) {
    if (dynamic_constraint_control) {
        bool previous_state = enable_gps_plane_constraint;
        enable_gps_plane_constraint = msg->data;

        if (previous_state != enable_gps_plane_constraint) {
            if (enable_gps_plane_constraint) {
                constraint_enable_count++;
                ROS_INFO("[Dynamic Control] GPS Constraint ENABLED (Count: %d)", constraint_enable_count);
            } else {
                constraint_disable_count++;
                ROS_WARN("[Dynamic Control] GPS Constraint DISABLED (Count: %d)", constraint_disable_count);
            }
        }
    }
}
'''
        # 在start_positions_set变量后添加回调函数
        content = content.replace('bool start_positions_set = false;', 
                                'bool start_positions_set = false;' + callback_func)
    
    # 修复4: 移除lambda函数，使用传统回调
    lambda_pattern = r'ros::Subscriber sub_constraint_control = nh\.subscribe\("/gps_constraint_control", 10,\s*\[.*?\]\(.*?\)\s*\{.*?\}\);'
    replacement = 'ros::Subscriber sub_constraint_control = nh.subscribe("/gps_constraint_control", 10, gps_constraint_control_callback);'
    content = re.sub(lambda_pattern, replacement, content, flags=re.DOTALL)
    
    # 修复5: 确保全局发布器声明存在
    if 'ros::Publisher pub_icp_fitness;' not in content:
        content = content.replace('ros::Publisher stats_pub;', 
                                'ros::Publisher stats_pub;\nros::Publisher pub_icp_fitness;  // ICP适应度分数发布器')
    
    # 修复6: 确保发布器初始化存在
    if 'pub_icp_fitness = nh.advertise<std_msgs::Float64>("/icp_fitness_score", 10);' not in content:
        content = content.replace('pubLaserCloudFull = nh.advertise<sensor_msgs::PointCloud2>("/cloud_registered", 100000);',
                                'pubLaserCloudFull = nh.advertise<sensor_msgs::PointCloud2>("/cloud_registered", 100000);\n    pub_icp_fitness = nh.advertise<std_msgs::Float64>("/icp_fitness_score", 10);')
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✓ voxelMapping.cpp修复完成")

if __name__ == "__main__":
    fix_voxel_mapping_cpp("src/state_estimation/src/voxelMapping.cpp")
EOF

# 运行修复脚本
python3 temp_fix.py
rm temp_fix.py

echo ""
echo "步骤3: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤4: 安装依赖"
sudo apt update
sudo apt install -y \
    ros-noetic-std-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-tf \
    ros-noetic-tf2-ros \
    libpcl-dev \
    pcl-tools

echo ""
echo "步骤5: 彻底清理编译"
rm -rf build devel
echo "✓ 清理完成"

echo ""
echo "步骤6: 编译系统"
echo "使用单线程编译..."

catkin_make --only-pkg-with-deps state_estimation -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    source devel/setup.bash
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤7: 验证编译结果"
    
    echo "检查可执行文件..."
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点"
    else
        echo "❌ SLAM核心节点"
    fi
    
    if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
        echo "✅ 强度保持模块"
    fi
    
    if [ -f "devel/lib/state_estimation/enhanced_slam_loop_closure_integration" ]; then
        echo "✅ SLAM回环集成"
    fi
    
    if [ -f "devel/lib/state_estimation/adaptive_parameter_optimizer" ]; then
        echo "✅ 自适应参数优化器"
    fi
    
    if [ -f "devel/lib/state_estimation/force_start_end_loop_matcher" ]; then
        echo "✅ 强制匹配器"
    fi
    
    echo ""
    echo "检查Python脚本..."
    if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
        echo "✅ 智能检测器"
    fi
    
    if [ -f "src/state_estimation/scripts/intelligent_gps_constraint_controller.py" ]; then
        echo "✅ GPS约束控制器"
    fi
    
    echo ""
    echo "🎉 完整GPS功能编译成功!"
    echo ""
    echo "可用的启动选项:"
    echo "1. 完整GPS约束解决方案:"
    echo "   ./solve_gps_constraint_conflict.sh"
    echo ""
    echo "2. 首尾偏差解决方案:"
    echo "   ./solve_start_end_offset.sh"
    echo ""
    echo "3. 鲁棒启动系统:"
    echo "   ./start_slam_system_robust.sh"
    echo ""
    echo "4. 手动启动完整功能:"
    echo "   roslaunch state_estimation optimized_slam_simple.launch enable_intelligent_detection:=true"
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "尝试最小化编译..."
    
    # 临时禁用GPS集成
    sed -i 's/^#define USE_GPS_INTEGRATION/\/\/ #define USE_GPS_INTEGRATION  \/\/ 临时禁用/' src/state_estimation/src/voxelMapping.cpp
    
    catkin_make --only-pkg-with-deps state_estimation -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 最小化编译成功!"
        echo "GPS集成功能已临时禁用"
        echo ""
        echo "可用功能:"
        echo "✅ 基础SLAM"
        echo "✅ 强度保持"
        echo "✅ 智能检测"
        echo "❌ GPS集成 (已禁用)"
        echo ""
        echo "启动命令:"
        echo "   ./start_basic_slam.sh"
        
        source devel/setup.bash
        chmod +x *.sh
        chmod +x src/state_estimation/scripts/*.py
        
    else
        echo ""
        echo "❌ 最小化编译也失败!"
        echo ""
        echo "恢复备份文件..."
        latest_backup=$(ls -t src/state_estimation/src/voxelMapping.cpp.backup.* | head -1)
        if [ -f "$latest_backup" ]; then
            cp "$latest_backup" src/state_estimation/src/voxelMapping.cpp
            echo "✓ 已恢复: $latest_backup"
        fi
        
        echo ""
        echo "建议的解决方案:"
        echo "1. 检查ROS完整安装:"
        echo "   sudo apt install ros-noetic-desktop-full"
        echo ""
        echo "2. 检查编译器支持C++14:"
        echo "   g++ --version"
        echo ""
        echo "3. 手动检查编译错误并修复"
        echo ""
        echo "4. 或联系技术支持"
        
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "GPS功能编译修复完成!"
echo "=========================================="
