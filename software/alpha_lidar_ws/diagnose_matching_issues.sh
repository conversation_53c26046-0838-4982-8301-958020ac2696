#!/bin/bash

# 点云匹配问题快速诊断脚本
# 自动检测常见问题并提供解决方案

echo "=========================================="
echo "🔍 点云匹配问题快速诊断"
echo "=========================================="

CONFIG_FILE="src/state_estimation/config/rs16_rotation_v2.yaml"
OPTIMIZED_CONFIG="src/state_estimation/config/rs16_rotation_v2_optimized.yaml"

# 诊断结果
issues_found=0
recommendations=()

echo "📋 正在检查系统配置..."

# 1. 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    issues_found=$((issues_found + 1))
    recommendations+=("创建或恢复配置文件")
else
    echo "✅ 配置文件存在"
fi

# 2. 检查关键参数
echo ""
echo "🔧 检查关键SLAM参数..."

if [ -f "$CONFIG_FILE" ]; then
    # 检查迭代次数
    max_iter=$(grep "max_iteration:" "$CONFIG_FILE" | awk '{print $2}')
    if [ "$max_iter" -lt 6 ]; then
        echo "⚠️  迭代次数偏低: $max_iter (建议 ≥ 6)"
        issues_found=$((issues_found + 1))
        recommendations+=("增加max_iteration到8")
    else
        echo "✅ 迭代次数合适: $max_iter"
    fi
    
    # 检查体素大小
    voxel_size=$(grep "voxel_size:" "$CONFIG_FILE" | awk '{print $2}')
    if (( $(echo "$voxel_size > 0.3" | bc -l) )); then
        echo "⚠️  体素大小偏大: $voxel_size (建议 ≤ 0.25)"
        issues_found=$((issues_found + 1))
        recommendations+=("减小voxel_size到0.2")
    else
        echo "✅ 体素大小合适: $voxel_size"
    fi
    
    # 检查平面阈值
    plannar_th=$(grep "plannar_threshold:" "$CONFIG_FILE" | awk '{print $2}')
    if (( $(echo "$plannar_th > 0.02" | bc -l) )); then
        echo "⚠️  平面阈值偏大: $plannar_th (建议 ≤ 0.01)"
        issues_found=$((issues_found + 1))
        recommendations+=("减小plannar_threshold到0.005")
    else
        echo "✅ 平面阈值合适: $plannar_th"
    fi
    
    # 检查GPS平面约束
    gps_constraint=$(grep "enable_plane_constraint:" "$CONFIG_FILE" | awk '{print $2}')
    if [ "$gps_constraint" = "true" ]; then
        echo "⚠️  GPS平面约束已启用，可能干扰匹配"
        issues_found=$((issues_found + 1))
        recommendations+=("禁用GPS平面约束")
    else
        echo "✅ GPS平面约束已禁用"
    fi
fi

# 3. 检查系统资源
echo ""
echo "💻 检查系统资源..."

# CPU核心数
cpu_cores=$(nproc)
if [ "$cpu_cores" -lt 4 ]; then
    echo "⚠️  CPU核心数较少: $cpu_cores (建议 ≥ 4)"
    issues_found=$((issues_found + 1))
    recommendations+=("考虑降低数据播放速度")
else
    echo "✅ CPU核心数充足: $cpu_cores"
fi

# 内存
total_mem=$(free -g | grep "Mem:" | awk '{print $2}')
if [ "$total_mem" -lt 8 ]; then
    echo "⚠️  内存容量较小: ${total_mem}GB (建议 ≥ 8GB)"
    issues_found=$((issues_found + 1))
    recommendations+=("增加系统内存或减小点云密度")
else
    echo "✅ 内存容量充足: ${total_mem}GB"
fi

# 4. 检查ROS环境
echo ""
echo "🤖 检查ROS环境..."

if ! command -v rosversion &> /dev/null; then
    echo "❌ ROS未安装或未正确配置"
    issues_found=$((issues_found + 1))
    recommendations+=("安装并配置ROS环境")
else
    ros_version=$(rosversion -d)
    echo "✅ ROS版本: $ros_version"
fi

# 5. 检查编译状态
echo ""
echo "🔨 检查编译状态..."

if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "✅ SLAM节点已编译"
else
    echo "❌ SLAM节点未编译"
    issues_found=$((issues_found + 1))
    recommendations+=("重新编译项目")
fi

# 6. 检查依赖库
echo ""
echo "📚 检查依赖库..."

# PCL
if pkg-config --exists pcl_common; then
    pcl_version=$(pkg-config --modversion pcl_common)
    echo "✅ PCL已安装: $pcl_version"
else
    echo "❌ PCL未安装"
    issues_found=$((issues_found + 1))
    recommendations+=("安装PCL库: sudo apt install libpcl-dev")
fi

# Eigen
if [ -d "/usr/include/eigen3" ] || [ -d "/usr/local/include/eigen3" ]; then
    echo "✅ Eigen已安装"
else
    echo "❌ Eigen未安装"
    issues_found=$((issues_found + 1))
    recommendations+=("安装Eigen库: sudo apt install libeigen3-dev")
fi

# 7. 生成诊断报告
echo ""
echo "=========================================="
echo "📊 诊断报告"
echo "=========================================="

if [ "$issues_found" -eq 0 ]; then
    echo "🎉 恭喜！未发现明显问题"
    echo ""
    echo "💡 如果仍有匹配错乱问题，建议："
    echo "1. 降低数据播放速度: rosbag play data.bag -r 0.5"
    echo "2. 使用优化配置文件"
    echo "3. 实时监控匹配质量"
else
    echo "⚠️  发现 $issues_found 个潜在问题"
    echo ""
    echo "🔧 建议的解决方案："
    for i in "${!recommendations[@]}"; do
        echo "  $((i+1)). ${recommendations[$i]}"
    done
fi

echo ""
echo "🚀 快速修复选项："
echo "1. 自动应用优化参数: ./fix_point_cloud_matching.sh"
echo "2. 使用优化配置文件: 复制 rs16_rotation_v2_optimized.yaml"
echo "3. 启动实时监控: ./monitor_point_cloud_matching.sh"
echo ""

# 询问是否自动修复
if [ "$issues_found" -gt 0 ]; then
    read -p "是否立即运行自动修复脚本？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "fix_point_cloud_matching.sh" ]; then
            echo "🔧 运行自动修复..."
            chmod +x fix_point_cloud_matching.sh
            ./fix_point_cloud_matching.sh
        else
            echo "❌ 修复脚本不存在"
        fi
    fi
fi

echo ""
echo "📋 诊断完成！"
