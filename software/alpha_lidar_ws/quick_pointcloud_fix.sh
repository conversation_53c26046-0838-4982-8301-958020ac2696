#!/bin/bash

# 快速点云问题修复脚本

echo "=========================================="
echo "⚡ 快速点云问题修复"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "步骤1: 快速诊断"
echo "=============="

# 检查常见的点云topic名称
COMMON_TOPICS=("/velodyne_points" "/points_raw" "/cloud" "/lidar_points" "/rslidar_points")

echo "检查常见点云topic..."
for topic in "${COMMON_TOPICS[@]}"; do
    if rostopic list 2>/dev/null | grep -q "^$topic$"; then
        echo "✅ 找到topic: $topic"
        FOUND_TOPIC="$topic"
        break
    fi
done

if [ -z "$FOUND_TOPIC" ]; then
    echo "❌ 未找到常见点云topic"
    echo "所有可用topic:"
    rostopic list 2>/dev/null | grep -E "(points|cloud|velodyne|lidar)"
    
    echo ""
    read -p "请输入正确的点云topic名称: " FOUND_TOPIC
fi

echo "使用点云topic: $FOUND_TOPIC"

echo ""
echo "步骤2: 设置修复参数"
echo "=================="

# 设置SLAM参数以处理空点云问题
rosparam set /state_estimation_node/lidar_topic "$FOUND_TOPIC"
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 50
rosparam set /state_estimation_node/enable_empty_scan_protection true
rosparam set /state_estimation_node/skip_empty_scans true

# 设置保守的处理参数
rosparam set /state_estimation_node/voxel_size 1.0
rosparam set /state_estimation_node/max_iterations 10
rosparam set /state_estimation_node/downsample_ratio 0.4

# 禁用可能导致崩溃的功能
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false

echo "✅ 修复参数设置完成"

echo ""
echo "步骤3: 创建点云过滤器"
echo "===================="

# 创建简单的点云过滤器
cat > src/state_estimation/scripts/simple_pointcloud_filter.py << 'EOF'
#!/usr/bin/env python3

import rospy
from sensor_msgs.msg import PointCloud2
import sensor_msgs.point_cloud2 as pc2
from std_msgs.msg import Header

class SimplePointCloudFilter:
    def __init__(self):
        rospy.init_node('simple_pointcloud_filter', anonymous=True)
        
        self.input_topic = rospy.get_param('~input_topic', '/velodyne_points')
        self.output_topic = rospy.get_param('~output_topic', '/filtered_points')
        self.min_points = rospy.get_param('~min_points', 50)
        
        self.sub = rospy.Subscriber(self.input_topic, PointCloud2, self.callback, queue_size=1)
        self.pub = rospy.Publisher(self.output_topic, PointCloud2, queue_size=1)
        
        self.empty_count = 0
        self.total_count = 0
        
        rospy.loginfo("Simple PointCloud Filter: %s -> %s", self.input_topic, self.output_topic)
    
    def callback(self, msg):
        self.total_count += 1
        
        # 检查点云是否为空
        if msg.width == 0 or msg.height == 0:
            self.empty_count += 1
            rospy.logwarn("Empty pointcloud %d/%d, skipping...", self.empty_count, self.total_count)
            return
        
        total_points = msg.width * msg.height
        
        if total_points < self.min_points:
            self.empty_count += 1
            rospy.logwarn("Too few points: %d, skipping...", total_points)
            return
        
        # 直接转发有效的点云
        self.pub.publish(msg)
        
        if self.total_count % 100 == 0:
            rospy.loginfo("Processed %d scans, %d empty", self.total_count, self.empty_count)

if __name__ == '__main__':
    try:
        filter_node = SimplePointCloudFilter()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
EOF

chmod +x src/state_estimation/scripts/simple_pointcloud_filter.py

echo "✅ 创建了简单点云过滤器"

echo ""
echo "步骤4: 创建修复启动脚本"
echo "======================"

cat > start_slam_with_filter.sh << EOF
#!/bin/bash

echo "🚀 启动带过滤器的SLAM系统"
echo "=========================="

source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "启动点云过滤器..."
rosrun state_estimation simple_pointcloud_filter.py \\
    _input_topic:="$FOUND_TOPIC" \\
    _output_topic:="/filtered_points" \\
    _min_points:=50 &
FILTER_PID=\$!

sleep 3

echo "启动SLAM核心节点..."
rosparam set /state_estimation_node/lidar_topic "/filtered_points"
rosrun state_estimation state_estimation_node &
SLAM_PID=\$!

sleep 5

if ps -p \$SLAM_PID > /dev/null; then
    echo "✅ SLAM系统启动成功"
    echo ""
    echo "现在可以播放bag文件:"
    echo "  rosbag play your_data.bag"
    echo ""
    echo "监控命令:"
    echo "  rostopic hz /filtered_points"
    echo "  rostopic echo /aft_mapped_to_init"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    cleanup() {
        echo "停止系统..."
        kill \$FILTER_PID \$SLAM_PID 2>/dev/null
        exit 0
    }
    
    trap cleanup SIGINT
    wait
else
    echo "❌ SLAM系统启动失败"
    kill \$FILTER_PID 2>/dev/null
fi
EOF

chmod +x start_slam_with_filter.sh

echo "✅ 创建了修复启动脚本: start_slam_with_filter.sh"

echo ""
echo "步骤5: 编译系统"
echo "=============="

echo "编译点云过滤器..."
catkin_make --only-pkg-with-deps state_estimation

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    source devel/setup.bash
else
    echo "❌ 编译失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "⚡ 快速修复完成"
echo "=========================================="
echo ""
echo "修复内容:"
echo "  ✅ 设置了点云topic: $FOUND_TOPIC"
echo "  ✅ 启用了空点云保护"
echo "  ✅ 创建了点云过滤器"
echo "  ✅ 设置了保守参数"
echo ""
echo "启动命令:"
echo "  ./start_slam_with_filter.sh"
echo ""
echo "测试流程:"
echo "1. 启动修复后的系统:"
echo "   ./start_slam_with_filter.sh"
echo ""
echo "2. 在另一个终端播放bag:"
echo "   rosbag play your_data.bag"
echo ""
echo "3. 检查系统状态:"
echo "   rostopic hz /filtered_points"
echo "   rostopic echo /aft_mapped_to_init"
