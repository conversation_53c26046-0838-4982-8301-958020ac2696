#!/bin/bash

# GPS软约束SLAM调试启动脚本

echo "=========================================="
echo "🐛 GPS软约束SLAM调试模式启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/debug_gps_soft_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "调试输出目录: $OUTPUT_DIR"

echo ""
echo "调试模式选项："
echo "1) 仅启动GPS软约束检测器"
echo "2) 仅启动SLAM核心（无GPS）"
echo "3) 逐步启动所有组件"
echo "4) 使用GDB调试SLAM核心"
echo ""

read -p "请选择调试模式 (1-4): " debug_choice

case $debug_choice in
    1)
        echo ""
        echo "🎯 启动GPS软约束检测器调试模式"
        echo "=================================="
        
        # 设置调试参数
        rosparam set /gps_soft_loop_detector/gps_high_quality_threshold 4
        rosparam set /gps_soft_loop_detector/gps_medium_quality_threshold 1
        rosparam set /gps_soft_loop_detector/high_quality_proximity 12.0
        rosparam set /gps_soft_loop_detector/medium_quality_proximity 20.0
        rosparam set /gps_soft_loop_detector/low_quality_proximity 30.0
        
        echo "启动GPS软约束检测器..."
        rosrun state_estimation gps_soft_loop_detector.py &
        GPS_DETECTOR_PID=$!
        
        echo "启动GPS质量分析器..."
        rosrun state_estimation gps_quality_analyzer.py &
        QUALITY_ANALYZER_PID=$!
        
        echo "启动软约束处理器..."
        rosrun state_estimation soft_constraint_loop_processor.py &
        PROCESSOR_PID=$!
        
        echo ""
        echo "✅ GPS软约束组件已启动"
        echo "监控命令："
        echo "  rostopic echo /gps_soft_loop_trigger"
        echo "  rostopic echo /gps_quality_report"
        echo "  rostopic echo /loop_confidence_score"
        echo ""
        echo "按任意键停止..."
        read -n 1
        
        kill $GPS_DETECTOR_PID $QUALITY_ANALYZER_PID $PROCESSOR_PID 2>/dev/null
        ;;
        
    2)
        echo ""
        echo "🎯 启动SLAM核心调试模式（无GPS）"
        echo "=================================="
        
        # 禁用GPS功能
        rosparam set /state_estimation_node/enable_gps false
        rosparam set /state_estimation_node/gps/enable_plane_constraint false
        
        echo "启动SLAM核心节点（无GPS模式）..."
        rosrun state_estimation state_estimation_node &
        SLAM_PID=$!
        
        echo "等待SLAM初始化..."
        sleep 5
        
        if ps -p $SLAM_PID > /dev/null; then
            echo "✅ SLAM核心节点运行正常"
            
            echo "启动强度保持模块..."
            rosrun state_estimation intensity_preserving_pcd_saver \
                _save_directory:="$OUTPUT_DIR" &
            INTENSITY_PID=$!
            
            echo ""
            echo "✅ SLAM核心系统已启动（无GPS）"
            echo "监控命令："
            echo "  rostopic echo /aft_mapped_to_init"
            echo "  rostopic echo /cloud_registered"
            echo ""
            echo "按任意键停止..."
            read -n 1
            
            kill $SLAM_PID $INTENSITY_PID 2>/dev/null
        else
            echo "❌ SLAM核心节点启动失败"
            echo "检查错误日志："
            echo "  tail -f ~/.ros/log/latest/state_estimation_node-*.log"
        fi
        ;;
        
    3)
        echo ""
        echo "🎯 逐步启动所有组件"
        echo "===================="
        
        echo "步骤1: 启动SLAM核心..."
        # 先禁用GPS避免崩溃
        rosparam set /state_estimation_node/enable_gps false
        rosrun state_estimation state_estimation_node &
        SLAM_PID=$!
        sleep 5
        
        if ! ps -p $SLAM_PID > /dev/null; then
            echo "❌ SLAM核心启动失败，停止调试"
            exit 1
        fi
        echo "✅ SLAM核心启动成功"
        
        echo ""
        echo "步骤2: 启动GPS软约束检测器..."
        rosrun state_estimation gps_soft_loop_detector.py &
        GPS_PID=$!
        sleep 3
        echo "✅ GPS软约束检测器启动"
        
        echo ""
        echo "步骤3: 启动GPS质量分析器..."
        rosrun state_estimation gps_quality_analyzer.py &
        QUALITY_PID=$!
        sleep 2
        echo "✅ GPS质量分析器启动"
        
        echo ""
        echo "步骤4: 启动软约束处理器..."
        rosrun state_estimation soft_constraint_loop_processor.py &
        PROCESSOR_PID=$!
        sleep 2
        echo "✅ 软约束处理器启动"
        
        echo ""
        echo "步骤5: 启动强度保持模块..."
        rosrun state_estimation intensity_preserving_pcd_saver \
            _save_directory:="$OUTPUT_DIR" &
        INTENSITY_PID=$!
        sleep 2
        echo "✅ 强度保持模块启动"
        
        echo ""
        echo "步骤6: 启动智能检测器..."
        rosrun state_estimation intelligent_start_end_detector.py &
        DETECTOR_PID=$!
        sleep 2
        echo "✅ 智能检测器启动"
        
        echo ""
        echo "步骤7: 重新启用GPS功能..."
        rosparam set /state_estimation_node/enable_gps true
        rosparam set /state_estimation_node/gps/enable_plane_constraint false
        rosparam set /state_estimation_node/gps/use_soft_constraint true
        echo "✅ GPS软约束功能已启用"
        
        echo ""
        echo "🎉 所有组件启动完成!"
        echo ""
        echo "系统状态检查："
        echo "  SLAM核心: $(ps -p $SLAM_PID > /dev/null && echo '运行中' || echo '已停止')"
        echo "  GPS检测器: $(ps -p $GPS_PID > /dev/null && echo '运行中' || echo '已停止')"
        echo "  质量分析器: $(ps -p $QUALITY_PID > /dev/null && echo '运行中' || echo '已停止')"
        echo "  软约束处理器: $(ps -p $PROCESSOR_PID > /dev/null && echo '运行中' || echo '已停止')"
        echo ""
        echo "监控命令："
        echo "  rostopic list | grep gps"
        echo "  rostopic echo /gps_soft_loop_trigger"
        echo "  rostopic echo /aft_mapped_to_init"
        echo ""
        echo "按任意键停止所有组件..."
        read -n 1
        
        kill $SLAM_PID $GPS_PID $QUALITY_PID $PROCESSOR_PID $INTENSITY_PID $DETECTOR_PID 2>/dev/null
        ;;
        
    4)
        echo ""
        echo "🐛 使用GDB调试SLAM核心"
        echo "======================"
        
        # 检查GDB是否安装
        if ! command -v gdb &> /dev/null; then
            echo "安装GDB..."
            sudo apt install -y gdb
        fi
        
        echo "准备GDB调试环境..."
        
        # 创建GDB命令文件
        cat > /tmp/gdb_commands.txt << 'EOF'
set environment ROS_MASTER_URI=http://localhost:11311
set environment ROS_HOSTNAME=localhost
run
bt
info registers
quit
EOF
        
        echo ""
        echo "启动GDB调试SLAM核心节点..."
        echo "GDB命令："
        echo "  run - 运行程序"
        echo "  bt - 显示调用栈"
        echo "  c - 继续执行"
        echo "  quit - 退出GDB"
        echo ""
        
        # 禁用GPS避免复杂性
        rosparam set /state_estimation_node/enable_gps false
        
        gdb --args devel/lib/state_estimation/state_estimation_node __name:=state_estimation_node
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "调试会话结束"
echo "=========================================="
