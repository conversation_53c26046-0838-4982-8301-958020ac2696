#!/bin/bash

# GPS参考位置增强SLAM系统启动脚本
# 仅使用GPS作为参考位置，通过增强SLAM自身匹配能力解决首尾偏差

echo "=========================================="
echo "🎯 GPS参考位置增强SLAM系统启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "✅ GPS仅作为参考位置，不直接修改SLAM"
echo "✅ 避免GPS约束导致的点云匹配错位"
echo "✅ 通过增强SLAM匹配能力解决偏差"
echo "✅ 多分辨率、多算法自适应匹配"
echo "✅ 根据GPS质量动态调整增强级别"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_reference_enhanced_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "GPS参考增强配置："
echo "1) 高精度模式 (RTK环境，严格匹配)"
echo "2) 平衡模式 (混合GPS环境，推荐)"
echo "3) 鲁棒模式 (低质量GPS，宽松匹配)"
echo "4) 自定义模式"
echo ""

read -p "请选择模式 (1-4): " mode_choice

case $mode_choice in
    1)
        echo "配置高精度模式..."
        GPS_REFERENCE_RADIUS=30.0
        SLAM_ENHANCEMENT_RADIUS=20.0
        ENHANCED_SEARCH_RADIUS=60.0
        ENHANCED_VOXEL_SIZE=0.01
        ENHANCED_MAX_ITERATIONS=800
        MULTI_RESOLUTION_LEVELS=4
        echo "✅ 高精度模式: 严格参数，精细匹配"
        ;;
    2)
        echo "配置平衡模式..."
        GPS_REFERENCE_RADIUS=50.0
        SLAM_ENHANCEMENT_RADIUS=30.0
        ENHANCED_SEARCH_RADIUS=80.0
        ENHANCED_VOXEL_SIZE=0.02
        ENHANCED_MAX_ITERATIONS=500
        MULTI_RESOLUTION_LEVELS=3
        echo "✅ 平衡模式: 平衡精度与性能"
        ;;
    3)
        echo "配置鲁棒模式..."
        GPS_REFERENCE_RADIUS=80.0
        SLAM_ENHANCEMENT_RADIUS=50.0
        ENHANCED_SEARCH_RADIUS=120.0
        ENHANCED_VOXEL_SIZE=0.03
        ENHANCED_MAX_ITERATIONS=300
        MULTI_RESOLUTION_LEVELS=2
        echo "✅ 鲁棒模式: 宽松参数，容错性强"
        ;;
    4)
        echo "自定义模式配置："
        read -p "GPS参考半径 (20-100米): " GPS_REFERENCE_RADIUS
        read -p "SLAM增强半径 (15-60米): " SLAM_ENHANCEMENT_RADIUS
        read -p "增强搜索半径 (50-150米): " ENHANCED_SEARCH_RADIUS
        read -p "增强体素大小 (0.01-0.05): " ENHANCED_VOXEL_SIZE
        read -p "最大迭代次数 (200-1000): " ENHANCED_MAX_ITERATIONS
        read -p "多分辨率层级 (2-5): " MULTI_RESOLUTION_LEVELS
        echo "✅ 自定义模式已配置"
        ;;
    *)
        echo "使用默认平衡模式..."
        GPS_REFERENCE_RADIUS=50.0
        SLAM_ENHANCEMENT_RADIUS=30.0
        ENHANCED_SEARCH_RADIUS=80.0
        ENHANCED_VOXEL_SIZE=0.02
        ENHANCED_MAX_ITERATIONS=500
        MULTI_RESOLUTION_LEVELS=3
        ;;
esac

echo ""
echo "SLAM增强算法配置："
echo "1) 单算法模式 (ICP，速度快)"
echo "2) 双算法模式 (ICP+NDT，平衡)"
echo "3) 多算法模式 (ICP+NDT+GICP，精度高)"
echo ""

read -p "请选择算法模式 (1-3): " algo_choice

case $algo_choice in
    1)
        ALGORITHM_MODE="single"
        echo "✅ 单算法模式: ICP"
        ;;
    2)
        ALGORITHM_MODE="dual"
        echo "✅ 双算法模式: ICP + NDT"
        ;;
    3)
        ALGORITHM_MODE="multi"
        echo "✅ 多算法模式: ICP + NDT + GICP"
        ;;
    *)
        ALGORITHM_MODE="dual"
        echo "✅ 默认双算法模式"
        ;;
esac

echo ""
echo "启动GPS参考增强SLAM系统..."

# 启动优化的SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=false \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 10

echo ""
echo "设置GPS参考增强参数..."

# 确保完全禁用GPS平面约束
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/gps/enable_soft_constraint false
rosparam set /state_estimation_node/gps/enable_reference_guidance true

# 设置GPS参考增强参数
rosparam set /gps_reference_enhanced_slam/gps_reference_radius $GPS_REFERENCE_RADIUS
rosparam set /gps_reference_enhanced_slam/slam_enhancement_radius $SLAM_ENHANCEMENT_RADIUS
rosparam set /gps_reference_enhanced_slam/enhanced_search_radius $ENHANCED_SEARCH_RADIUS
rosparam set /gps_reference_enhanced_slam/enhanced_voxel_size $ENHANCED_VOXEL_SIZE
rosparam set /gps_reference_enhanced_slam/enhanced_max_iterations $ENHANCED_MAX_ITERATIONS
rosparam set /gps_reference_enhanced_slam/multi_resolution_levels $MULTI_RESOLUTION_LEVELS

# 设置算法模式
rosparam set /gps_reference_enhanced_slam/algorithm_mode $ALGORITHM_MODE

# 设置智能检测参数 (更宽松，让GPS参考增强主导)
rosparam set /intelligent_start_end_detector/return_threshold 60.0
rosparam set /intelligent_start_end_detector/departure_threshold 40.0

# 设置强制匹配器参数 (配合GPS参考增强)
rosparam set /force_start_end_loop_matcher/voxel_size $ENHANCED_VOXEL_SIZE
rosparam set /force_start_end_loop_matcher/search_radius $ENHANCED_SEARCH_RADIUS
rosparam set /force_start_end_loop_matcher/max_iterations $ENHANCED_MAX_ITERATIONS
rosparam set /force_start_end_loop_matcher/use_multi_resolution true
rosparam set /force_start_end_loop_matcher/resolution_levels $MULTI_RESOLUTION_LEVELS

# 禁用GPS约束控制器 (避免干扰)
rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 1.0
rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 1.0

echo "✅ 参数设置完成"

echo ""
echo "=========================================="
echo "🚀 GPS参考增强SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统配置:"
echo "  输出目录: $OUTPUT_DIR"
echo "  GPS平面约束: ❌ 完全禁用"
echo "  GPS参考半径: ${GPS_REFERENCE_RADIUS}米"
echo "  SLAM增强半径: ${SLAM_ENHANCEMENT_RADIUS}米"
echo "  增强搜索半径: ${ENHANCED_SEARCH_RADIUS}米"
echo "  体素大小: ${ENHANCED_VOXEL_SIZE}米"
echo "  最大迭代: ${ENHANCED_MAX_ITERATIONS}次"
echo "  算法模式: $ALGORITHM_MODE"
echo ""
echo "工作原理:"
echo "🎯 GPS仅提供参考位置信息"
echo "📍 不直接修改SLAM位置，避免点云错位"
echo "🔄 GPS接近时触发SLAM匹配增强"
echo "💎 多分辨率、多算法自适应匹配"
echo "⚡ 根据GPS质量动态调整增强级别"
echo ""
echo "增强级别:"
echo "  🔥 高精度增强: RTK质量，4层分辨率，3种算法"
echo "  ⚙️  中等精度增强: DGPS质量，3层分辨率，2种算法"
echo "  📊 基础增强: 单点定位，2层分辨率，1种算法"
echo ""
echo "监控命令:"
echo "  GPS参考状态: rostopic echo /gps_reference_slam_status"
echo "  增强触发:    rostopic echo /slam_enhancement_trigger"
echo "  增强参数:    rostopic echo /enhanced_slam_parameters"
echo "  参考引导:    rostopic echo /gps_reference_guidance"
echo "  强制匹配:    rostopic echo /force_match_score"
echo ""
echo "实时参数调节:"
echo "  调节GPS参考半径: rosparam set /gps_reference_enhanced_slam/gps_reference_radius 40.0"
echo "  调节增强半径:    rosparam set /gps_reference_enhanced_slam/slam_enhancement_radius 25.0"
echo "  调节搜索半径:    rosparam set /gps_reference_enhanced_slam/enhanced_search_radius 100.0"
echo "  调节体素大小:    rosparam set /gps_reference_enhanced_slam/enhanced_voxel_size 0.015"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果:"
echo "✅ 完全避免GPS约束导致的点云匹配错位"
echo "✅ GPS质量好时使用高精度增强匹配"
echo "✅ GPS质量差时使用鲁棒增强匹配"
echo "✅ SLAM自身匹配能力显著提升"
echo "✅ 首尾偏差大幅减少，轨迹更精确"
echo ""
echo "按 Ctrl+C 停止系统"
echo ""
echo "🔍 实时监控GPS软约束效果:"
echo "  监控脚本: ./monitor_gps_soft_constraint.sh"
echo "  验证脚本: ./verify_gps_soft_constraint_effect.sh"
echo ""
echo "📊 关键监控命令:"
echo "  GPS增强触发: rostopic echo /slam_enhancement_trigger"
echo "  GPS参考引导: rostopic echo /gps_reference_guidance"
echo "  回环置信度:  rostopic echo /loop_confidence_score"
echo "  增强参数:    rostopic echo /enhanced_slam_parameters"
echo ""
echo "🎯 GPS软约束工作指标:"
echo "  ✅ GPS接近历史位置时应触发增强 (距离<50米)"
echo "  ✅ 根据GPS质量动态调整增强级别"
echo "  ✅ 高质量GPS使用高精度增强 (RTK: 4层分辨率)"
echo "  ✅ 低质量GPS使用基础增强 (单点: 2层分辨率)"
echo "  ✅ 回环置信度>0.7时触发强制匹配"
echo ""

# 启动后台监控 (可选)
read -p "是否启动后台监控? (y/n): " start_monitor
if [[ "$start_monitor" == "y" || "$start_monitor" == "Y" ]]; then
    echo "启动后台监控..."
    ./monitor_gps_soft_constraint.sh &
    MONITOR_PID=$!
    echo "监控进程PID: $MONITOR_PID"

    # 等待用户中断
    wait

    # 清理监控进程
    kill $MONITOR_PID 2>/dev/null
else
    # 等待用户中断
    wait
fi
