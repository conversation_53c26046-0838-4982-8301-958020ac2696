#!/bin/bash

# 监控首尾偏差解决方案的实时脚本

echo "=========================================="
echo "🔍 首尾偏差解决方案监控"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动解决方案系统"
    exit 1
fi

echo "选择监控模式："
echo "1) 实时GPS距离和匹配状态"
echo "2) 强制匹配结果监控"
echo "3) 匹配算法性能对比"
echo "4) 系统综合诊断"
echo "5) 参数实时调节"
echo ""

read -p "请选择模式 (1-5): " choice

case $choice in
    1)
        echo "🔍 实时GPS距离和匹配状态监控"
        echo ""
        echo "时间     | GPS距离 | 状态      | 匹配触发"
        echo "---------|---------|-----------|----------"
        
        # 监控GPS距离和匹配状态
        (rostopic echo /intelligent_detector_status | grep -o '"distance_to_start": [0-9.]*' &
         rostopic echo /force_match_score | grep -o '"success": [a-z]*' &
         wait) | while read line; do
            if [[ $line == *"distance_to_start"* ]]; then
                distance=$(echo $line | grep -o '[0-9.]*')
                if (( $(echo "$distance < 2" | bc -l) )); then
                    status="🎯 触发区域"
                elif (( $(echo "$distance < 10" | bc -l) )); then
                    status="🚶 接近中"
                else
                    status="🚀 远离中"
                fi
                echo "$(date '+%H:%M:%S') | ${distance}m    | $status | 等待中"
            elif [[ $line == *"success"* ]]; then
                success=$(echo $line | grep -o '[a-z]*')
                if [[ $success == "true" ]]; then
                    echo "$(date '+%H:%M:%S') | ---     | ---       | ✅ 成功"
                else
                    echo "$(date '+%H:%M:%S') | ---     | ---       | ❌ 失败"
                fi
            fi
        done
        ;;
    2)
        echo "🔥 强制匹配结果监控"
        echo ""
        rostopic echo /force_match_score | while read line; do
            if [[ $line == *"method"* ]]; then
                echo "$(date '+%H:%M:%S') - 匹配方法: $line"
            elif [[ $line == *"fitness_score"* ]]; then
                echo "$(date '+%H:%M:%S') - 适应度分数: $line"
            elif [[ $line == *"success"* ]]; then
                echo "$(date '+%H:%M:%S') - 匹配结果: $line"
                echo "----------------------------------------"
            fi
        done
        ;;
    3)
        echo "📊 匹配算法性能对比"
        echo ""
        echo "等待匹配结果..."
        timeout 300 rostopic echo /force_match_debug -n 5 | grep -E "(method|fitness_score|iterations|converged)"
        ;;
    4)
        echo "🔍 系统综合诊断"
        echo ""
        
        echo "=== 智能检测器状态 ==="
        timeout 5 rostopic echo /intelligent_detector_status -n 1 | grep -E "(distance_to_start|has_departed|within_return_zone)"
        
        echo ""
        echo "=== 强制匹配器状态 ==="
        echo "节点运行状态: $(rosnode list | grep force_start_end_loop_matcher | wc -l) 个"
        
        echo ""
        echo "=== 最近匹配结果 ==="
        timeout 3 rostopic echo /force_match_score -n 1
        
        echo ""
        echo "=== 当前参数设置 ==="
        echo "GPS返回阈值: $(rosparam get /intelligent_start_end_detector/return_threshold 2>/dev/null)米"
        echo "体素大小: $(rosparam get /force_start_end_loop_matcher/voxel_size 2>/dev/null)米"
        echo "匹配阈值: $(rosparam get /force_start_end_loop_matcher/outlier_rejection_threshold 2>/dev/null)"
        echo "最大迭代: $(rosparam get /force_start_end_loop_matcher/max_iterations 2>/dev/null)次"
        
        echo ""
        echo "=== 系统建议 ==="
        distance=$(timeout 3 rostopic echo /intelligent_detector_status -n 1 | grep -o '"distance_to_start": [0-9.]*' | grep -o '[0-9.]*' | head -1)
        if [ ! -z "$distance" ]; then
            if (( $(echo "$distance < 1" | bc -l) )); then
                echo "🎯 GPS距离很近(${distance}m)，应该触发强制匹配"
            elif (( $(echo "$distance < 5" | bc -l) )); then
                echo "🚶 GPS距离较近(${distance}m)，准备触发匹配"
            else
                echo "🚀 GPS距离较远(${distance}m)，等待接近"
            fi
        fi
        ;;
    5)
        echo "🔧 参数实时调节"
        echo ""
        echo "当前关键参数："
        echo "GPS返回阈值: $(rosparam get /intelligent_start_end_detector/return_threshold 2>/dev/null)米"
        echo "体素大小: $(rosparam get /force_start_end_loop_matcher/voxel_size 2>/dev/null)米"
        echo "匹配阈值: $(rosparam get /force_start_end_loop_matcher/outlier_rejection_threshold 2>/dev/null)"
        echo ""
        
        echo "调节选项："
        echo "1) 调节GPS触发阈值 (当前: $(rosparam get /intelligent_start_end_detector/return_threshold 2>/dev/null)米)"
        echo "2) 调节体素大小 (当前: $(rosparam get /force_start_end_loop_matcher/voxel_size 2>/dev/null)米)"
        echo "3) 调节匹配阈值 (当前: $(rosparam get /force_start_end_loop_matcher/outlier_rejection_threshold 2>/dev/null))"
        echo "4) 调节最大迭代次数"
        echo "5) 一键设置极限精度"
        echo ""
        
        read -p "请选择 (1-5): " param_choice
        
        case $param_choice in
            1)
                echo "当前GPS触发阈值: $(rosparam get /intelligent_start_end_detector/return_threshold)米"
                read -p "新的GPS触发阈值 (米, 建议1.0-5.0): " new_threshold
                rosparam set /intelligent_start_end_detector/return_threshold $new_threshold
                echo "✅ GPS触发阈值已设置为 ${new_threshold}米"
                ;;
            2)
                echo "当前体素大小: $(rosparam get /force_start_end_loop_matcher/voxel_size)米"
                read -p "新的体素大小 (米, 建议0.01-0.1): " new_voxel
                rosparam set /force_start_end_loop_matcher/voxel_size $new_voxel
                echo "✅ 体素大小已设置为 ${new_voxel}米"
                ;;
            3)
                echo "当前匹配阈值: $(rosparam get /force_start_end_loop_matcher/outlier_rejection_threshold)"
                read -p "新的匹配阈值 (建议0.1-1.0): " new_match_threshold
                rosparam set /force_start_end_loop_matcher/outlier_rejection_threshold $new_match_threshold
                echo "✅ 匹配阈值已设置为 $new_match_threshold"
                ;;
            4)
                echo "当前最大迭代: $(rosparam get /force_start_end_loop_matcher/max_iterations)次"
                read -p "新的最大迭代次数 (建议100-1000): " new_iter
                rosparam set /force_start_end_loop_matcher/max_iterations $new_iter
                echo "✅ 最大迭代次数已设置为 ${new_iter}次"
                ;;
            5)
                echo "设置极限精度参数..."
                rosparam set /intelligent_start_end_detector/return_threshold 1.0
                rosparam set /force_start_end_loop_matcher/voxel_size 0.01
                rosparam set /force_start_end_loop_matcher/outlier_rejection_threshold 0.1
                rosparam set /force_start_end_loop_matcher/max_iterations 1000
                rosparam set /force_start_end_loop_matcher/max_correspondence_distance 0.3
                echo "✅ 极限精度参数已设置:"
                echo "   GPS触发阈值: 1.0米"
                echo "   体素大小: 0.01米"
                echo "   匹配阈值: 0.1"
                echo "   最大迭代: 1000次"
                ;;
        esac
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "监控结束"
