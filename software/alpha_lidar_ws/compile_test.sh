#!/bin/bash

# 编译测试脚本
# 用于测试优化系统的编译

echo "=== 开始编译优化SLAM系统 ==="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash

# 清理之前的编译
echo "清理之前的编译..."
catkin_make clean

# 开始编译
echo "开始编译..."
catkin_make

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "=== 编译成功! ==="
    echo ""
    echo "新增的可执行文件:"
    echo "- intensity_preserving_pcd_saver"
    echo "- adaptive_parameter_optimizer"
    echo "- enhanced_slam_loop_closure_integration"
    echo ""
    echo "新增的Python脚本:"
    echo "- enhanced_gps_loop_closure_optimizer.py"
    echo "- intensity_quality_monitor.py"
    echo "- advanced_intensity_analyzer.py"
    echo "- performance_dashboard.py"
    echo ""
    echo "启动完整优化系统:"
    echo "roslaunch state_estimation optimized_slam_system.launch"
    echo ""
else
    echo "=== 编译失败! ==="
    echo "请检查错误信息并修复问题"
    exit 1
fi

# 设置环境
echo "设置环境..."
source devel/setup.bash

echo "=== 编译完成 ==="
