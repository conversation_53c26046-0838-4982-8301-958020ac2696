#!/bin/bash

# GPS软约束效果验证脚本
# 验证GPS软约束是否真正发挥作用

echo "=========================================="
echo "🔬 GPS软约束效果验证系统"
echo "=========================================="
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "验证项目："
echo "1️⃣  GPS约束配置验证"
echo "2️⃣  节点运行状态验证"
echo "3️⃣  Topic活动验证"
echo "4️⃣  参数设置验证"
echo "5️⃣  实时效果验证"
echo ""

echo "步骤1: GPS约束配置验证"
echo "==============================="

CONFIG_FILE="src/state_estimation/config/rs16_rotation_v2.yaml"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置文件存在: $CONFIG_FILE"
    
    # 检查关键配置
    if grep -q "enable_plane_constraint: false" "$CONFIG_FILE"; then
        echo "✅ GPS平面约束已禁用"
    else
        echo "❌ GPS平面约束未正确禁用"
        echo "   当前设置: $(grep 'enable_plane_constraint:' $CONFIG_FILE)"
    fi
    
    if grep -q "constraint_mode: 0" "$CONFIG_FILE"; then
        echo "✅ GPS约束模式已禁用"
    else
        echo "❌ GPS约束模式未正确禁用"
        echo "   当前设置: $(grep 'constraint_mode:' $CONFIG_FILE)"
    fi
    
    plane_weight=$(grep "plane_constraint_weight:" "$CONFIG_FILE" | grep -o '[0-9.]*')
    if (( $(echo "$plane_weight == 0" | bc -l) )); then
        echo "✅ GPS约束权重已设置为0"
    else
        echo "❌ GPS约束权重未设置为0"
        echo "   当前权重: $plane_weight"
    fi
else
    echo "❌ 配置文件不存在"
fi

echo ""
echo "步骤2: 节点运行状态验证"
echo "==============================="

# 检查关键节点
nodes_to_check=(
    "gps_reference_enhanced_slam:GPS参考增强器"
    "gps_soft_constraint_loop_detector:GPS软约束检测器"
    "intelligent_start_end_detector:智能检测器"
    "state_estimation_node:SLAM核心节点"
)

for node_info in "${nodes_to_check[@]}"; do
    IFS=':' read -r node_name description <<< "$node_info"
    if rosnode list | grep -q "$node_name"; then
        echo "✅ $description: 运行中"
    else
        echo "❌ $description: 未运行"
    fi
done

echo ""
echo "步骤3: Topic活动验证"
echo "==============================="

# 检查关键topic
topics_to_check=(
    "/slam_enhancement_trigger:SLAM增强触发"
    "/gps_reference_guidance:GPS参考引导"
    "/gps_soft_loop_trigger:GPS软约束触发"
    "/loop_confidence_score:回环置信度"
    "/enhanced_slam_parameters:增强参数"
)

for topic_info in "${topics_to_check[@]}"; do
    IFS=':' read -r topic_name description <<< "$topic_info"
    if rostopic list | grep -q "$topic_name"; then
        echo "✅ $description: topic存在"
        
        # 检查topic是否有数据
        timeout 3 rostopic echo "$topic_name" -n 1 >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "   📡 有数据流"
        else
            echo "   📡 暂无数据 (正常，等待触发)"
        fi
    else
        echo "❌ $description: topic不存在"
    fi
done

echo ""
echo "步骤4: 参数设置验证"
echo "==============================="

# 检查运行时参数
params_to_check=(
    "/state_estimation_node/gps/enable_plane_constraint:GPS平面约束"
    "/gps_reference_enhanced_slam/gps_reference_radius:GPS参考半径"
    "/gps_reference_enhanced_slam/slam_enhancement_radius:SLAM增强半径"
    "/gps_soft_constraint_loop_detector/good_gps_threshold:高质量GPS阈值"
)

for param_info in "${params_to_check[@]}"; do
    IFS=':' read -r param_name description <<< "$param_info"
    param_value=$(rosparam get "$param_name" 2>/dev/null || echo "未设置")
    echo "⚙️  $description: $param_value"
done

echo ""
echo "步骤5: 实时效果验证"
echo "==============================="

echo "选择验证方式："
echo "1) 监听GPS软约束触发事件 (30秒)"
echo "2) 检查GPS质量变化 (30秒)"
echo "3) 监控SLAM增强参数变化 (30秒)"
echo "4) 综合实时监控 (60秒)"
echo "5) 跳过实时验证"
echo ""

read -p "请选择 (1-5): " verify_choice

case $verify_choice in
    1)
        echo ""
        echo "🔍 监听GPS软约束触发事件 (30秒)..."
        echo "如果GPS接近历史位置，应该看到触发事件"
        echo ""
        
        timeout 30 rostopic echo /slam_enhancement_trigger | while read line; do
            if [[ "$line" == *"gps_reference_slam_enhancement"* ]]; then
                echo "🎯 [$(date '+%H:%M:%S')] GPS参考增强触发!"
            elif [[ "$line" == *"enhancement_level"* ]]; then
                level=$(echo "$line" | grep -o '"[^"]*"' | tr -d '"')
                echo "   ✨ 增强级别: $level"
            elif [[ "$line" == *"gps_distance"* ]]; then
                dist=$(echo "$line" | grep -o '[0-9.]*')
                echo "   📍 GPS距离: ${dist}米"
            elif [[ "$line" == *"enhancement_score"* ]]; then
                score=$(echo "$line" | grep -o '[0-9.]*')
                echo "   📊 增强评分: $score"
                echo "   ----------------------------------------"
            fi
        done
        
        echo "✅ GPS软约束触发监听完成"
        ;;
        
    2)
        echo ""
        echo "📡 检查GPS质量变化 (30秒)..."
        echo ""
        
        timeout 30 rostopic echo /gps_reference_slam_status | while read line; do
            if [[ "$line" == *"average_gps_quality"* ]]; then
                quality=$(echo "$line" | grep -o '[0-9.]*')
                echo "📡 [$(date '+%H:%M:%S')] 平均GPS质量: $quality"
            elif [[ "$line" == *"total_enhancements"* ]]; then
                count=$(echo "$line" | grep -o '[0-9]*')
                echo "🔢 总增强次数: $count"
            elif [[ "$line" == *"trajectory_length"* ]]; then
                length=$(echo "$line" | grep -o '[0-9.]*')
                echo "📏 轨迹长度: ${length}米"
            fi
        done
        
        echo "✅ GPS质量监控完成"
        ;;
        
    3)
        echo ""
        echo "⚙️  监控SLAM增强参数变化 (30秒)..."
        echo ""
        
        timeout 30 rostopic echo /enhanced_slam_parameters | while read line; do
            if [[ "$line" == *"search_radius"* ]]; then
                radius=$(echo "$line" | grep -o '[0-9.]*')
                echo "🔍 [$(date '+%H:%M:%S')] 搜索半径: ${radius}米"
            elif [[ "$line" == *"voxel_size"* ]]; then
                voxel=$(echo "$line" | grep -o '[0-9.]*')
                echo "📦 体素大小: ${voxel}米"
            elif [[ "$line" == *"max_iterations"* ]]; then
                iter=$(echo "$line" | grep -o '[0-9]*')
                echo "🔄 最大迭代: ${iter}次"
            fi
        done
        
        echo "✅ SLAM增强参数监控完成"
        ;;
        
    4)
        echo ""
        echo "🔍 综合实时监控 (60秒)..."
        echo "监控所有GPS软约束相关活动"
        echo ""
        
        {
            timeout 60 rostopic echo /slam_enhancement_trigger | while read line; do
                if [[ "$line" == *"gps_reference_slam_enhancement"* ]]; then
                    echo "🎯 [$(date '+%H:%M:%S')] GPS参考增强触发!"
                fi
            done
        } &
        
        {
            timeout 60 rostopic echo /gps_soft_loop_trigger | while read line; do
                if [[ "$line" == *"gps_soft_constraint_loop_closure"* ]]; then
                    echo "🔄 [$(date '+%H:%M:%S')] GPS软约束回环触发!"
                fi
            done
        } &
        
        {
            timeout 60 rostopic echo /loop_confidence_score | while read line; do
                if [[ "$line" == *"data:"* ]]; then
                    confidence=$(echo "$line" | grep -o '[0-9.]*')
                    if (( $(echo "$confidence > 0.7" | bc -l) )); then
                        echo "🔥 [$(date '+%H:%M:%S')] 高置信度回环: $confidence"
                    fi
                fi
            done
        } &
        
        wait
        echo "✅ 综合监控完成"
        ;;
        
    5)
        echo "⏭️  跳过实时验证"
        ;;
        
    *)
        echo "无效选择，跳过实时验证"
        ;;
esac

echo ""
echo "=========================================="
echo "📋 验证结果总结"
echo "=========================================="
echo ""

# 生成验证报告
echo "GPS软约束系统验证报告 [$(date)]"
echo ""

# 检查配置
if grep -q "enable_plane_constraint: false" "$CONFIG_FILE" 2>/dev/null; then
    echo "✅ 配置验证: GPS平面约束已正确禁用"
else
    echo "❌ 配置验证: GPS平面约束配置有问题"
fi

# 检查节点
if rosnode list | grep -q "gps_reference_enhanced_slam"; then
    echo "✅ 节点验证: GPS参考增强器正常运行"
else
    echo "❌ 节点验证: GPS参考增强器未运行"
fi

# 检查topic
if rostopic list | grep -q "/slam_enhancement_trigger"; then
    echo "✅ Topic验证: 增强触发topic存在"
else
    echo "❌ Topic验证: 增强触发topic不存在"
fi

echo ""
echo "🔍 如何确认GPS软约束真正发挥作用："
echo ""
echo "1️⃣  观察日志输出:"
echo "   当GPS接近历史位置时，应该看到："
echo "   '🎯 GPS参考增强SLAM匹配触发!'"
echo ""
echo "2️⃣  监控topic活动:"
echo "   rostopic echo /slam_enhancement_trigger"
echo "   rostopic echo /gps_reference_guidance"
echo ""
echo "3️⃣  检查参数变化:"
echo "   rostopic echo /enhanced_slam_parameters"
echo ""
echo "4️⃣  观察置信度变化:"
echo "   rostopic echo /loop_confidence_score"
echo ""
echo "5️⃣  使用实时监控脚本:"
echo "   ./monitor_gps_soft_constraint.sh"
echo ""
echo "📊 预期效果指标:"
echo "   ✅ GPS接近时触发增强 (距离<50米)"
echo "   ✅ 根据GPS质量调整增强级别"
echo "   ✅ SLAM匹配参数动态优化"
echo "   ✅ 回环置信度提升 (>0.7为高置信度)"
echo "   ✅ 首尾偏差显著减少"
echo ""
echo "=========================================="
echo "验证完成!"
echo "=========================================="
