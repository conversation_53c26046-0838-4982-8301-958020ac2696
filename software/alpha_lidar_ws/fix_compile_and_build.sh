#!/bin/bash

# 修复编译错误并重新编译的脚本

echo "=========================================="
echo "🔧 修复编译错误并重新编译"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    echo "当前目录: $(pwd)"
    echo "请执行: cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash
echo "✓ ROS环境已设置"

echo ""
echo "步骤2: 清理之前的编译"
if [ -d "build" ]; then
    rm -rf build
    echo "✓ 清理build目录"
fi
if [ -d "devel" ]; then
    rm -rf devel  
    echo "✓ 清理devel目录"
fi

echo ""
echo "步骤3: 检查并安装依赖"

# 检查PCL
if ! pkg-config --exists pcl_common-1.10; then
    echo "安装PCL依赖..."
    sudo apt update
    sudo apt install -y libpcl-dev pcl-tools
fi

# 检查其他ROS依赖
echo "检查ROS依赖..."
sudo apt install -y \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-std-msgs \
    ros-noetic-tf \
    ros-noetic-tf2 \
    ros-noetic-tf2-ros \
    ros-noetic-eigen-conversions

echo "✓ 依赖检查完成"

echo ""
echo "步骤4: 编译系统"
echo "开始编译..."

# 使用单线程编译避免内存不足
catkin_make -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    echo ""
    echo "步骤5: 设置工作空间环境"
    source devel/setup.bash
    echo "✓ 工作空间环境已设置"
    
    echo ""
    echo "步骤6: 设置脚本权限"
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    echo "✓ 脚本权限已设置"
    
    echo ""
    echo "步骤7: 验证编译结果"
    echo "检查关键可执行文件..."
    
    executables=(
        "devel/lib/state_estimation/state_estimation_node"
        "devel/lib/state_estimation/intensity_preserving_pcd_saver"
        "devel/lib/state_estimation/enhanced_slam_loop_closure_integration"
        "devel/lib/state_estimation/adaptive_parameter_optimizer"
        "devel/lib/state_estimation/force_start_end_loop_matcher"
    )
    
    all_good=true
    for exe in "${executables[@]}"; do
        if [ -f "$exe" ]; then
            echo "✓ $exe"
        else
            echo "❌ $exe (缺失)"
            all_good=false
        fi
    done
    
    echo ""
    echo "检查Python脚本..."
    scripts=(
        "src/state_estimation/scripts/intelligent_start_end_detector.py"
        "src/state_estimation/scripts/simple_intensity_analyzer.py"
        "src/state_estimation/scripts/intensity_quality_monitor.py"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            echo "✓ $script"
        else
            echo "❌ $script (缺失)"
            all_good=false
        fi
    done
    
    echo ""
    if [ "$all_good" = true ]; then
        echo "🎉 所有组件编译成功!"
        echo ""
        echo "=========================================="
        echo "可用的启动选项:"
        echo "=========================================="
        echo ""
        echo "1. 解决首尾偏差问题:"
        echo "   ./solve_start_end_offset.sh"
        echo ""
        echo "2. 智能SLAM系统:"
        echo "   ./intelligent_slam_system.sh"
        echo ""
        echo "3. 标准优化系统:"
        echo "   ./quick_start.sh"
        echo ""
        echo "4. 手动启动:"
        echo "   roslaunch state_estimation optimized_slam_simple.launch"
        echo ""
        echo "监控命令:"
        echo "   ./monitor_offset_solution.sh"
        echo "   rostopic echo /force_match_score"
        echo "   rostopic echo /intelligent_detector_status"
        echo ""
    else
        echo "⚠️  部分组件缺失，但核心功能应该可用"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方法:"
    echo "1. 检查错误信息中的具体问题"
    echo "2. 确保所有依赖都已安装"
    echo "3. 尝试单独编译有问题的包:"
    echo "   catkin_make --only-pkg-with-deps state_estimation"
    echo "4. 如果内存不足，使用单线程编译:"
    echo "   catkin_make -j1"
    echo ""
    echo "如果仍有问题，请检查具体的编译错误信息"
    exit 1
fi

echo ""
echo "=========================================="
echo "编译完成! 系统已准备就绪"
echo "=========================================="
