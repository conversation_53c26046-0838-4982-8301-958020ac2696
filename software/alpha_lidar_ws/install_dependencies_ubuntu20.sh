#!/bin/bash
# Ubuntu 20.04 依赖安装脚本 - 使用国内源

set -e

echo "=========================================="
echo "Ubuntu 20.04 GPS回环检测系统依赖安装"
echo "使用国内镜像源加速下载"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_step "步骤1: 更新系统包管理器"
sudo apt update

print_step "步骤2: 安装系统依赖"
echo "安装ROS和编译相关依赖..."

# 安装ROS相关包
sudo apt-get install -y \
    ros-noetic-pcl-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-std-msgs \
    ros-noetic-visualization-msgs

# 安装编译工具
sudo apt-get install -y \
    build-essential \
    cmake \
    git \
    python3-pip \
    python3-dev \
    python3-setuptools \
    python3-wheel

print_status "系统依赖安装完成"

print_step "步骤3: 配置pip国内镜像源"
echo "配置pip使用清华大学镜像源..."

# 创建pip配置目录
mkdir -p ~/.pip

# 配置pip使用国内镜像
cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

print_status "pip镜像源配置完成"

print_step "步骤4: 安装Python依赖包"
echo "使用国内镜像源安装Python包..."

# 升级pip
python3 -m pip install --upgrade pip

# 安装ROS Python依赖
python3 -m pip install --user \
    rospkg \
    catkin_pkg \
    PyYAML \
    numpy \
    scipy \
    matplotlib

print_status "Python依赖包安装完成"

print_step "步骤5: 验证安装"
echo "验证关键包是否安装成功..."

# 验证Python包
python3 -c "import rospkg; print('rospkg: OK')" || print_error "rospkg安装失败"
python3 -c "import yaml; print('PyYAML: OK')" || print_error "PyYAML安装失败"
python3 -c "import numpy; print('numpy: OK')" || print_error "numpy安装失败"
python3 -c "import rospy; print('rospy: OK')" || print_error "rospy安装失败"

print_status "依赖验证完成"

print_step "步骤6: 设置环境变量"
echo "配置环境变量..."

# 添加环境变量到bashrc
if ! grep -q "PYTHONIOENCODING" ~/.bashrc; then
    echo "" >> ~/.bashrc
    echo "# GPS Loop Closure System Environment" >> ~/.bashrc
    echo "export PYTHONIOENCODING=utf-8" >> ~/.bashrc
    echo "export LC_ALL=C.UTF-8" >> ~/.bashrc
    echo "export LANG=C.UTF-8" >> ~/.bashrc
    echo "export PYTHONPATH=\$PYTHONPATH:~/.local/lib/python3.8/site-packages" >> ~/.bashrc
fi

# 立即设置当前会话的环境变量
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
export PYTHONPATH=$PYTHONPATH:~/.local/lib/python3.8/site-packages

print_status "环境变量配置完成"

print_step "步骤7: 检查ROS环境"
if [ -f "/opt/ros/noetic/setup.bash" ]; then
    source /opt/ros/noetic/setup.bash
    print_status "ROS Noetic环境已加载"
else
    print_error "ROS Noetic未安装，请先安装ROS"
    exit 1
fi

echo ""
echo "=========================================="
print_status "所有依赖安装完成！"
echo "=========================================="
echo ""
echo "接下来请运行："
echo "1. source ~/.bashrc  # 重新加载环境变量"
echo "2. cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
echo "3. ./build_and_run_ubuntu20.sh  # 编译和运行系统"
echo ""
print_status "安装脚本执行完成"
