#!/bin/bash

# 手动控制的SLAM启动脚本

echo "=========================================="
echo "🎯 手动控制SLAM启动"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/manual_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "📊 分析bag文件"
echo "=============="
rosbag info "$BAG_FILE"

echo ""
echo "🔧 设置SLAM参数"
echo "==============="

# 设置安全的SLAM参数
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 点云处理安全参数
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 100
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/wait_for_pointcloud true

# 处理参数
rosparam set /state_estimation_node/voxel_size 0.6
rosparam set /state_estimation_node/downsample_ratio 0.4
rosparam set /state_estimation_node/max_iterations 20

# 禁用复杂功能避免崩溃
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false

echo "✅ SLAM参数设置完成"

echo ""
echo "🚀 手动启动流程"
echo "==============="

echo ""
echo "📋 启动说明："
echo "1. 脚本将在新终端中启动bag播放（暂停状态）"
echo "2. 然后启动SLAM节点"
echo "3. 您需要手动在bag播放窗口按空格键开始播放"
echo ""

read -p "按回车键继续..." dummy

echo ""
echo "步骤1: 在新终端启动bag播放"

# 创建bag播放脚本
cat > /tmp/play_bag.sh << EOF
#!/bin/bash
source /opt/ros/noetic/setup.bash
echo "=========================================="
echo "🎬 Bag文件播放控制"
echo "=========================================="
echo "文件: $BAG_FILE"
echo ""
echo "控制说明:"
echo "  空格键 - 播放/暂停"
echo "  s键    - 单步播放"
echo "  Ctrl+C - 停止播放"
echo ""
echo "请等待SLAM节点启动完成后，按空格键开始播放"
echo "=========================================="
rosbag play "$BAG_FILE" --pause --clock --immediate
EOF

chmod +x /tmp/play_bag.sh

# 在新终端中启动bag播放
if command -v gnome-terminal &> /dev/null; then
    gnome-terminal -- bash -c "/tmp/play_bag.sh; exec bash"
elif command -v xterm &> /dev/null; then
    xterm -e "bash -c '/tmp/play_bag.sh; exec bash'" &
elif command -v konsole &> /dev/null; then
    konsole -e bash -c "/tmp/play_bag.sh; exec bash" &
else
    echo "⚠️  无法自动打开新终端，请手动运行:"
    echo "  bash /tmp/play_bag.sh"
    echo ""
    read -p "请在另一个终端运行上述命令，然后按回车继续..." dummy
fi

echo "✅ bag播放已在新终端启动"

echo ""
echo "步骤2: 等待topic就绪"
sleep 5

# 检查topic
echo "检查可用topic..."
TOPICS=$(rostopic list)

if echo "$TOPICS" | grep -q "/velodyne_points"; then
    echo "✅ 点云topic已就绪"
else
    echo "❌ 点云topic未找到，请确保bag文件正在播放"
    echo "可用topic:"
    echo "$TOPICS"
    echo ""
    read -p "按回车键继续，或Ctrl+C退出..." dummy
fi

echo ""
echo "步骤3: 启动SLAM节点"
rosrun state_estimation state_estimation_node &
SLAM_PID=$!
echo "✅ SLAM节点已启动 (PID: $SLAM_PID)"

echo ""
echo "步骤4: 等待SLAM初始化"
echo "等待SLAM节点初始化..."
sleep 8

# 检查SLAM是否还在运行
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM节点启动失败或崩溃"
    echo "请检查错误日志:"
    echo "  tail ~/.ros/log/latest/state_estimation_node-*.log"
    exit 1
fi

echo "✅ SLAM节点初始化完成"

echo ""
echo "步骤5: 开始数据处理"
echo ""
echo "🎬 现在请在bag播放窗口按空格键开始播放数据"
echo ""
echo "等待用户操作..."

# 等待用户开始播放
echo "等待检测到数据流..."
WAIT_COUNT=0
while [ $WAIT_COUNT -lt 30 ]; do
    if timeout 2 rostopic hz /velodyne_points 2>/dev/null | grep -q "average rate"; then
        echo "✅ 检测到点云数据流"
        break
    fi
    echo "等待数据流... ($((WAIT_COUNT+1))/30)"
    sleep 2
    WAIT_COUNT=$((WAIT_COUNT+1))
done

if [ $WAIT_COUNT -ge 30 ]; then
    echo "⚠️  未检测到数据流，请确保:"
    echo "1. bag播放窗口已按空格键开始播放"
    echo "2. bag文件包含/velodyne_points topic"
    echo ""
    echo "继续监控系统状态..."
fi

echo ""
echo "步骤6: 监控系统运行"
sleep 5

# 检查系统状态
if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM系统运行稳定"
    
    # 检查输出topic
    echo ""
    echo "检查输出topic:"
    if rostopic list | grep -q "/aft_mapped_to_init"; then
        echo "✅ 位姿输出正常"
    else
        echo "⚠️  位姿输出未找到"
    fi
    
    if rostopic list | grep -q "/cloud_registered"; then
        echo "✅ 点云输出正常"
    else
        echo "⚠️  点云输出未找到"
    fi
    
    echo ""
    echo "🎉 SLAM系统成功启动并运行!"
    echo ""
    echo "系统状态："
    echo "  SLAM节点: 运行中 (PID: $SLAM_PID)"
    echo "  输出目录: $OUTPUT_DIR"
    echo ""
    echo "监控命令："
    echo "  rostopic echo /aft_mapped_to_init"
    echo "  rostopic echo /cloud_registered"
    echo "  rostopic hz /velodyne_points"
    echo ""
    echo "控制说明："
    echo "  在bag播放窗口:"
    echo "    空格键 - 播放/暂停"
    echo "    s键    - 单步播放"
    echo "    Ctrl+C - 停止播放"
    echo ""
    echo "可选功能启用："
    echo "  启用GPS: rosparam set /state_estimation_node/enable_gps true"
    echo "  启用回环: rosparam set /state_estimation_node/enable_loop_closure true"
    echo ""
    echo "按 Ctrl+C 停止SLAM系统"
    
    # 创建停止函数
    cleanup() {
        echo ""
        echo "正在停止SLAM系统..."
        kill $SLAM_PID 2>/dev/null
        echo "✅ SLAM系统已停止"
        echo ""
        echo "请手动停止bag播放（在播放窗口按Ctrl+C）"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup SIGINT
    
    # 等待用户中断或进程结束
    while ps -p $SLAM_PID > /dev/null; do
        sleep 5
        
        # 定期检查数据流
        if timeout 2 rostopic hz /velodyne_points 2>/dev/null | grep -q "average rate"; then
            echo "📊 数据流正常 - $(date '+%H:%M:%S')"
        else
            echo "⚠️  数据流中断 - $(date '+%H:%M:%S')"
        fi
    done
    
    # 如果进程意外结束
    echo ""
    echo "❌ SLAM节点意外停止"
    echo "查看日志: tail ~/.ros/log/latest/state_estimation_node-*.log"
    
else
    echo "❌ SLAM系统启动失败"
    echo ""
    echo "建议的调试步骤："
    echo "1. 查看详细日志:"
    echo "   tail -f ~/.ros/log/latest/state_estimation_node-*.log"
    echo ""
    echo "2. 检查点云数据:"
    echo "   rostopic echo /velodyne_points -n 1"
    echo ""
    echo "3. 使用GDB调试:"
    echo "   gdb --args devel/lib/state_estimation/state_estimation_node"
    
    exit 1
fi
