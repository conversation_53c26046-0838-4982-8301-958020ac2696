#!/bin/bash

# 解决GPS平面约束与SLAM点云匹配冲突的专用脚本

echo "=========================================="
echo "🎯 解决GPS约束与SLAM匹配冲突"
echo "=========================================="
echo ""
echo "问题分析："
echo "❌ GPS平面约束强制修改SLAM位置"
echo "❌ 约束干扰ICP/NDT点云匹配算法"
echo "❌ 导致匹配质量下降和累积误差"
echo "✅ 解决方案: 智能动态约束控制"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_constraint_solution_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "选择GPS约束控制策略："
echo "1) 保守策略 (匹配质量优先，约束较少)"
echo "2) 平衡策略 (匹配质量与GPS约束平衡)"
echo "3) 激进策略 (GPS约束优先，但智能控制)"
echo "4) 完全禁用GPS约束 (纯SLAM模式)"
echo "5) 自定义策略"
echo ""

read -p "请选择策略 (1-5): " choice

case $choice in
    1)
        echo "使用保守策略..."
        ICP_DISABLE_THRESHOLD=0.4
        ICP_ENABLE_THRESHOLD=0.15
        VELOCITY_THRESHOLD=1.5
        CONSTRAINT_COOLDOWN=8.0
        GPS_PLANE_CONSTRAINT=false
        ;;
    2)
        echo "使用平衡策略..."
        ICP_DISABLE_THRESHOLD=0.5
        ICP_ENABLE_THRESHOLD=0.2
        VELOCITY_THRESHOLD=2.0
        CONSTRAINT_COOLDOWN=5.0
        GPS_PLANE_CONSTRAINT=true
        ;;
    3)
        echo "使用激进策略..."
        ICP_DISABLE_THRESHOLD=0.7
        ICP_ENABLE_THRESHOLD=0.3
        VELOCITY_THRESHOLD=3.0
        CONSTRAINT_COOLDOWN=3.0
        GPS_PLANE_CONSTRAINT=true
        ;;
    4)
        echo "完全禁用GPS约束..."
        ICP_DISABLE_THRESHOLD=1.0
        ICP_ENABLE_THRESHOLD=1.0
        VELOCITY_THRESHOLD=10.0
        CONSTRAINT_COOLDOWN=1.0
        GPS_PLANE_CONSTRAINT=false
        ;;
    5)
        echo "自定义策略："
        read -p "ICP匹配质量禁用阈值 (0.3-0.8): " ICP_DISABLE_THRESHOLD
        read -p "ICP匹配质量启用阈值 (0.1-0.3): " ICP_ENABLE_THRESHOLD
        read -p "速度阈值 (1.0-5.0 m/s): " VELOCITY_THRESHOLD
        read -p "约束冷却时间 (3.0-10.0 s): " CONSTRAINT_COOLDOWN
        read -p "启用GPS平面约束 (true/false): " GPS_PLANE_CONSTRAINT
        ;;
    *)
        echo "使用默认平衡策略..."
        ICP_DISABLE_THRESHOLD=0.5
        ICP_ENABLE_THRESHOLD=0.2
        VELOCITY_THRESHOLD=2.0
        CONSTRAINT_COOLDOWN=5.0
        GPS_PLANE_CONSTRAINT=true
        ;;
esac

echo ""
echo "约束控制参数："
echo "  ICP禁用阈值: ${ICP_DISABLE_THRESHOLD} (超过此值禁用约束)"
echo "  ICP启用阈值: ${ICP_ENABLE_THRESHOLD} (低于此值启用约束)"
echo "  速度阈值: ${VELOCITY_THRESHOLD} m/s (超过此值禁用约束)"
echo "  约束冷却时间: ${CONSTRAINT_COOLDOWN}秒"
echo "  GPS平面约束: ${GPS_PLANE_CONSTRAINT}"
echo ""

# 启动智能约束控制SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=true \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 8

# 设置GPS约束参数
echo "设置GPS约束参数..."
rosparam set /state_estimation_node/gps/enable_plane_constraint $GPS_PLANE_CONSTRAINT
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.1
rosparam set /state_estimation_node/gps/xy_correction_rate 0.02

# 设置智能约束控制器参数
echo "设置智能约束控制器参数..."
rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness $ICP_DISABLE_THRESHOLD
rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness $ICP_ENABLE_THRESHOLD
rosparam set /intelligent_gps_constraint_controller/velocity_threshold $VELOCITY_THRESHOLD
rosparam set /intelligent_gps_constraint_controller/constraint_cooldown $CONSTRAINT_COOLDOWN

# 设置智能检测参数
echo "设置智能检测参数..."
rosparam set /intelligent_start_end_detector/return_threshold 3.0
rosparam set /intelligent_start_end_detector/departure_threshold 25.0

echo ""
echo "=========================================="
echo "🚀 智能GPS约束控制系统已启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "🎯 根据ICP匹配质量动态控制GPS约束"
echo "⚡ 高速运动时自动禁用约束"
echo "🔄 回环检测时临时禁用约束"
echo "💎 完整保持强度值"
echo "📊 实时监控约束状态"
echo ""
echo "监控命令："
echo "  约束控制状态: rostopic echo /gps_constraint_controller_status"
echo "  约束开关状态: rostopic echo /gps_constraint_control"
echo "  ICP匹配质量:  rostopic echo /icp_fitness_score"
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo ""
echo "手动控制命令："
echo "  禁用GPS约束: rostopic pub /gps_constraint_control std_msgs/Bool \"data: false\""
echo "  启用GPS约束: rostopic pub /gps_constraint_control std_msgs/Bool \"data: true\""
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果："
echo "✅ ICP匹配质量差时自动禁用GPS约束"
echo "✅ 匹配质量好时重新启用约束"
echo "✅ 避免约束干扰点云匹配算法"
echo "✅ 保持SLAM轨迹的连续性和准确性"
echo ""
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 启动实时监控
echo ""
echo "启动实时约束状态监控..."
sleep 2

# 在后台监控约束状态变化
(
    echo "监控GPS约束状态变化..."
    rostopic echo /gps_constraint_control | while read line; do
        if [[ $line == *"data: true"* ]]; then
            echo "$(date '+%H:%M:%S') - ✅ GPS约束已启用"
        elif [[ $line == *"data: false"* ]]; then
            echo "$(date '+%H:%M:%S') - ❌ GPS约束已禁用"
        fi
    done
) &

# 监控ICP匹配质量
(
    echo "监控ICP匹配质量..."
    rostopic echo /icp_fitness_score | while read line; do
        if [[ $line == *"data:"* ]]; then
            fitness=$(echo $line | grep -o '[0-9.]*')
            if (( $(echo "$fitness > 0.5" | bc -l) )); then
                echo "$(date '+%H:%M:%S') - ⚠️  ICP匹配质量差: $fitness"
            elif (( $(echo "$fitness < 0.2" | bc -l) )); then
                echo "$(date '+%H:%M:%S') - ✅ ICP匹配质量好: $fitness"
            fi
        fi
    done
) &

# 等待用户中断
wait
