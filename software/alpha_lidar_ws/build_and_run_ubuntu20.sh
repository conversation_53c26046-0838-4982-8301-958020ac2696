#!/bin/bash
# Ubuntu 20.04 GPS回环检测系统 - 构建和运行脚本

set -e

echo "=========================================="
echo "Ubuntu 20.04 GPS回环检测系统"
echo "构建和运行脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "src/CMakeLists.txt" ]; then
    print_error "请在catkin工作空间根目录运行此脚本"
    print_error "期望目录: ~/alpha_lidar_GPS/software/alpha_lidar_ws/"
    exit 1
fi

print_step "步骤1: 检查和安装依赖"

# 检查是否已运行依赖安装脚本
if [ ! -f "~/.pip/pip.conf" ]; then
    print_warning "检测到依赖可能未安装，正在运行依赖安装脚本..."
    if [ -f "install_dependencies_ubuntu20.sh" ]; then
        chmod +x install_dependencies_ubuntu20.sh
        ./install_dependencies_ubuntu20.sh
    else
        print_error "依赖安装脚本不存在，请先运行 install_dependencies_ubuntu20.sh"
        exit 1
    fi
fi

print_step "步骤2: 设置环境变量"
echo "设置UTF-8编码和Python路径..."

# 设置环境变量
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
export PYTHONPATH=$PYTHONPATH:~/.local/lib/python3.8/site-packages

print_status "环境变量设置完成"

print_step "步骤3: 验证Python依赖"
echo "验证关键Python包..."

# 验证Python包
python3 -c "import rospkg; print('rospkg: OK')" || {
    print_error "rospkg未安装，正在安装..."
    python3 -m pip install --user rospkg
}

python3 -c "import yaml; print('PyYAML: OK')" || {
    print_error "PyYAML未安装，正在安装..."
    python3 -m pip install --user PyYAML
}

python3 -c "import numpy; print('numpy: OK')" || {
    print_error "numpy未安装，正在安装..."
    python3 -m pip install --user numpy
}

print_status "Python依赖验证完成"

print_step "步骤4: 设置ROS环境"
echo "加载ROS环境..."

# 检查并加载ROS环境
if [ -f "/opt/ros/noetic/setup.bash" ]; then
    source /opt/ros/noetic/setup.bash
    print_status "ROS Noetic环境已加载"
else
    print_error "ROS Noetic未找到"
    exit 1
fi

print_step "步骤5: 构建工作空间"
echo "构建catkin工作空间..."

# 清理构建（如果需要）
if [ "$1" = "clean" ]; then
    print_warning "清理之前的构建..."
    rm -rf build/ devel/
fi

# 构建工作空间
catkin_make -DCMAKE_BUILD_TYPE=Release -j$(nproc) || {
    print_error "构建失败！"
    print_error "常见解决方案："
    print_error "1. 安装缺失依赖: sudo apt-get install ros-noetic-pcl-ros ros-noetic-tf2-geometry-msgs"
    print_error "2. 清理构建: $0 clean"
    print_error "3. 检查CMakeLists.txt语法错误"
    exit 1
}

print_status "构建完成"

print_step "步骤6: 设置工作空间环境"
# 加载工作空间
if [ -f "devel/setup.bash" ]; then
    source devel/setup.bash
    print_status "工作空间环境已加载"
else
    print_error "工作空间构建失败 - devel/setup.bash未找到"
    exit 1
fi

print_step "步骤7: 验证安装"
echo "验证节点和启动文件..."

# 检查Python脚本
PYTHON_SCRIPT="src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py"
if [ -f "$PYTHON_SCRIPT" ]; then
    chmod +x "$PYTHON_SCRIPT"
    print_status "Python脚本权限已设置"
else
    print_error "Python脚本未找到: $PYTHON_SCRIPT"
    exit 1
fi

# 检查C++可执行文件
CPP_EXECUTABLE="devel/lib/state_estimation/enhanced_slam_loop_closure_integration"
if [ -f "$CPP_EXECUTABLE" ]; then
    print_status "C++可执行文件构建成功"
else
    print_warning "C++可执行文件未找到，但继续..."
fi

# 检查启动文件
LAUNCH_FILE="src/state_estimation/launch/mapping_robosense_with_enhanced_gps_loop.launch"
if [ -f "$LAUNCH_FILE" ]; then
    print_status "启动文件已找到"
else
    print_error "启动文件未找到: $LAUNCH_FILE"
    exit 1
fi

print_step "步骤8: 测试Python导入"
echo "测试Python模块导入..."

# 测试Python脚本导入
cd src/state_estimation/scripts/
python3 -c "
import sys
import os
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LC_ALL'] = 'C.UTF-8'
os.environ['LANG'] = 'C.UTF-8'

try:
    import rospy
    import rospkg
    import yaml
    import numpy
    print('所有Python模块导入成功')
except ImportError as e:
    print(f'导入错误: {e}')
    sys.exit(1)
" || {
    print_error "Python模块导入测试失败"
    exit 1
}

cd ../../..

print_status "Python模块测试通过"

print_step "步骤9: 系统就绪"
echo "=========================================="
print_status "Ubuntu 20.04 GPS回环检测系统就绪！"
echo "=========================================="

echo ""
echo "使用选项："
echo ""
echo "1. 运行增强系统（基础配置）："
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch"
echo ""
echo "2. 运行优化配置（适合GPS status=-1）："
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \\"
echo "     gps_quality_threshold:=-1 \\"
echo "     loop_closure_distance_threshold:=8.0 \\"
echo "     intermediate_loop_threshold:=10.0 \\"
echo "     min_trajectory_length:=30.0"
echo ""
echo "3. 调试模式（显示控制台输出）："
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch --screen"
echo ""
echo "4. 监控回环检测："
echo "   rostopic echo /force_loop_closure"
echo "   rostopic echo /intermediate_loop_detected"
echo "   rostopic echo /detected_loop_type"
echo ""

# 询问是否立即运行
echo ""
read -p "是否立即启动GPS回环检测系统？(y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "启动GPS回环检测系统..."
    echo ""
    print_status "使用优化配置启动（适合GPS status=-1）"
    print_status "按Ctrl+C停止系统"
    echo ""
    
    # 启动系统（使用优化配置）
    roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \
        gps_quality_threshold:=-1 \
        loop_closure_distance_threshold:=8.0 \
        intermediate_loop_threshold:=10.0 \
        min_trajectory_length:=30.0 \
        --screen
else
    print_status "系统就绪。您可以使用上述命令手动启动。"
fi

echo ""
print_status "脚本执行完成！"
