#!/bin/bash

# 点云匹配质量实时监控脚本
# 监控关键指标，帮助诊断匹配错乱问题

echo "=========================================="
echo "📊 点云匹配质量实时监控"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动roscore"
    exit 1
fi

echo "🔍 开始监控点云匹配质量..."
echo "按 Ctrl+C 停止监控"
echo ""

# 创建监控日志目录
LOG_DIR="/tmp/alpha_lidar_monitoring"
mkdir -p "$LOG_DIR"

# 日志文件
MATCH_LOG="$LOG_DIR/matching_quality_$(date +%Y%m%d_%H%M%S).log"
STATS_LOG="$LOG_DIR/system_stats_$(date +%Y%m%d_%H%M%S).log"

echo "📝 日志文件:"
echo "  匹配质量: $MATCH_LOG"
echo "  系统统计: $STATS_LOG"
echo ""

# 初始化计数器
frame_count=0
error_count=0
warning_count=0

# 监控函数
monitor_matching_quality() {
    echo "时间戳,帧数,点云频率,IMU频率,GPS频率,轨迹长度,匹配质量" > "$MATCH_LOG"
    echo "时间戳,CPU使用率,内存使用率,磁盘使用率" > "$STATS_LOG"
    
    while true; do
        timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        frame_count=$((frame_count + 1))
        
        # 获取话题频率
        lidar_hz=$(timeout 3 rostopic hz /velodyne_points 2>/dev/null | grep "average rate" | awk '{print $3}' || echo "0")
        imu_hz=$(timeout 3 rostopic hz /imu/data 2>/dev/null | grep "average rate" | awk '{print $3}' || echo "0")
        gps_hz=$(timeout 3 rostopic hz /rtk/gnss 2>/dev/null | grep "average rate" | awk '{print $3}' || echo "0")
        
        # 获取轨迹信息
        path_length=$(timeout 2 rostopic echo -n 1 /path 2>/dev/null | grep -c "position:" || echo "0")
        
        # 获取ICP适应度分数
        icp_fitness=$(timeout 2 rostopic echo -n 1 /icp_fitness_score 2>/dev/null | grep "data:" | awk '{print $2}' || echo "N/A")
        
        # 系统资源监控
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
        mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        disk_usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
        
        # 记录到日志
        echo "$timestamp,$frame_count,$lidar_hz,$imu_hz,$gps_hz,$path_length,$icp_fitness" >> "$MATCH_LOG"
        echo "$timestamp,$cpu_usage,$mem_usage,$disk_usage" >> "$STATS_LOG"
        
        # 实时显示
        printf "\r🔄 [%s] 帧:%d | LiDAR:%.1fHz | IMU:%.1fHz | GPS:%.1fHz | 轨迹:%d点 | ICP:%.3f | CPU:%.1f%% | 内存:%.1f%%   " \
               "$(date '+%H:%M:%S')" "$frame_count" "$lidar_hz" "$imu_hz" "$gps_hz" "$path_length" "$icp_fitness" "$cpu_usage" "$mem_usage"
        
        # 异常检测
        if (( $(echo "$lidar_hz < 5.0" | bc -l) )); then
            echo ""
            echo "⚠️  警告: LiDAR频率过低 ($lidar_hz Hz)"
            warning_count=$((warning_count + 1))
        fi
        
        if (( $(echo "$icp_fitness > 0.5" | bc -l) )) && [ "$icp_fitness" != "N/A" ]; then
            echo ""
            echo "❌ 错误: ICP匹配质量差 ($icp_fitness)"
            error_count=$((error_count + 1))
        fi
        
        if (( $(echo "$cpu_usage > 90.0" | bc -l) )); then
            echo ""
            echo "⚠️  警告: CPU使用率过高 ($cpu_usage%)"
            warning_count=$((warning_count + 1))
        fi
        
        sleep 2
    done
}

# 信号处理函数
cleanup() {
    echo ""
    echo ""
    echo "=========================================="
    echo "📊 监控总结报告"
    echo "=========================================="
    echo "监控时长: $frame_count 个周期"
    echo "警告次数: $warning_count"
    echo "错误次数: $error_count"
    echo ""
    echo "📁 日志文件已保存:"
    echo "  $MATCH_LOG"
    echo "  $STATS_LOG"
    echo ""
    
    # 生成简单的分析报告
    if [ -f "$MATCH_LOG" ] && [ $(wc -l < "$MATCH_LOG") -gt 1 ]; then
        echo "📈 数据分析:"
        avg_lidar_hz=$(tail -n +2 "$MATCH_LOG" | awk -F',' '{sum+=$3; count++} END {if(count>0) printf "%.2f", sum/count; else print "0"}')
        avg_imu_hz=$(tail -n +2 "$MATCH_LOG" | awk -F',' '{sum+=$4; count++} END {if(count>0) printf "%.2f", sum/count; else print "0"}')
        max_path_length=$(tail -n +2 "$MATCH_LOG" | awk -F',' '{if($6>max) max=$6} END {print max+0}')
        
        echo "  平均LiDAR频率: ${avg_lidar_hz} Hz"
        echo "  平均IMU频率: ${avg_imu_hz} Hz"
        echo "  最大轨迹长度: ${max_path_length} 点"
        
        if (( $(echo "$avg_lidar_hz < 8.0" | bc -l) )); then
            echo ""
            echo "💡 建议:"
            echo "  - LiDAR频率偏低，考虑降低数据播放速度"
            echo "  - 检查系统资源是否充足"
        fi
        
        if [ "$error_count" -gt 5 ]; then
            echo ""
            echo "🔧 匹配错乱问题建议:"
            echo "  - 运行修复脚本: ./fix_point_cloud_matching.sh"
            echo "  - 使用优化配置: rs16_rotation_v2_optimized.yaml"
            echo "  - 降低数据播放速度: rosbag play data.bag -r 0.3"
        fi
    fi
    
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 开始监控
monitor_matching_quality
