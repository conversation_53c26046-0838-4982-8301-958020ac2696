#!/bin/bash

# 最终编译解决方案 - 逐步尝试不同的修复方法

echo "=========================================="
echo "🎯 最终编译解决方案"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "当前编译错误分析:"
echo "1. pub_icp_fitness 作用域问题"
echo "2. Lambda函数不兼容ROS订阅器"
echo "3. GPS约束控制代码语法问题"
echo ""

echo "解决方案策略:"
echo "方案1: 彻底清理有问题的代码"
echo "方案2: 创建最小化版本"
echo "方案3: 完全禁用GPS功能"
echo ""

read -p "选择解决方案 (1-3) 或按Enter自动尝试: " choice

case $choice in
    1)
        echo "执行方案1: 彻底清理有问题的代码"
        ./complete_fix_compile.sh
        ;;
    2)
        echo "执行方案2: 创建最小化版本"
        ./create_minimal_voxelmapping.sh
        ;;
    3)
        echo "执行方案3: 完全禁用GPS功能"
        echo "备份原文件..."
        cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup
        
        echo "禁用GPS功能..."
        sed -i 's/^#define USE_GPS_INTEGRATION/\/\/ #define USE_GPS_INTEGRATION/' src/state_estimation/src/voxelMapping.cpp
        sed -i 's/^#ifdef USE_GPS_INTEGRATION/#if 0/' src/state_estimation/src/voxelMapping.cpp
        
        echo "编译..."
        source /opt/ros/noetic/setup.bash
        rm -rf build devel
        catkin_make --only-pkg-with-deps state_estimation -j1
        ;;
    *)
        echo "自动尝试所有解决方案..."
        
        # 方案1: 彻底清理
        echo ""
        echo "=== 尝试方案1: 彻底清理 ==="
        if [ -f "./complete_fix_compile.sh" ]; then
            chmod +x complete_fix_compile.sh
            ./complete_fix_compile.sh
            if [ $? -eq 0 ]; then
                echo "✅ 方案1成功!"
                exit 0
            fi
        fi
        
        # 方案2: 最小化版本
        echo ""
        echo "=== 尝试方案2: 最小化版本 ==="
        if [ -f "./create_minimal_voxelmapping.sh" ]; then
            chmod +x create_minimal_voxelmapping.sh
            ./create_minimal_voxelmapping.sh
            if [ $? -eq 0 ]; then
                echo "✅ 方案2成功!"
                exit 0
            fi
        fi
        
        # 方案3: 完全禁用GPS
        echo ""
        echo "=== 尝试方案3: 完全禁用GPS ==="
        
        # 备份原文件
        if [ ! -f "src/state_estimation/src/voxelMapping.cpp.original" ]; then
            cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.original
        fi
        
        # 禁用GPS功能
        sed -i 's/^#define USE_GPS_INTEGRATION/\/\/ #define USE_GPS_INTEGRATION  \/\/ DISABLED/' src/state_estimation/src/voxelMapping.cpp
        sed -i 's/^#ifdef USE_GPS_INTEGRATION/#if 0  \/\/ GPS功能已完全禁用/' src/state_estimation/src/voxelMapping.cpp
        
        # 移除所有有问题的代码
        sed -i '/pub_icp_fitness/d' src/state_estimation/src/voxelMapping.cpp
        sed -i '/gps_constraint_control_callback/,/^}/d' src/state_estimation/src/voxelMapping.cpp
        sed -i '/sub_constraint_control/d' src/state_estimation/src/voxelMapping.cpp
        
        # 编译
        source /opt/ros/noetic/setup.bash
        rm -rf build devel
        catkin_make --only-pkg-with-deps state_estimation -j1
        
        if [ $? -eq 0 ]; then
            echo "✅ 方案3成功!"
            
            source devel/setup.bash
            chmod +x *.sh
            chmod +x src/state_estimation/scripts/*.py
            
            echo ""
            echo "🎉 编译成功! (GPS功能已禁用)"
            echo ""
            echo "可用功能:"
            echo "✅ 基础SLAM定位和建图"
            echo "✅ 点云处理和配准"
            echo "✅ 轨迹生成"
            echo "✅ 强度值保持"
            echo "❌ GPS集成功能 (已禁用)"
            echo ""
            echo "启动选项:"
            echo "1. 基础SLAM系统:"
            echo "   roslaunch state_estimation mapping_robosense.launch"
            echo ""
            echo "2. 手动启动:"
            echo "   rosrun state_estimation state_estimation_node"
            echo ""
            echo "3. 简化启动:"
            echo "   ./start_basic_slam.sh"
            echo ""
            echo "如需恢复GPS功能:"
            echo "   cp src/state_estimation/src/voxelMapping.cpp.original src/state_estimation/src/voxelMapping.cpp"
            
        else
            echo "❌ 所有方案都失败了!"
            echo ""
            echo "恢复原始文件..."
            if [ -f "src/state_estimation/src/voxelMapping.cpp.original" ]; then
                cp src/state_estimation/src/voxelMapping.cpp.original src/state_estimation/src/voxelMapping.cpp
            fi
            
            echo ""
            echo "最终建议:"
            echo "1. 检查系统环境:"
            echo "   - Ubuntu 20.04 LTS"
            echo "   - ROS Noetic完整安装"
            echo "   - PCL 1.10"
            echo "   - GCC支持C++14"
            echo ""
            echo "2. 安装命令:"
            echo "   sudo apt update"
            echo "   sudo apt install ros-noetic-desktop-full"
            echo "   sudo apt install libpcl-dev pcl-tools"
            echo ""
            echo "3. 或者考虑使用Docker环境"
            echo ""
            echo "4. 查看具体错误信息进行手动修复"
            
            exit 1
        fi
        ;;
esac

echo ""
echo "=========================================="
echo "解决方案执行完成!"
echo "=========================================="
