#!/bin/bash

# 完整的编译和运行脚本

echo "=========================================="
echo "优化SLAM系统 - 编译和运行"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    echo "当前目录: $(pwd)"
    echo "请执行: cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash
echo "✓ ROS环境已设置"

echo ""
echo "步骤2: 清理之前的编译"
if [ -d "build" ]; then
    rm -rf build
    echo "✓ 清理build目录"
fi
if [ -d "devel" ]; then
    rm -rf devel  
    echo "✓ 清理devel目录"
fi

echo ""
echo "步骤3: 安装依赖"
rosdep update
rosdep install --from-paths src --ignore-src -r -y
echo "✓ 依赖检查完成"

echo ""
echo "步骤4: 编译系统"
catkin_make

if [ $? -eq 0 ]; then
    echo "✓ 编译成功!"
else
    echo "✗ 编译失败!"
    echo ""
    echo "常见解决方法:"
    echo "1. 检查ROS环境: source /opt/ros/noetic/setup.bash"
    echo "2. 安装缺失依赖: sudo apt install ros-noetic-pcl-ros ros-noetic-pcl-conversions"
    echo "3. 清理重新编译: rm -rf build devel && catkin_make"
    exit 1
fi

echo ""
echo "步骤5: 设置工作空间环境"
source devel/setup.bash
echo "✓ 工作空间环境已设置"

echo ""
echo "步骤6: 设置脚本权限"
chmod +x *.sh
chmod +x src/state_estimation/scripts/*.py
echo "✓ 脚本权限已设置"

echo ""
echo "=========================================="
echo "编译完成! 可用的启动选项:"
echo "=========================================="
echo ""
echo "🧠 1. 智能首尾回环检测系统 (最新推荐):"
echo "   ./start_intelligent_slam.sh"
echo ""
echo "2. 标准优化系统:"
echo "   ./quick_start.sh"
echo ""
echo "3. 超宽松首尾回环检测:"
echo "   ./ultra_loose_start_end.sh"
echo ""
echo "4. 手动启动:"
echo "   roslaunch state_estimation intelligent_slam_system.launch"
echo ""
echo "5. 实时参数调节:"
echo "   ./adjust_loop_params.sh"
echo ""
echo "🔍 监控工具:"
echo "   ./monitor_intelligent_detection.sh"
echo "   ./diagnose_loop_detection.sh"
echo ""
echo "监控命令:"
echo "   rostopic echo /enhanced_gps_loop_closure_status"
echo "   rostopic echo /force_loop_closure"
echo ""
echo "=========================================="
