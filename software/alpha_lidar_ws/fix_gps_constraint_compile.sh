#!/bin/bash

# 修复GPS约束相关编译错误的脚本

echo "=========================================="
echo "🔧 修复GPS约束编译错误"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    echo "当前目录: $(pwd)"
    echo "请执行: cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash
echo "✓ ROS环境已设置"

echo ""
echo "步骤2: 清理之前的编译"
if [ -d "build" ]; then
    rm -rf build
    echo "✓ 清理build目录"
fi
if [ -d "devel" ]; then
    rm -rf devel  
    echo "✓ 清理devel目录"
fi

echo ""
echo "步骤3: 检查并修复代码问题"

# 检查是否存在编译问题的文件
if [ -f "src/state_estimation/src/voxelMapping.cpp" ]; then
    echo "检查voxelMapping.cpp..."
    
    # 检查是否包含必要的头文件
    if ! grep -q "#include <std_msgs/Bool.h>" src/state_estimation/src/voxelMapping.cpp; then
        echo "⚠️  缺少std_msgs/Bool.h头文件"
    else
        echo "✓ std_msgs/Bool.h头文件存在"
    fi
    
    if ! grep -q "#include <std_msgs/Float64.h>" src/state_estimation/src/voxelMapping.cpp; then
        echo "⚠️  缺少std_msgs/Float64.h头文件"
    else
        echo "✓ std_msgs/Float64.h头文件存在"
    fi
    
    # 检查全局变量声明
    if ! grep -q "ros::Publisher pub_icp_fitness;" src/state_estimation/src/voxelMapping.cpp; then
        echo "⚠️  缺少pub_icp_fitness全局声明"
    else
        echo "✓ pub_icp_fitness全局声明存在"
    fi
fi

echo ""
echo "步骤4: 安装依赖"
sudo apt update
sudo apt install -y \
    ros-noetic-std-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-tf \
    ros-noetic-tf2-ros \
    libpcl-dev \
    pcl-tools

echo "✓ 依赖安装完成"

echo ""
echo "步骤5: 尝试编译"
echo "使用单线程编译避免内存问题..."

# 首先尝试只编译state_estimation包
catkin_make --only-pkg-with-deps state_estimation -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ state_estimation包编译成功!"
    
    # 尝试编译整个工作空间
    echo "编译整个工作空间..."
    catkin_make -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 完整编译成功!"
        
        echo ""
        echo "步骤6: 设置环境和权限"
        source devel/setup.bash
        chmod +x *.sh
        chmod +x src/state_estimation/scripts/*.py
        echo "✓ 环境和权限设置完成"
        
        echo ""
        echo "步骤7: 验证关键文件"
        echo "检查可执行文件..."
        
        if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
            echo "✓ state_estimation_node"
        else
            echo "❌ state_estimation_node (缺失)"
        fi
        
        if [ -f "src/state_estimation/scripts/intelligent_gps_constraint_controller.py" ]; then
            echo "✓ intelligent_gps_constraint_controller.py"
        else
            echo "❌ intelligent_gps_constraint_controller.py (缺失)"
        fi
        
        echo ""
        echo "🎉 编译修复完成!"
        echo ""
        echo "可用的启动选项:"
        echo "1. 解决GPS约束冲突:"
        echo "   ./solve_gps_constraint_conflict.sh"
        echo ""
        echo "2. 解决首尾偏差:"
        echo "   ./solve_start_end_offset.sh"
        echo ""
        echo "3. 标准启动:"
        echo "   roslaunch state_estimation optimized_slam_simple.launch"
        
    else
        echo ""
        echo "⚠️  完整编译失败，但核心包编译成功"
        echo "可以尝试使用核心功能"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方法:"
    echo "1. 检查错误信息中的具体问题"
    echo "2. 确保所有ROS依赖都已安装:"
    echo "   sudo apt install ros-noetic-desktop-full"
    echo "3. 检查PCL版本:"
    echo "   pkg-config --modversion pcl_common-1.10"
    echo "4. 如果是lambda函数问题，检查C++编译器版本:"
    echo "   g++ --version"
    echo ""
    echo "如果仍有问题，可以尝试禁用GPS约束功能:"
    echo "在voxelMapping.cpp中注释掉GPS相关代码"
    exit 1
fi

echo ""
echo "=========================================="
echo "编译修复完成! 系统已准备就绪"
echo "=========================================="
