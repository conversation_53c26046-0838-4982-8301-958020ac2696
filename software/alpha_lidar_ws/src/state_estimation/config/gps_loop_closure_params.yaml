# GPS回环检测系统参数配置文件
# 可以根据实际场景调整这些参数

# ========================================
# GPS回环检测优化器参数
# ========================================
enhanced_gps_loop_closure_optimizer:
  # 基础回环检测参数
  gps_topic: "/rtk/gnss"                    # GPS话题名称
  loop_closure_distance_threshold: 30.0     # 起点-终点回环距离阈值(米) - 大幅增大以提高检测率
  min_trajectory_length: 50.0               # 最小轨迹长度(米) - 确保有足够轨迹
  gps_quality_threshold: -1                 # GPS质量阈值(-1接受所有质量)
  check_interval: 1.0                       # 检查间隔(秒)
  
  # 中间区域回环检测参数
  intermediate_loop_threshold: 10.0         # 中间回环距离阈值(米)
  min_loop_separation: 30.0                 # 最小回环分离距离(米)
  trajectory_window_size: 100               # 轨迹分析窗口大小
  
  # 重复访问回环检测参数
  revisit_threshold: 12.0                   # 重访距离阈值(米)
  revisit_time_threshold: 10.0              # 重访时间间隔阈值(秒)
  
  # 系统性能参数
  max_trajectory_points: 2000               # 最大轨迹点数
  loop_detection_cooldown: 5.0              # 回环检测冷却时间(秒)

# ========================================
# SLAM回环检测集成模块参数
# ========================================
enhanced_slam_loop_closure_integration:
  # 基础搜索参数
  force_search_radius: 35.0                 # 基础搜索半径(米) - 大幅增大
  max_search_candidates: 15                 # 最大搜索候选数 - 增加候选数
  min_keyframes_for_loop: 50                # 最小关键帧数
  
  # 不同回环类型的搜索半径
  intermediate_search_radius: 20.0          # 中间回环搜索半径(米)
  revisit_search_radius: 15.0               # 重访回环搜索半径(米)
  
  # 不同回环类型的匹配分数阈值(越小越严格)
  start_end_score_threshold: 0.70           # 起点-终点匹配阈值 - 大幅放宽以提高检测率
  intermediate_score_threshold: 0.40        # 中间回环匹配阈值
  revisit_score_threshold: 0.35             # 重访回环匹配阈值
  
  # 点云处理参数
  voxel_leaf_size: 0.15                     # 体素滤波叶子大小(米)
  sliding_window_size: 100                  # 滑动窗口大小
  temporal_consistency_weight: 0.2          # 时间一致性权重
  
  # NDT配准参数
  ndt_transformation_epsilon: 0.01          # NDT变换收敛阈值
  ndt_step_size: 0.1                        # NDT步长
  ndt_resolution: 1.0                       # NDT分辨率
  ndt_max_iterations: 35                    # NDT最大迭代次数
  
  # ICP配准参数
  icp_max_iterations: 50                    # ICP最大迭代次数
  icp_transformation_epsilon: 1e-6          # ICP变换收敛阈值
  icp_euclidean_fitness_epsilon: 1e-6       # ICP欧几里得适应度阈值

# ========================================
# 场景特定配置预设
# ========================================

# 预设1: GPS质量差的场景(如您的status=-1情况) - 优化首尾回环检测
poor_gps_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: -1               # 接受NO_FIX状态
    loop_closure_distance_threshold: 40.0   # 进一步放宽首尾距离要求
    intermediate_loop_threshold: 12.0       # 放宽中间回环要求
    min_trajectory_length: 50.0             # 确保有足够轨迹长度
    loop_detection_cooldown: 1.0            # 快速检测

  enhanced_slam_loop_closure_integration:
    force_search_radius: 50.0               # 进一步增大搜索范围
    intermediate_search_radius: 25.0        # 增大中间回环搜索
    start_end_score_threshold: 0.8          # 进一步放宽首尾匹配要求
    intermediate_score_threshold: 0.50      # 放宽中间回环匹配
    voxel_leaf_size: 0.3                    # 增大体素大小减少计算

# 预设2: GPS质量好的场景
good_gps_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: 0                # 只接受RTK_FIXED
    loop_closure_distance_threshold: 5.0    # 严格距离要求
    intermediate_loop_threshold: 6.0        # 严格中间回环要求
    min_trajectory_length: 50.0             # 较高最小轨迹长度
    
  enhanced_slam_loop_closure_integration:
    force_search_radius: 10.0               # 较小搜索范围
    intermediate_search_radius: 12.0        # 较小中间回环搜索
    start_end_score_threshold: 0.20         # 严格匹配要求
    intermediate_score_threshold: 0.25      # 严格中间回环匹配
    voxel_leaf_size: 0.1                    # 较小体素大小提高精度

# 预设3: 高性能计算场景
high_performance_preset:
  enhanced_gps_loop_closure_optimizer:
    check_interval: 0.5                     # 更频繁检查
    trajectory_window_size: 200             # 更大分析窗口
    
  enhanced_slam_loop_closure_integration:
    max_search_candidates: 20               # 更多候选
    ndt_max_iterations: 50                  # 更多NDT迭代
    icp_max_iterations: 80                  # 更多ICP迭代
    voxel_leaf_size: 0.05                   # 更小体素大小

# 预设4: 低性能计算场景
low_performance_preset:
  enhanced_gps_loop_closure_optimizer:
    check_interval: 2.0                     # 较少检查频率
    trajectory_window_size: 50              # 较小分析窗口
    max_trajectory_points: 1000             # 限制轨迹点数
    
  enhanced_slam_loop_closure_integration:
    max_search_candidates: 5                # 较少候选
    ndt_max_iterations: 20                  # 较少NDT迭代
    icp_max_iterations: 30                  # 较少ICP迭代
    voxel_leaf_size: 0.3                    # 较大体素大小

# ========================================
# 调试和监控参数
# ========================================
debug_settings:
  enable_detailed_logging: true             # 启用详细日志
  log_gps_data: false                       # 记录GPS数据
  log_loop_candidates: true                 # 记录回环候选
  log_matching_scores: true                 # 记录匹配分数
  publish_debug_markers: true               # 发布调试标记
  save_loop_closure_data: false             # 保存回环数据

# ========================================
# 可视化参数
# ========================================
visualization:
  publish_trajectory_markers: true          # 发布轨迹标记
  publish_loop_markers: true                # 发布回环标记
  marker_scale: 1.0                         # 标记缩放
  trajectory_color: [0.0, 1.0, 0.0, 0.8]   # 轨迹颜色(RGBA)
  loop_color: [1.0, 0.0, 0.0, 0.8]         # 回环颜色(RGBA)
  marker_lifetime: 30.0                     # 标记生存时间(秒)

# ========================================
# 专门优化首尾回环检测的预设
# ========================================
start_end_optimized_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: -1               # 接受所有GPS状态
    loop_closure_distance_threshold: 30.0   # 非常宽松的首尾距离阈值
    intermediate_loop_threshold: 8.0        # 保持中间回环正常
    revisit_threshold: 10.0                 # 保持重访回环正常
    min_trajectory_length: 100.0            # 确保轨迹足够长
    loop_detection_cooldown: 1.0            # 快速检测

  enhanced_slam_loop_closure_integration:
    force_search_radius: 40.0               # 超大搜索范围
    intermediate_search_radius: 20.0        # 中间回环正常范围
    revisit_search_radius: 15.0             # 重访回环正常范围
    start_end_score_threshold: 0.75         # 非常宽松的首尾匹配阈值
    intermediate_score_threshold: 0.40      # 中间回环正常阈值
    revisit_score_threshold: 0.35           # 重访回环正常阈值
    voxel_leaf_size: 0.3                    # 大体素减少计算量
    max_search_candidates: 15               # 增加候选数量

# ========================================
# 超宽松首尾回环预设（针对大偏移情况）
# ========================================
ultra_loose_start_end_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: -1               # 接受所有GPS状态
    loop_closure_distance_threshold: 50.0   # 超大首尾距离阈值
    intermediate_loop_threshold: 8.0        # 保持中间回环正常
    revisit_threshold: 10.0                 # 保持重访回环正常
    min_trajectory_length: 100.0            # 确保轨迹足够长
    loop_detection_cooldown: 0.5            # 非常快速检测

  enhanced_slam_loop_closure_integration:
    force_search_radius: 60.0               # 超超大搜索范围
    intermediate_search_radius: 20.0        # 中间回环正常范围
    revisit_search_radius: 15.0             # 重访回环正常范围
    start_end_score_threshold: 0.9          # 超超宽松的首尾匹配阈值
    intermediate_score_threshold: 0.40      # 中间回环正常阈值
    revisit_score_threshold: 0.35           # 重访回环正常阈值
    voxel_leaf_size: 0.4                    # 超大体素减少计算量
    max_search_candidates: 20               # 大量候选数量

# ========================================
# RTK GPS优化预设（针对GPS质量好但仍有偏差的情况）
# ========================================
rtk_gps_optimized_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: 0                # 只接受RTK固定解
    loop_closure_distance_threshold: 15.0   # 适中的首尾距离阈值
    intermediate_loop_threshold: 8.0        # 保持中间回环正常
    revisit_threshold: 10.0                 # 保持重访回环正常
    min_trajectory_length: 30.0             # 较短的最小轨迹长度
    loop_detection_cooldown: 1.0            # 快速检测

  enhanced_slam_loop_closure_integration:
    force_search_radius: 35.0               # 适中的搜索范围
    intermediate_search_radius: 20.0        # 中间回环正常范围
    revisit_search_radius: 15.0             # 重访回环正常范围
    start_end_score_threshold: 0.7          # 适中的首尾匹配阈值
    intermediate_score_threshold: 0.40      # 中间回环正常阈值
    revisit_score_threshold: 0.35           # 重访回环正常阈值
    voxel_leaf_size: 0.1                    # 标准体素大小
    max_search_candidates: 12               # 适中的候选数量

# ========================================
# 超宽松首尾回环预设（针对大偏移情况）
# ========================================
ultra_loose_start_end_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: -1               # 接受所有GPS状态
    loop_closure_distance_threshold: 50.0   # 超大首尾距离阈值
    intermediate_loop_threshold: 8.0        # 保持中间回环正常
    revisit_threshold: 10.0                 # 保持重访回环正常
    min_trajectory_length: 100.0            # 确保轨迹足够长
    loop_detection_cooldown: 0.5            # 非常快速检测

  enhanced_slam_loop_closure_integration:
    force_search_radius: 60.0               # 超超大搜索范围
    intermediate_search_radius: 20.0        # 中间回环正常范围
    revisit_search_radius: 15.0             # 重访回环正常范围
    start_end_score_threshold: 0.9          # 超超宽松的首尾匹配阈值
    intermediate_score_threshold: 0.40      # 中间回环正常阈值
    revisit_score_threshold: 0.35           # 重访回环正常阈值
    voxel_leaf_size: 0.4                    # 超大体素减少计算量
    max_search_candidates: 20               # 大量候选数量

# ========================================
# 极端大偏移修复预设（针对几十米偏移）
# ========================================
extreme_offset_fix_preset:
  enhanced_gps_loop_closure_optimizer:
    gps_quality_threshold: -1               # 接受所有GPS状态包括NO_FIX
    loop_closure_distance_threshold: 150.0  # 极大首尾距离阈值
    intermediate_loop_threshold: 12.0       # 放宽中间回环
    revisit_threshold: 15.0                 # 放宽重访回环
    min_trajectory_length: 10.0             # 极低轨迹长度要求
    loop_detection_cooldown: 0.1            # 极快检测频率
    check_interval: 0.1                     # 极快检查间隔

  enhanced_slam_loop_closure_integration:
    force_search_radius: 250.0              # 极大搜索范围
    intermediate_search_radius: 30.0        # 大中间回环搜索
    revisit_search_radius: 25.0             # 大重访回环搜索
    start_end_score_threshold: 5.0          # 极度宽松的首尾匹配阈值
    intermediate_score_threshold: 0.60      # 放宽中间回环匹配
    revisit_score_threshold: 0.50           # 放宽重访回环匹配
    voxel_leaf_size: 1.5                    # 极大体素减少计算量
    max_search_candidates: 150              # 极大候选数量

    # NDT参数优化
    ndt_transformation_epsilon: 0.1         # 放宽NDT收敛要求
    ndt_step_size: 0.2                      # 增大NDT步长
    ndt_resolution: 2.0                     # 增大NDT分辨率
    ndt_max_iterations: 100                 # 增加NDT迭代次数

    # ICP参数优化
    icp_max_iterations: 200                 # 增加ICP迭代次数
    icp_transformation_epsilon: 1e-4        # 放宽ICP收敛要求
    icp_euclidean_fitness_epsilon: 1e-4     # 放宽ICP适应度要求
