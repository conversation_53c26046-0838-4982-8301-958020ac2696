common:
    lid_topic:  "/velodyne_points"
    imu_topic:  "/imu/data"
    encoder_topic:  "/imu/encoder"
    gps_topic:  "/rtk/gnss"
    time_sync_en: false

# GPS integration parameters - 优化后的GPS参数
gps:
    enable_correction: true
    enable_loop_closure: true
    enable_plane_constraint: false      # 禁用GPS平面约束，避免点云错乱
    height_correction_threshold: 0.5    # 增加到0.5米，减少频繁校正
    correction_rate: 0.05               # 降低到5%，更温和的校正
    loop_closure_distance: 8.0          # 降低到8米，更严格的回环检测
    loop_closure_min_distance: 25.0     # 增加到25米，确保足够轨迹长度

    # GPS触发的ICP回环检测参数 - 优化匹配精度
    enable_icp_loop_closure: true
    icp_trigger_distance: 15.0          # 降低到15米，减少误触发
    keyframe_skip: 8                    # 降低到8，增加关键帧密度
    icp_fitness_threshold: 0.2          # 降低到0.2，提高匹配要求
    icp_max_correspondence_distance: 0.8 # 降低到0.8，提高匹配精度

    # GPS约束模式 - 完全禁用直接约束
    constraint_mode: 0
    plane_constraint_weight: 0.0
    xy_correction_threshold: 999.0
    xy_correction_rate: 0.0
    constraint_window_size: 1

    # GPS参考增强参数
    enable_reference_enhancement: true
    reference_radius: 80.0              # 降低到80米
    enhancement_radius: 80.0
    start_point_return_threshold: 80.0

# PCD save parameters
pcd_save:
    pcd_save_en: true
    interval: -1
    directory: "/home/<USER>/slam_share/aLidar/temp"

# 预处理参数 - 优化点云质量
preprocess:
    lidar_type: 2
    scan_line: 16
    scan_rate: 10
    blind: 0.5                          # 从0.8降低到0.5，保留更多近距离点
    point_filter_num: 1                 # 保持最小滤波

# 核心SLAM参数 - 大幅优化匹配精度
mapping:
    down_sample_size: 0.15              # 从0.2降低到0.15，保留更多特征
    max_iteration: 8                    # 从4增加到8，提高收敛性
    voxel_size: 0.2                     # 从0.25降低到0.2，提高精度
    max_layer: 3                        # 从2增加到3，增加地图层次
    layer_point_size: [ 8, 8, 8, 8, 8 ] # 从[5,5,5,5,5]增加，提高匹配点数
    plannar_threshold: 0.005            # 从0.01降低到0.005，提高平面检测精度
    max_points_size: 1500               # 从1000增加到1500
    max_cov_points_size: 1500           # 从1000增加到1500
    init_gravity_with_pose: true

    fov_degree: 360
    det_range: 80.0                     # 从100.0降低到80.0，专注近距离匹配
    extrinsic_est_en: true
    extrinsic_T: [ 0.03, 0.01, -0.05 ]
    extrinsic_R: [ 1, 0, 0,
                   0, -1, 0,
                   0, 0, -1 ]

    encoder_fusion_en: true
    extrinsic_T_encoder_lidar: [-0.0042, 0.0046, 0.09994]
    extrinsic_R_encoder_lidar: [-0.0002004, 3.04e-05, 1.0,
                                0.0040013, 0.999992, -2.96e-05,
                                -0.999992, 0.0040013, -0.0002005]
    encoder_offset_deg: 120

# 噪声模型 - 优化传感器信任度
noise_model:
    ranging_cov: 0.02                   # 从0.04降低到0.02，提高距离测量信任度
    angle_cov: 0.05                     # 从0.1降低到0.05，提高角度测量信任度
    acc_cov: 0.05                       # 从0.1降低到0.05，提高IMU加速度信任度
    gyr_cov: 0.005                      # 从0.01降低到0.005，提高陀螺仪信任度
    b_acc_cov: 0.000001                 # 进一步降低偏置噪声
    b_gyr_cov: 0.0000001                # 进一步降低偏置噪声

# 发布参数
publish:
    pub_voxel_map: false
    publish_max_voxel_layer: 3          # 增加到3层
    path_en: true
    publish_limit_z: 3.0
    publish_dense_skip: 1               # 从2降低到1，发布更密集的点云
    scan_publish_en: true
    dense_publish_en: true
    scan_bodyframe_pub_en: true
    intensity_th: 1.0                   # 降低强度阈值

# PCD保存参数
pcd_save:
    pcd_save_en: true
    interval: -1
    directory: "/home/<USER>/slam_share/aLidar/temp"
