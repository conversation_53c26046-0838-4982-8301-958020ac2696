/**
 * 强制首尾回环精细匹配器
 * 专门解决GPS接近但SLAM仍有偏差的问题
 * 当GPS距离很小时，强制执行首尾帧的高精度匹配
 */

#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <std_msgs/String.h>
#include <sensor_msgs/PointCloud2.h>
#include <geometry_msgs/PoseStamped.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>
#include <pcl/registration/ndt.h>
#include <pcl/registration/gicp.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/crop_box.h>
#include <pcl_conversions/pcl_conversions.h>

#include <vector>
#include <deque>
#include <mutex>
#include <sstream>
#include <algorithm>
#include <cctype>

class ForceStartEndLoopMatcher {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 订阅器
    ros::Subscriber intelligent_force_sub_;
    ros::Subscriber pointcloud_sub_;
    ros::Subscriber pose_sub_;
    
    // 发布器
    ros::Publisher loop_result_pub_;
    ros::Publisher corrected_pose_pub_;
    ros::Publisher match_score_pub_;
    ros::Publisher debug_info_pub_;
    
    // 参数
    struct MatchingParams {
        double voxel_size;
        double search_radius;
        double convergence_threshold;
        double max_correspondence_distance;
        int max_iterations;
        double transformation_epsilon;
        double euclidean_fitness_epsilon;
        bool use_reciprocal_correspondences;
        double outlier_rejection_threshold;
    } params_;
    
    // 数据存储
    std::deque<pcl::PointCloud<pcl::PointXYZI>::Ptr> keyframe_clouds_;
    std::deque<geometry_msgs::PoseStamped> keyframe_poses_;
    std::mutex data_mutex_;
    
    // 状态变量
    bool matching_in_progress_;
    int max_keyframes_;
    
public:
    ForceStartEndLoopMatcher() : private_nh_("~"), matching_in_progress_(false) {
        loadParameters();
        initializeSubscribers();
        initializePublishers();
        
        ROS_INFO("Force Start-End Loop Matcher Started");
        ROS_INFO("Voxel size: %.3f", params_.voxel_size);
        ROS_INFO("Search radius: %.1f", params_.search_radius);
        ROS_INFO("Max iterations: %d", params_.max_iterations);
    }
    
    void loadParameters() {
        private_nh_.param("voxel_size", params_.voxel_size, 0.1);
        private_nh_.param("search_radius", params_.search_radius, 50.0);
        private_nh_.param("convergence_threshold", params_.convergence_threshold, 1e-6);
        private_nh_.param("max_correspondence_distance", params_.max_correspondence_distance, 2.0);
        private_nh_.param("max_iterations", params_.max_iterations, 100);
        private_nh_.param("transformation_epsilon", params_.transformation_epsilon, 1e-8);
        private_nh_.param("euclidean_fitness_epsilon", params_.euclidean_fitness_epsilon, 1e-6);
        private_nh_.param("use_reciprocal_correspondences", params_.use_reciprocal_correspondences, true);
        private_nh_.param("outlier_rejection_threshold", params_.outlier_rejection_threshold, 1.0);
        private_nh_.param("max_keyframes", max_keyframes_, 1000);
    }
    
    void initializeSubscribers() {
        intelligent_force_sub_ = nh_.subscribe("/intelligent_force_loop_closure", 1,
            &ForceStartEndLoopMatcher::intelligentForceCallback, this);
        pointcloud_sub_ = nh_.subscribe("/cloud_registered", 1,
            &ForceStartEndLoopMatcher::pointCloudCallback, this);
        pose_sub_ = nh_.subscribe("/aft_mapped_to_init", 1,
            &ForceStartEndLoopMatcher::poseCallback, this);
    }
    
    void initializePublishers() {
        loop_result_pub_ = nh_.advertise<std_msgs::Bool>("/force_loop_result", 1);
        corrected_pose_pub_ = nh_.advertise<geometry_msgs::PoseStamped>("/force_corrected_pose", 1);
        match_score_pub_ = nh_.advertise<std_msgs::String>("/force_match_score", 1);
        debug_info_pub_ = nh_.advertise<std_msgs::String>("/force_match_debug", 1);
    }
    
    void intelligentForceCallback(const std_msgs::String::ConstPtr& msg) {
        if (matching_in_progress_) {
            ROS_WARN("Force matching already in progress, skipping...");
            return;
        }
        
        try {
            // 简单解析智能检测数据
            std::string data = msg->data;

            // 检查是否是智能首尾匹配类型
            if (data.find("intelligent_start_end_precise_match") == std::string::npos) {
                return;
            }

            // 提取GPS距离
            double gps_distance = 0.0;
            size_t pos = data.find("distance_to_start");
            if (pos != std::string::npos) {
                size_t start = data.find(":", pos) + 1;
                size_t end = data.find(",", start);
                if (end == std::string::npos) end = data.find("}", start);
                std::string distance_str = data.substr(start, end - start);
                // 移除空格和引号
                distance_str.erase(std::remove_if(distance_str.begin(), distance_str.end(),
                    [](char c) { return std::isspace(c) || c == '"'; }), distance_str.end());
                gps_distance = std::stod(distance_str);
            }
            
            ROS_WARN("🔥 FORCE START-END MATCHING TRIGGERED!");
            ROS_INFO("GPS distance to start: %.2f meters", gps_distance);
            
            // 执行强制首尾匹配
            performForceStartEndMatching(gps_distance);
            
        } catch (const std::exception& e) {
            ROS_ERROR("Error in intelligent force callback: %s", e.what());
        }
    }
    
    void pointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        pcl::PointCloud<pcl::PointXYZI>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZI>);
        pcl::fromROSMsg(*msg, *cloud);
        
        if (cloud->empty()) return;
        
        keyframe_clouds_.push_back(cloud);
        if (keyframe_clouds_.size() > max_keyframes_) {
            keyframe_clouds_.pop_front();
        }
    }
    
    void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        keyframe_poses_.push_back(*msg);
        if (keyframe_poses_.size() > max_keyframes_) {
            keyframe_poses_.pop_front();
        }
    }
    
    void performForceStartEndMatching(double gps_distance) {
        matching_in_progress_ = true;
        
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        if (keyframe_clouds_.size() < 20) {
            ROS_WARN("Insufficient keyframes for force matching: %lu", keyframe_clouds_.size());
            matching_in_progress_ = false;
            return;
        }
        
        ROS_INFO("🎯 Starting force start-end matching with %lu keyframes", keyframe_clouds_.size());
        
        // 获取起始帧和结束帧
        auto start_cloud = keyframe_clouds_.front();
        auto end_cloud = keyframe_clouds_.back();
        
        // 预处理点云
        auto processed_start = preprocessPointCloud(start_cloud);
        auto processed_end = preprocessPointCloud(end_cloud);
        
        // 执行多种匹配算法
        std::vector<MatchResult> results;
        
        // 1. ICP匹配
        auto icp_result = performICPMatching(processed_start, processed_end);
        icp_result.method = "ICP";
        results.push_back(icp_result);
        
        // 2. NDT匹配
        auto ndt_result = performNDTMatching(processed_start, processed_end);
        ndt_result.method = "NDT";
        results.push_back(ndt_result);
        
        // 3. GICP匹配
        auto gicp_result = performGICPMatching(processed_start, processed_end);
        gicp_result.method = "GICP";
        results.push_back(gicp_result);
        
        // 选择最佳匹配结果
        auto best_result = selectBestResult(results);
        
        // 发布结果
        publishMatchingResults(best_result, gps_distance);
        
        matching_in_progress_ = false;
    }
    
    struct MatchResult {
        Eigen::Matrix4f transformation;
        double fitness_score;
        bool converged;
        std::string method;
        int iterations;
        double final_mse;
    };
    
    pcl::PointCloud<pcl::PointXYZI>::Ptr preprocessPointCloud(
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& input) {
        
        pcl::PointCloud<pcl::PointXYZI>::Ptr filtered(new pcl::PointCloud<pcl::PointXYZI>);
        
        // 体素滤波
        pcl::VoxelGrid<pcl::PointXYZI> voxel_filter;
        voxel_filter.setInputCloud(input);
        voxel_filter.setLeafSize(params_.voxel_size, params_.voxel_size, params_.voxel_size);
        voxel_filter.filter(*filtered);
        
        return filtered;
    }
    
    MatchResult performICPMatching(
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& source,
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& target) {
        
        MatchResult result;
        
        pcl::IterativeClosestPoint<pcl::PointXYZI, pcl::PointXYZI> icp;
        icp.setInputSource(source);
        icp.setInputTarget(target);
        icp.setMaxCorrespondenceDistance(params_.max_correspondence_distance);
        icp.setMaximumIterations(params_.max_iterations);
        icp.setTransformationEpsilon(params_.transformation_epsilon);
        icp.setEuclideanFitnessEpsilon(params_.euclidean_fitness_epsilon);
        icp.setUseReciprocalCorrespondences(params_.use_reciprocal_correspondences);
        
        pcl::PointCloud<pcl::PointXYZI> aligned;
        icp.align(aligned);
        
        result.transformation = icp.getFinalTransformation();
        result.fitness_score = icp.getFitnessScore();
        result.converged = icp.hasConverged();
        result.iterations = icp.nr_iterations_;
        result.final_mse = icp.getFitnessScore();
        
        return result;
    }

    MatchResult performNDTMatching(
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& source,
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& target) {

        MatchResult result;

        pcl::NormalDistributionsTransform<pcl::PointXYZI, pcl::PointXYZI> ndt;
        ndt.setInputSource(source);
        ndt.setInputTarget(target);
        ndt.setResolution(params_.voxel_size * 2.0);
        ndt.setMaximumIterations(params_.max_iterations);
        ndt.setTransformationEpsilon(params_.transformation_epsilon);

        pcl::PointCloud<pcl::PointXYZI> aligned;
        ndt.align(aligned);

        result.transformation = ndt.getFinalTransformation();
        result.fitness_score = ndt.getFitnessScore();
        result.converged = ndt.hasConverged();
        result.iterations = ndt.getFinalNumIteration();
        result.final_mse = ndt.getFitnessScore();

        return result;
    }

    MatchResult performGICPMatching(
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& source,
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& target) {

        MatchResult result;

        pcl::GeneralizedIterativeClosestPoint<pcl::PointXYZI, pcl::PointXYZI> gicp;
        gicp.setInputSource(source);
        gicp.setInputTarget(target);
        gicp.setMaxCorrespondenceDistance(params_.max_correspondence_distance);
        gicp.setMaximumIterations(params_.max_iterations);
        gicp.setTransformationEpsilon(params_.transformation_epsilon);

        pcl::PointCloud<pcl::PointXYZI> aligned;
        gicp.align(aligned);

        result.transformation = gicp.getFinalTransformation();
        result.fitness_score = gicp.getFitnessScore();
        result.converged = gicp.hasConverged();
        result.iterations = gicp.nr_iterations_;
        result.final_mse = gicp.getFitnessScore();

        return result;
    }

    MatchResult selectBestResult(const std::vector<MatchResult>& results) {
        MatchResult best_result = results[0];

        for (const auto& result : results) {
            if (result.converged && result.fitness_score < best_result.fitness_score) {
                best_result = result;
            }
        }

        return best_result;
    }

    void publishMatchingResults(const MatchResult& result, double gps_distance) {
        // 发布匹配结果
        std_msgs::Bool loop_result;
        loop_result.data = result.converged && result.fitness_score < params_.outlier_rejection_threshold;
        loop_result_pub_.publish(loop_result);

        // 发布匹配分数
        std_msgs::String score_msg;
        std::stringstream score_ss;
        score_ss << "{"
                 << "\"method\":\"" << result.method << "\","
                 << "\"fitness_score\":" << result.fitness_score << ","
                 << "\"converged\":" << (result.converged ? "true" : "false") << ","
                 << "\"iterations\":" << result.iterations << ","
                 << "\"gps_distance\":" << gps_distance << ","
                 << "\"success\":" << (loop_result.data ? "true" : "false")
                 << "}";
        score_msg.data = score_ss.str();
        match_score_pub_.publish(score_msg);

        // 发布调试信息
        std_msgs::String debug_msg;
        std::stringstream debug_ss;
        debug_ss << "{"
                 << "\"timestamp\":" << ros::Time::now().toSec() << ","
                 << "\"keyframes_used\":" << keyframe_clouds_.size() << ","
                 << "\"matching_method\":\"" << result.method << "\","
                 << "\"transformation_matrix\":[";

        for (int i = 0; i < 4; ++i) {
            debug_ss << "[";
            for (int j = 0; j < 4; ++j) {
                debug_ss << result.transformation(i, j);
                if (j < 3) debug_ss << ",";
            }
            debug_ss << "]";
            if (i < 3) debug_ss << ",";
        }
        debug_ss << "]}";

        debug_msg.data = debug_ss.str();
        debug_info_pub_.publish(debug_msg);

        // 日志输出
        ROS_INFO("🎯 Force matching completed!");
        ROS_INFO("Method: %s", result.method.c_str());
        ROS_INFO("Fitness score: %.6f", result.fitness_score);
        ROS_INFO("Converged: %s", result.converged ? "YES" : "NO");
        ROS_INFO("Iterations: %d", result.iterations);
        ROS_INFO("GPS distance: %.2f meters", gps_distance);
        ROS_INFO("Success: %s", loop_result.data ? "YES" : "NO");

        if (loop_result.data) {
            ROS_INFO("✅ FORCE START-END MATCHING SUCCESSFUL!");
        } else {
            ROS_WARN("❌ Force matching failed - fitness score too high");
        }
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "force_start_end_loop_matcher");

    try {
        ForceStartEndLoopMatcher matcher;
        ros::spin();
    } catch (const std::exception& e) {
        ROS_ERROR("Force Start-End Loop Matcher error: %s", e.what());
        return -1;
    }

    return 0;
}
