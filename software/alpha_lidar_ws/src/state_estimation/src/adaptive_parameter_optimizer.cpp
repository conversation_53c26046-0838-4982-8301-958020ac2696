/**
 * 自适应参数优化器
 * 根据实时数据质量和系统性能自动调整GPS回环检测和强度保持参数
 */

#include <ros/ros.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/NavSatFix.h>
#include <std_msgs/Float64.h>
#include <std_msgs/String.h>
#include <geometry_msgs/PoseStamped.h>
#include <dynamic_reconfigure/server.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include <deque>
#include <mutex>
#include <thread>
#include <chrono>
#include <algorithm>
#include <numeric>

class AdaptiveParameterOptimizer {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 订阅器
    ros::Subscriber gps_sub_;
    ros::Subscriber pointcloud_sub_;
    ros::Subscriber pose_sub_;
    ros::Subscriber loop_detection_sub_;
    
    // 发布器
    ros::Publisher optimization_status_pub_;
    ros::Publisher parameter_changes_pub_;
    ros::Publisher performance_metrics_pub_;
    
    // 数据缓存
    std::deque<sensor_msgs::NavSatFix> gps_history_;
    std::deque<double> gps_quality_history_;
    std::deque<size_t> pointcloud_size_history_;
    std::deque<double> processing_time_history_;
    std::deque<bool> loop_detection_history_;
    
    // 性能指标
    struct PerformanceMetrics {
        double avg_gps_quality = 0.0;
        double gps_stability = 0.0;
        double avg_pointcloud_density = 0.0;
        double processing_efficiency = 0.0;
        double loop_detection_rate = 0.0;
        double system_load = 0.0;
        double memory_usage = 0.0;
    };
    
    PerformanceMetrics current_metrics_;
    
    // 自适应参数
    struct AdaptiveParams {
        double loop_distance_threshold = 8.0;
        double intermediate_threshold = 10.0;
        double gps_quality_threshold = -1;
        double search_radius = 15.0;
        double score_threshold = 0.35;
        double voxel_size = 0.1;
        double intensity_scale = 1.0;
        int max_candidates = 10;
    };
    
    AdaptiveParams current_params_;
    AdaptiveParams optimal_params_;
    
    // 优化状态
    std::mutex data_mutex_;
    bool optimization_active_;
    std::chrono::steady_clock::time_point last_optimization_;
    double optimization_interval_;

    // 统计数据
    size_t total_points_processed_;
    
    // 学习参数
    double learning_rate_;
    double stability_weight_;
    double performance_weight_;
    int history_window_size_;
    
public:
    AdaptiveParameterOptimizer() :
        private_nh_("~"),
        optimization_active_(true),
        optimization_interval_(30.0),
        learning_rate_(0.1),
        stability_weight_(0.6),
        performance_weight_(0.4),
        history_window_size_(100),
        total_points_processed_(0) {
        
        loadParameters();
        initializeSubscribers();
        initializePublishers();
        
        // 启动优化线程
        std::thread optimization_thread(&AdaptiveParameterOptimizer::optimizationLoop, this);
        optimization_thread.detach();
        
        ROS_INFO("Adaptive Parameter Optimizer Started");
        printConfiguration();
    }
    
    void loadParameters() {
        private_nh_.param("optimization_interval", optimization_interval_, 30.0);
        private_nh_.param("learning_rate", learning_rate_, 0.1);
        private_nh_.param("stability_weight", stability_weight_, 0.6);
        private_nh_.param("performance_weight", performance_weight_, 0.4);
        private_nh_.param("history_window_size", history_window_size_, 100);
        
        // 加载初始参数
        private_nh_.param("initial_loop_distance_threshold", current_params_.loop_distance_threshold, 8.0);
        private_nh_.param("initial_intermediate_threshold", current_params_.intermediate_threshold, 10.0);
        private_nh_.param("initial_search_radius", current_params_.search_radius, 15.0);
        private_nh_.param("initial_score_threshold", current_params_.score_threshold, 0.35);
        private_nh_.param("initial_voxel_size", current_params_.voxel_size, 0.1);
        
        optimal_params_ = current_params_;
    }
    
    void initializeSubscribers() {
        gps_sub_ = nh_.subscribe("/rtk/gnss", 10, 
            &AdaptiveParameterOptimizer::gpsCallback, this);
        pointcloud_sub_ = nh_.subscribe("/velodyne_points", 5,
            &AdaptiveParameterOptimizer::pointcloudCallback, this);
        pose_sub_ = nh_.subscribe("/aft_mapped_to_init", 10,
            &AdaptiveParameterOptimizer::poseCallback, this);
        loop_detection_sub_ = nh_.subscribe("/force_loop_closure", 10,
            &AdaptiveParameterOptimizer::loopDetectionCallback, this);
    }
    
    void initializePublishers() {
        optimization_status_pub_ = nh_.advertise<std_msgs::String>("/optimization_status", 1);
        parameter_changes_pub_ = nh_.advertise<std_msgs::String>("/parameter_changes", 1);
        performance_metrics_pub_ = nh_.advertise<std_msgs::String>("/performance_metrics", 1);
    }
    
    void printConfiguration() {
        ROS_INFO("Adaptive Parameter Optimizer Configuration:");
        ROS_INFO("  Optimization interval: %.1f seconds", optimization_interval_);
        ROS_INFO("  Learning rate: %.3f", learning_rate_);
        ROS_INFO("  Stability weight: %.2f", stability_weight_);
        ROS_INFO("  Performance weight: %.2f", performance_weight_);
        ROS_INFO("  History window size: %d", history_window_size_);
    }
    
    void gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        gps_history_.push_back(*msg);
        if (gps_history_.size() > history_window_size_) {
            gps_history_.pop_front();
        }
        
        // 计算GPS质量分数
        double quality_score = calculateGPSQuality(*msg);
        gps_quality_history_.push_back(quality_score);
        if (gps_quality_history_.size() > history_window_size_) {
            gps_quality_history_.pop_front();
        }
    }
    
    void pointcloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 分析点云密度
        pcl::PointCloud<pcl::PointXYZI>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZI>());
        pcl::fromROSMsg(*msg, *cloud);
        
        pointcloud_size_history_.push_back(cloud->size());
        if (pointcloud_size_history_.size() > history_window_size_) {
            pointcloud_size_history_.pop_front();
        }

        // 更新总点数统计
        total_points_processed_ += cloud->size();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        processing_time_history_.push_back(duration.count() / 1000.0); // 转换为毫秒
        
        if (processing_time_history_.size() > history_window_size_) {
            processing_time_history_.pop_front();
        }
    }
    
    void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg) {
        // 分析位姿稳定性和轨迹质量
        // 这里可以添加轨迹分析逻辑
    }
    
    void loopDetectionCallback(const std_msgs::String::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);

        // 解析回环检测消息（通常是字符串格式）
        bool loop_detected = (msg->data == "loop_detected" || msg->data == "true");
        loop_detection_history_.push_back(loop_detected);
        if (loop_detection_history_.size() > history_window_size_) {
            loop_detection_history_.pop_front();
        }
    }
    
    double calculateGPSQuality(const sensor_msgs::NavSatFix& gps_msg) {
        // 基于GPS状态和协方差计算质量分数
        double quality_score = 0.0;
        
        // 状态分数
        switch (gps_msg.status.status) {
            case sensor_msgs::NavSatStatus::STATUS_NO_FIX:
                quality_score += 0.0;
                break;
            case sensor_msgs::NavSatStatus::STATUS_FIX:
                quality_score += 0.3;
                break;
            case sensor_msgs::NavSatStatus::STATUS_SBAS_FIX:
                quality_score += 0.6;
                break;
            case sensor_msgs::NavSatStatus::STATUS_GBAS_FIX:
                quality_score += 1.0;
                break;
            default:
                quality_score += 0.1;
        }
        
        // 协方差分数（越小越好）
        if (!gps_msg.position_covariance.empty()) {
            double avg_covariance = (gps_msg.position_covariance[0] + 
                                   gps_msg.position_covariance[4] + 
                                   gps_msg.position_covariance[8]) / 3.0;
            
            if (avg_covariance < 1.0) quality_score += 0.5;
            else if (avg_covariance < 5.0) quality_score += 0.3;
            else if (avg_covariance < 10.0) quality_score += 0.1;
        }
        
        return std::min(1.0, quality_score);
    }
    
    void updatePerformanceMetrics() {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // GPS质量指标
        if (!gps_quality_history_.empty()) {
            current_metrics_.avg_gps_quality = std::accumulate(
                gps_quality_history_.begin(), gps_quality_history_.end(), 0.0) / 
                gps_quality_history_.size();
            
            // GPS稳定性（方差的倒数）
            double mean = current_metrics_.avg_gps_quality;
            double variance = 0.0;
            for (double quality : gps_quality_history_) {
                variance += (quality - mean) * (quality - mean);
            }
            variance /= gps_quality_history_.size();
            current_metrics_.gps_stability = 1.0 / (1.0 + variance);
        }
        
        // 点云密度指标
        if (!pointcloud_size_history_.empty()) {
            current_metrics_.avg_pointcloud_density = std::accumulate(
                pointcloud_size_history_.begin(), pointcloud_size_history_.end(), 0.0) / 
                pointcloud_size_history_.size();
        }
        
        // 处理效率指标
        if (!processing_time_history_.empty()) {
            double avg_time = std::accumulate(
                processing_time_history_.begin(), processing_time_history_.end(), 0.0) / 
                processing_time_history_.size();
            current_metrics_.processing_efficiency = 1000.0 / (avg_time + 1.0); // 越快效率越高
        }
        
        // 回环检测率
        if (!loop_detection_history_.empty()) {
            int detections = std::count(loop_detection_history_.begin(), 
                                      loop_detection_history_.end(), true);
            current_metrics_.loop_detection_rate = static_cast<double>(detections) / 
                                                 loop_detection_history_.size();
        }
    }
    
    void optimizeParameters() {
        updatePerformanceMetrics();
        
        AdaptiveParams new_params = current_params_;
        
        // 基于GPS质量调整参数
        if (current_metrics_.avg_gps_quality < 0.3) {
            // GPS质量差，放宽参数
            new_params.loop_distance_threshold = std::min(15.0, 
                current_params_.loop_distance_threshold * (1.0 + learning_rate_));
            new_params.intermediate_threshold = std::min(20.0,
                current_params_.intermediate_threshold * (1.0 + learning_rate_));
            new_params.score_threshold = std::min(0.6,
                current_params_.score_threshold * (1.0 + learning_rate_));
            new_params.search_radius = std::min(30.0,
                current_params_.search_radius * (1.0 + learning_rate_));
        } else if (current_metrics_.avg_gps_quality > 0.7) {
            // GPS质量好，收紧参数
            new_params.loop_distance_threshold = std::max(3.0,
                current_params_.loop_distance_threshold * (1.0 - learning_rate_));
            new_params.intermediate_threshold = std::max(5.0,
                current_params_.intermediate_threshold * (1.0 - learning_rate_));
            new_params.score_threshold = std::max(0.15,
                current_params_.score_threshold * (1.0 - learning_rate_));
            new_params.search_radius = std::max(8.0,
                current_params_.search_radius * (1.0 - learning_rate_));
        }
        
        // 基于处理效率调整体素大小
        if (current_metrics_.processing_efficiency < 50.0) {
            // 处理慢，增大体素大小
            new_params.voxel_size = std::min(0.3,
                current_params_.voxel_size * (1.0 + learning_rate_));
        } else if (current_metrics_.processing_efficiency > 200.0) {
            // 处理快，减小体素大小提高精度
            new_params.voxel_size = std::max(0.02,
                current_params_.voxel_size * (1.0 - learning_rate_));
        }
        
        // 基于回环检测率调整候选数
        if (current_metrics_.loop_detection_rate < 0.1) {
            // 检测率低，增加候选数
            new_params.max_candidates = std::min(20, current_params_.max_candidates + 2);
        } else if (current_metrics_.loop_detection_rate > 0.5) {
            // 检测率高，可以减少候选数
            new_params.max_candidates = std::max(5, current_params_.max_candidates - 1);
        }
        
        // 应用参数变化
        if (shouldApplyChanges(new_params)) {
            applyParameterChanges(new_params);
            current_params_ = new_params;
            publishParameterChanges();
        }
        
        publishPerformanceMetrics();
    }
    
    bool shouldApplyChanges(const AdaptiveParams& new_params) {
        // 计算参数变化幅度
        double change_magnitude = 
            std::abs(new_params.loop_distance_threshold - current_params_.loop_distance_threshold) +
            std::abs(new_params.intermediate_threshold - current_params_.intermediate_threshold) +
            std::abs(new_params.score_threshold - current_params_.score_threshold) +
            std::abs(new_params.search_radius - current_params_.search_radius) +
            std::abs(new_params.voxel_size - current_params_.voxel_size);
        
        // 只有变化足够大时才应用
        return change_magnitude > 0.1;
    }
    
    void applyParameterChanges(const AdaptiveParams& new_params) {
        // 更新GPS回环检测参数
        ros::param::set("/gps_loop_optimizer/loop_closure_distance_threshold", 
                       new_params.loop_distance_threshold);
        ros::param::set("/gps_loop_optimizer/intermediate_loop_threshold", 
                       new_params.intermediate_threshold);
        ros::param::set("/gps_loop_optimizer/gps_quality_threshold", 
                       new_params.gps_quality_threshold);
        
        // 更新SLAM集成参数
        ros::param::set("/slam_loop_integration/force_search_radius", 
                       new_params.search_radius);
        ros::param::set("/slam_loop_integration/intermediate_score_threshold", 
                       new_params.score_threshold);
        ros::param::set("/slam_loop_integration/voxel_leaf_size", 
                       new_params.voxel_size);
        ros::param::set("/slam_loop_integration/max_search_candidates", 
                       new_params.max_candidates);
        
        // 更新强度保持参数
        ros::param::set("/intensity_pcd_saver/voxel_leaf_size", 
                       new_params.voxel_size);
        ros::param::set("/intensity_pcd_saver/intensity_scale_factor", 
                       new_params.intensity_scale);
        
        ROS_INFO("Applied adaptive parameter changes");
    }
    
    void publishParameterChanges() {
        std_msgs::String msg;
        std::stringstream ss;
        
        ss << "Adaptive Parameter Update:\n";
        ss << "  Loop distance threshold: " << current_params_.loop_distance_threshold << "\n";
        ss << "  Intermediate threshold: " << current_params_.intermediate_threshold << "\n";
        ss << "  Search radius: " << current_params_.search_radius << "\n";
        ss << "  Score threshold: " << current_params_.score_threshold << "\n";
        ss << "  Voxel size: " << current_params_.voxel_size << "\n";
        ss << "  Max candidates: " << current_params_.max_candidates;
        
        msg.data = ss.str();
        parameter_changes_pub_.publish(msg);
    }
    
    void publishPerformanceMetrics() {
        std_msgs::String msg;
        std::stringstream ss;

        // 发布JSON格式的性能指标
        ss << "{\n";
        ss << "  \"processing_statistics\": {\n";
        ss << "    \"gps_quality\": " << current_metrics_.avg_gps_quality << ",\n";
        ss << "    \"gps_stability\": " << current_metrics_.gps_stability << ",\n";
        ss << "    \"point_density\": " << current_metrics_.avg_pointcloud_density << ",\n";
        ss << "    \"processing_efficiency\": " << current_metrics_.processing_efficiency << ",\n";
        ss << "    \"loop_detection_rate\": " << current_metrics_.loop_detection_rate << ",\n";
        ss << "    \"total_points_processed\": " << total_points_processed_ << ",\n";
        ss << "    \"average_fps\": " << (current_metrics_.processing_efficiency * 10.0) << "\n";
        ss << "  }\n";
        ss << "}";

        msg.data = ss.str();
        performance_metrics_pub_.publish(msg);
    }
    
    void optimizationLoop() {
        ros::Rate rate(1.0 / optimization_interval_);
        
        while (ros::ok() && optimization_active_) {
            try {
                optimizeParameters();
                
                std_msgs::String status_msg;
                status_msg.data = "Optimization cycle completed successfully";
                optimization_status_pub_.publish(status_msg);
                
            } catch (const std::exception& e) {
                ROS_ERROR("Optimization error: %s", e.what());
            }
            
            rate.sleep();
        }
    }
    
    ~AdaptiveParameterOptimizer() {
        optimization_active_ = false;
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "adaptive_parameter_optimizer");
    
    try {
        AdaptiveParameterOptimizer optimizer;
        
        ROS_INFO("Adaptive Parameter Optimizer running...");
        ROS_INFO("Automatically optimizing system parameters based on real-time performance");
        
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Adaptive Parameter Optimizer exception: %s", e.what());
        return -1;
    }
    
    ROS_INFO("Adaptive Parameter Optimizer stopped");
    return 0;
}
