#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版性能监控器
提供基础的系统性能监控，不使用GUI和中文字符
"""

import rospy
import psutil
import time
import json
from std_msgs.msg import String
from collections import deque
import threading

class SimplePerformanceMonitor:
    def __init__(self):
        rospy.init_node('simple_performance_monitor', anonymous=True)
        
        # 参数配置
        self.report_interval = rospy.get_param('~report_interval', 30.0)
        self.max_history = rospy.get_param('~max_history', 100)
        
        # 性能数据存储
        self.performance_data = {
            'timestamps': deque(maxlen=self.max_history),
            'cpu_usage': deque(maxlen=self.max_history),
            'memory_usage': deque(maxlen=self.max_history),
            'processing_fps': deque(maxlen=self.max_history),
            'quality_scores': deque(maxlen=self.max_history)
        }
        
        # 系统状态
        self.system_status = {
            'slam_active': False,
            'gps_loop_active': False,
            'intensity_preservation_active': False,
            'adaptive_optimization_active': False,
            'overall_health': 'Unknown'
        }
        
        # 实时统计
        self.current_stats = {
            'total_points_processed': 0,
            'total_loops_detected': 0,
            'total_corrections_applied': 0,
            'uptime_seconds': 0,
            'current_fps': 0.0,
            'current_cpu': 0.0,
            'current_memory': 0.0
        }
        
        # 订阅器和发布器
        self.setup_subscribers()
        self.performance_report_pub = rospy.Publisher(
            '/performance_monitor_report', String, queue_size=1)
        
        # 启动监控线程
        self.monitoring_active = True
        self.start_time = time.time()
        self.monitor_thread = threading.Thread(target=self.monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        # 定时器
        self.report_timer = rospy.Timer(
            rospy.Duration(self.report_interval), self.publish_performance_report)
        
        rospy.loginfo("Simple Performance Monitor Started")
        rospy.loginfo("Report interval: %.1f seconds", self.report_interval)
    
    def setup_subscribers(self):
        """设置ROS订阅器"""
        # 性能指标订阅
        rospy.Subscriber('/performance_metrics', String, self.performance_callback)
        rospy.Subscriber('/intensity_statistics', String, self.intensity_callback)
        rospy.Subscriber('/pointcloud_quality_report', String, self.quality_callback)
        rospy.Subscriber('/optimization_status', String, self.optimization_callback)
        rospy.Subscriber('/parameter_changes', String, self.parameter_callback)
    
    def performance_callback(self, msg):
        """处理性能指标消息"""
        try:
            if msg.data.strip():  # 检查消息不为空
                data = json.loads(msg.data)
                current_time = time.time()
                
                self.performance_data['timestamps'].append(current_time)
                
                # 更新性能数据
                if 'processing_statistics' in data:
                    stats = data['processing_statistics']
                    self.current_stats['total_points_processed'] = int(stats.get('total_points_processed', 0))
                    self.current_stats['current_fps'] = float(stats.get('average_fps', 0.0))
                    self.performance_data['processing_fps'].append(self.current_stats['current_fps'])
        
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            rospy.logwarn("Error processing performance data: %s", str(e))
        except Exception as e:
            rospy.logerr("Unexpected error in performance callback: %s", str(e))
    
    def intensity_callback(self, msg):
        """处理强度统计消息"""
        try:
            if msg.data.strip():
                data = json.loads(msg.data)
                if 'quality_score' in data:
                    quality_score = float(data['quality_score'])
                    self.performance_data['quality_scores'].append(quality_score)
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            rospy.logwarn("Error processing intensity data: %s", str(e))
        except Exception as e:
            rospy.logerr("Unexpected error in intensity callback: %s", str(e))
    
    def quality_callback(self, msg):
        """处理质量报告消息"""
        try:
            if msg.data.strip():
                data = json.loads(msg.data)
                if 'quality_metrics' in data:
                    metrics = data['quality_metrics']
                    quality_score = float(metrics.get('overall_quality_score', 0.0))
                    self.performance_data['quality_scores'].append(quality_score)
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            rospy.logwarn("Error processing quality data: %s", str(e))
        except Exception as e:
            rospy.logerr("Unexpected error in quality callback: %s", str(e))
    
    def optimization_callback(self, msg):
        """处理优化状态消息"""
        self.system_status['adaptive_optimization_active'] = True
    
    def parameter_callback(self, msg):
        """处理参数变化消息"""
        pass  # 简化版不处理参数变化
    
    def monitoring_loop(self):
        """监控循环"""
        while not rospy.is_shutdown() and self.monitoring_active:
            try:
                # 获取系统资源使用情况
                cpu_percent = float(psutil.cpu_percent(interval=1))
                memory_percent = float(psutil.virtual_memory().percent)
                
                current_time = time.time()
                
                # 更新性能数据
                self.performance_data['timestamps'].append(current_time)
                self.performance_data['cpu_usage'].append(cpu_percent)
                self.performance_data['memory_usage'].append(memory_percent)
                
                # 更新当前统计
                self.current_stats['current_cpu'] = cpu_percent
                self.current_stats['current_memory'] = memory_percent
                self.current_stats['uptime_seconds'] = current_time - self.start_time
                
                # 评估系统健康状态
                self.assess_system_health()
                
                time.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                rospy.logerr("Error in monitoring loop: %s", str(e))
                time.sleep(10)
    
    def assess_system_health(self):
        """评估系统健康状态"""
        try:
            health_score = 1.0
            
            # 基于CPU使用率
            if self.current_stats['current_cpu'] > 90:
                health_score -= 0.3
            elif self.current_stats['current_cpu'] > 70:
                health_score -= 0.1
            
            # 基于内存使用率
            if self.current_stats['current_memory'] > 90:
                health_score -= 0.3
            elif self.current_stats['current_memory'] > 70:
                health_score -= 0.1
            
            # 基于处理性能
            if self.current_stats['current_fps'] < 1.0:
                health_score -= 0.2
            
            # 确定健康等级
            if health_score >= 0.8:
                self.system_status['overall_health'] = "Excellent"
            elif health_score >= 0.6:
                self.system_status['overall_health'] = "Good"
            elif health_score >= 0.4:
                self.system_status['overall_health'] = "Fair"
            else:
                self.system_status['overall_health'] = "Poor"
                
        except Exception as e:
            rospy.logerr("Error assessing system health: %s", str(e))
            self.system_status['overall_health'] = "Unknown"
    
    def publish_performance_report(self, event):
        """发布性能报告"""
        try:
            # 计算平均值
            avg_cpu = sum(self.performance_data['cpu_usage']) / max(len(self.performance_data['cpu_usage']), 1) if self.performance_data['cpu_usage'] else 0
            avg_memory = sum(self.performance_data['memory_usage']) / max(len(self.performance_data['memory_usage']), 1) if self.performance_data['memory_usage'] else 0
            avg_fps = sum(self.performance_data['processing_fps']) / max(len(self.performance_data['processing_fps']), 1) if self.performance_data['processing_fps'] else 0
            avg_quality = sum(self.performance_data['quality_scores']) / max(len(self.performance_data['quality_scores']), 1) if self.performance_data['quality_scores'] else 0
            
            # 生成报告
            report = {
                'timestamp': time.time(),
                'uptime_seconds': int(self.current_stats['uptime_seconds']),
                'system_health': self.system_status['overall_health'],
                'current_stats': {
                    'cpu_usage': round(self.current_stats['current_cpu'], 1),
                    'memory_usage': round(self.current_stats['current_memory'], 1),
                    'processing_fps': round(self.current_stats['current_fps'], 2),
                    'total_points_processed': self.current_stats['total_points_processed']
                },
                'averages': {
                    'cpu_usage': round(avg_cpu, 1),
                    'memory_usage': round(avg_memory, 1),
                    'processing_fps': round(avg_fps, 2),
                    'quality_score': round(avg_quality, 3)
                },
                'system_status': self.system_status,
                'recommendations': self.generate_recommendations()
            }
            
            # 发布报告
            report_msg = String()
            report_msg.data = json.dumps(report, indent=2)
            self.performance_report_pub.publish(report_msg)
            
            # 控制台输出简化报告
            rospy.loginfo("=== Performance Report ===")
            rospy.loginfo("Uptime: %d seconds", report['uptime_seconds'])
            rospy.loginfo("System Health: %s", report['system_health'])
            rospy.loginfo("CPU: %.1f%% (avg: %.1f%%)", report['current_stats']['cpu_usage'], report['averages']['cpu_usage'])
            rospy.loginfo("Memory: %.1f%% (avg: %.1f%%)", report['current_stats']['memory_usage'], report['averages']['memory_usage'])
            rospy.loginfo("Processing FPS: %.2f (avg: %.2f)", report['current_stats']['processing_fps'], report['averages']['processing_fps'])
            rospy.loginfo("Points Processed: %d", report['current_stats']['total_points_processed'])
            rospy.loginfo("========================")
            
        except Exception as e:
            rospy.logerr("Error publishing performance report: %s", str(e))
    
    def generate_recommendations(self):
        """生成优化建议"""
        recommendations = []
        
        try:
            if self.current_stats['current_cpu'] > 80:
                recommendations.append("High CPU usage detected - consider optimization")
            
            if self.current_stats['current_memory'] > 80:
                recommendations.append("High memory usage detected - check for memory leaks")
            
            if self.current_stats['current_fps'] < 1.0:
                recommendations.append("Low processing FPS - check system performance")
            
            if self.system_status['overall_health'] in ['Poor', 'Fair']:
                recommendations.append("System health needs attention")
            
            if not recommendations:
                recommendations.append("System running normally")
                
        except Exception as e:
            rospy.logerr("Error generating recommendations: %s", str(e))
            recommendations = ["Unable to generate recommendations"]
        
        return recommendations

def main():
    try:
        monitor = SimplePerformanceMonitor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Simple Performance Monitor: %s", str(e))

if __name__ == '__main__':
    main()
