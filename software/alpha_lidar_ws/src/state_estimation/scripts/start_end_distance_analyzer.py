#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首尾距离分析器
专门分析轨迹的首尾距离，帮助调节回环检测参数
"""

import rospy
import numpy as np
import math
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import String
from collections import deque
import json
import time

class StartEndDistanceAnalyzer:
    def __init__(self):
        rospy.init_node('start_end_distance_analyzer', anonymous=True)
        
        # 参数
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        self.analysis_interval = rospy.get_param('~analysis_interval', 5.0)
        self.min_points_for_analysis = rospy.get_param('~min_points_for_analysis', 10)
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.trajectory_timestamps = deque(maxlen=2000)
        
        # 订阅器和发布器
        self.gps_sub = rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)
        self.analysis_pub = rospy.Publisher('/start_end_distance_analysis', String, queue_size=1)
        
        # 定时器
        self.analysis_timer = rospy.Timer(
            rospy.Duration(self.analysis_interval), self.analyze_trajectory)
        
        rospy.loginfo("Start-End Distance Analyzer Started")
        rospy.loginfo("GPS topic: %s", self.gps_topic)
        rospy.loginfo("Analysis interval: %.1f seconds", self.analysis_interval)
    
    def gps_callback(self, msg):
        """GPS数据回调"""
        try:
            # 只处理有效的GPS数据
            if not (math.isnan(msg.latitude) or math.isnan(msg.longitude)):
                gps_point = {
                    'latitude': msg.latitude,
                    'longitude': msg.longitude,
                    'altitude': msg.altitude if not math.isnan(msg.altitude) else 0.0,
                    'status': msg.status.status,
                    'timestamp': msg.header.stamp.to_sec()
                }
                
                self.gps_trajectory.append(gps_point)
                self.trajectory_timestamps.append(msg.header.stamp.to_sec())
                
        except Exception as e:
            rospy.logerr("Error in GPS callback: %s", str(e))
    
    def calculate_distance(self, point1, point2):
        """计算两个GPS点之间的距离（米）"""
        try:
            # 使用Haversine公式计算距离
            lat1, lon1 = math.radians(point1['latitude']), math.radians(point1['longitude'])
            lat2, lon2 = math.radians(point2['latitude']), math.radians(point2['longitude'])
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            # 地球半径（米）
            R = 6371000
            distance = R * c
            
            return distance
            
        except Exception as e:
            rospy.logerr("Error calculating distance: %s", str(e))
            return float('inf')
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.gps_trajectory) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(1, len(self.gps_trajectory)):
            distance = self.calculate_distance(
                self.gps_trajectory[i-1], 
                self.gps_trajectory[i]
            )
            if distance < 1000:  # 过滤异常距离
                total_length += distance
        
        return total_length
    
    def analyze_trajectory(self, event):
        """分析轨迹并发布结果"""
        try:
            if len(self.gps_trajectory) < self.min_points_for_analysis:
                return
            
            # 计算首尾距离
            start_point = self.gps_trajectory[0]
            end_point = self.gps_trajectory[-1]
            start_end_distance = self.calculate_distance(start_point, end_point)
            
            # 计算轨迹总长度
            trajectory_length = self.calculate_trajectory_length()
            
            # 计算轨迹时间跨度
            time_span = self.trajectory_timestamps[-1] - self.trajectory_timestamps[0]
            
            # 分析GPS质量
            gps_statuses = [point['status'] for point in self.gps_trajectory]
            status_counts = {}
            for status in gps_statuses:
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # 计算建议的参数
            recommended_distance_threshold = max(start_end_distance * 1.2, 20.0)
            recommended_search_radius = max(start_end_distance * 1.5, 30.0)
            
            # 生成分析报告
            analysis = {
                'timestamp': time.time(),
                'trajectory_info': {
                    'total_points': len(self.gps_trajectory),
                    'trajectory_length_m': round(trajectory_length, 1),
                    'time_span_seconds': round(time_span, 1),
                    'start_end_distance_m': round(start_end_distance, 2)
                },
                'gps_quality': {
                    'status_distribution': status_counts,
                    'dominant_status': max(status_counts.items(), key=lambda x: x[1])[0] if status_counts else -1
                },
                'loop_closure_analysis': {
                    'can_form_loop': start_end_distance < 50.0,  # 50米内认为可以形成回环
                    'loop_quality': self.assess_loop_quality(start_end_distance, trajectory_length),
                    'closure_ratio': start_end_distance / max(trajectory_length, 1.0)
                },
                'recommended_parameters': {
                    'loop_closure_distance_threshold': round(recommended_distance_threshold, 1),
                    'force_search_radius': round(recommended_search_radius, 1),
                    'start_end_score_threshold': self.recommend_score_threshold(start_end_distance),
                    'explanation': self.generate_parameter_explanation(start_end_distance, trajectory_length)
                },
                'current_assessment': {
                    'start_point': {
                        'lat': start_point['latitude'],
                        'lon': start_point['longitude'],
                        'status': start_point['status']
                    },
                    'end_point': {
                        'lat': end_point['latitude'],
                        'lon': end_point['longitude'],
                        'status': end_point['status']
                    }
                }
            }
            
            # 发布分析结果
            analysis_msg = String()
            analysis_msg.data = json.dumps(analysis, indent=2)
            self.analysis_pub.publish(analysis_msg)
            
            # 打印关键信息
            self.print_analysis_summary(analysis)
            
        except Exception as e:
            rospy.logerr("Error in trajectory analysis: %s", str(e))
    
    def assess_loop_quality(self, start_end_distance, trajectory_length):
        """评估回环质量"""
        if trajectory_length < 50:
            return "insufficient_trajectory"
        elif start_end_distance < 5:
            return "excellent"
        elif start_end_distance < 15:
            return "good"
        elif start_end_distance < 30:
            return "fair"
        elif start_end_distance < 50:
            return "poor"
        else:
            return "very_poor"
    
    def recommend_score_threshold(self, start_end_distance):
        """推荐匹配分数阈值"""
        if start_end_distance < 5:
            return 0.3  # 严格匹配
        elif start_end_distance < 15:
            return 0.5  # 中等匹配
        elif start_end_distance < 30:
            return 0.7  # 宽松匹配
        else:
            return 0.85  # 非常宽松匹配
    
    def generate_parameter_explanation(self, start_end_distance, trajectory_length):
        """生成参数解释"""
        explanations = []
        
        if start_end_distance > 30:
            explanations.append(f"首尾距离较大({start_end_distance:.1f}m)，建议使用宽松参数")
        elif start_end_distance < 5:
            explanations.append(f"首尾距离很小({start_end_distance:.1f}m)，可以使用严格参数")
        else:
            explanations.append(f"首尾距离适中({start_end_distance:.1f}m)，使用中等参数")
        
        if trajectory_length > 500:
            explanations.append("轨迹较长，建议增大搜索范围")
        elif trajectory_length < 100:
            explanations.append("轨迹较短，可能影响回环检测效果")
        
        return "; ".join(explanations)
    
    def print_analysis_summary(self, analysis):
        """打印分析摘要"""
        info = analysis['trajectory_info']
        params = analysis['recommended_parameters']
        
        rospy.loginfo("=== 首尾距离分析报告 ===")
        rospy.loginfo("轨迹点数: %d", info['total_points'])
        rospy.loginfo("轨迹长度: %.1f米", info['trajectory_length_m'])
        rospy.loginfo("首尾距离: %.2f米", info['start_end_distance_m'])
        rospy.loginfo("回环质量: %s", analysis['loop_closure_analysis']['loop_quality'])
        rospy.loginfo("建议距离阈值: %.1f米", params['loop_closure_distance_threshold'])
        rospy.loginfo("建议搜索半径: %.1f米", params['force_search_radius'])
        rospy.loginfo("建议匹配阈值: %.2f", params['start_end_score_threshold'])
        rospy.loginfo("参数说明: %s", params['explanation'])
        rospy.loginfo("========================")

def main():
    try:
        analyzer = StartEndDistanceAnalyzer()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Start-End Distance Analyzer: %s", str(e))

if __name__ == '__main__':
    main()
