#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版强度值分析器
提供基础的强度值分析和质量监控功能，不依赖复杂的第三方库
"""

import rospy
import numpy as np
from sensor_msgs.msg import PointCloud2
from std_msgs.msg import String, Float64MultiArray
import sensor_msgs.point_cloud2 as pc2
import json
import time
from collections import deque

def safe_json_serialize(obj):
    """安全的JSON序列化，处理numpy类型"""
    if isinstance(obj, dict):
        return {k: safe_json_serialize(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_json_serialize(v) for v in obj]
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)) or str(type(obj)).find('bool') != -1:
        return bool(obj)
    elif isinstance(obj, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64)) or str(type(obj)).find('int') != -1:
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)) or str(type(obj)).find('float') != -1:
        return float(obj)
    else:
        return obj

class SimpleIntensityAnalyzer:
    def __init__(self):
        rospy.init_node('simple_intensity_analyzer', anonymous=True)
        
        # 参数配置
        self.input_topic = rospy.get_param('~input_topic', '/velodyne_points')
        self.analysis_report_topic = rospy.get_param('~analysis_report_topic', '/intensity_analysis_report')
        self.report_interval = rospy.get_param('~report_interval', 15.0)
        
        # 分析参数
        self.analysis_window_size = rospy.get_param('~analysis_window_size', 50)
        self.intensity_outlier_factor = rospy.get_param('~intensity_outlier_factor', 3.0)
        self.expected_intensity_min = rospy.get_param('~expected_intensity_min', 0.0)
        self.expected_intensity_max = rospy.get_param('~expected_intensity_max', 255.0)
        
        # 数据存储
        self.intensity_history = deque(maxlen=self.analysis_window_size)
        self.quality_metrics_history = deque(maxlen=100)
        
        # 统计信息
        self.total_points_analyzed = 0
        self.total_anomalies_detected = 0
        self.analysis_start_time = time.time()
        
        # 订阅器和发布器
        self.pointcloud_sub = rospy.Subscriber(
            self.input_topic, PointCloud2, self.pointcloud_callback, queue_size=1)
        
        self.analysis_report_pub = rospy.Publisher(
            self.analysis_report_topic, String, queue_size=1)
        
        # 定时器
        self.report_timer = rospy.Timer(
            rospy.Duration(self.report_interval), self.publish_analysis_report)
        
        rospy.loginfo("Simple Intensity Analyzer Started")
        rospy.loginfo("Input topic: %s", self.input_topic)
        rospy.loginfo("Analysis window size: %d", self.analysis_window_size)
    
    def pointcloud_callback(self, msg):
        """处理点云数据"""
        try:
            # 提取点云数据
            points = list(pc2.read_points(msg, field_names=("x", "y", "z", "intensity"), skip_nans=True))
            
            if not points:
                return
            
            # 提取强度值
            intensity_data = np.array([point[3] for point in points])
            
            if len(intensity_data) == 0:
                return
            
            # 更新统计
            self.total_points_analyzed += len(intensity_data)
            
            # 分析强度数据
            analysis_results = self.analyze_intensity_data(intensity_data)
            
            # 存储历史数据
            self.intensity_history.append({
                'timestamp': msg.header.stamp.to_sec(),
                'mean_intensity': float(np.mean(intensity_data)),
                'std_intensity': float(np.std(intensity_data)),
                'min_intensity': float(np.min(intensity_data)),
                'max_intensity': float(np.max(intensity_data)),
                'point_count': len(intensity_data),
                'anomaly_count': analysis_results['anomaly_count']
            })
            
        except Exception as e:
            rospy.logerr("Error in pointcloud analysis: %s", str(e))
    
    def analyze_intensity_data(self, intensity_data):
        """分析强度值数据"""
        analysis_results = {
            'anomaly_count': 0,
            'quality_score': 0.0,
            'statistics': {},
            'recommendations': []
        }
        
        try:
            # 基础统计
            mean_val = np.mean(intensity_data)
            std_val = np.std(intensity_data)
            min_val = np.min(intensity_data)
            max_val = np.max(intensity_data)
            
            analysis_results['statistics'] = {
                'mean': float(mean_val),
                'std': float(std_val),
                'min': float(min_val),
                'max': float(max_val),
                'range': float(max_val - min_val),
                'count': len(intensity_data)
            }
            
            # 简单异常检测（基于Z-score）
            if std_val > 0:
                z_scores = np.abs((intensity_data - mean_val) / std_val)
                anomalies = np.where(z_scores > self.intensity_outlier_factor)[0]
                analysis_results['anomaly_count'] = len(anomalies)
                self.total_anomalies_detected += len(anomalies)
            
            # 基于IQR的异常检测
            q25, q75 = np.percentile(intensity_data, [25, 75])
            iqr = q75 - q25
            lower_bound = q25 - 1.5 * iqr
            upper_bound = q75 + 1.5 * iqr
            iqr_outliers = np.where(
                (intensity_data < lower_bound) | (intensity_data > upper_bound))[0]
            analysis_results['anomaly_count'] = max(analysis_results['anomaly_count'], len(iqr_outliers))
            
            # 质量评分
            quality_score = self.calculate_quality_score(analysis_results)
            analysis_results['quality_score'] = quality_score
            
            # 生成建议
            recommendations = self.generate_recommendations(analysis_results)
            analysis_results['recommendations'] = recommendations
            
        except Exception as e:
            rospy.logerr("Error in intensity analysis: %s", str(e))
        
        return analysis_results
    
    def calculate_quality_score(self, analysis_results):
        """计算质量分数"""
        try:
            stats = analysis_results['statistics']
            
            # 基础质量指标
            range_score = 1.0 if (self.expected_intensity_min <= stats['min'] and 
                                stats['max'] <= self.expected_intensity_max) else 0.5
            
            # 异常率评分
            anomaly_rate = analysis_results['anomaly_count'] / max(stats['count'], 1)
            anomaly_score = max(0.0, 1.0 - anomaly_rate * 10)  # 异常率越低分数越高
            
            # 分布评分（基于标准差）
            std_normalized = stats['std'] / max(stats['range'], 1)
            distribution_score = max(0.0, 1.0 - std_normalized)
            
            # 综合评分
            quality_score = (range_score * 0.3 + anomaly_score * 0.4 + distribution_score * 0.3)
            
            return float(quality_score)
            
        except Exception as e:
            rospy.logerr("Error calculating quality score: %s", str(e))
            return 0.0
    
    def generate_recommendations(self, analysis_results):
        """生成优化建议"""
        recommendations = []
        
        try:
            stats = analysis_results['statistics']
            quality_score = analysis_results['quality_score']
            
            if quality_score < 0.5:
                recommendations.append("Overall intensity quality is low, consider checking sensor calibration")
            
            if stats['min'] < self.expected_intensity_min:
                recommendations.append("Detected intensity values below expected minimum")
            
            if stats['max'] > self.expected_intensity_max:
                recommendations.append("Detected intensity values above expected maximum")
            
            anomaly_rate = analysis_results['anomaly_count'] / max(stats['count'], 1)
            if anomaly_rate > 0.1:
                recommendations.append("High anomaly rate detected, consider environmental factors")
            
            if stats['std'] > stats['mean'] * 0.5:
                recommendations.append("High intensity variance detected, check for interference")
            
        except Exception as e:
            rospy.logerr("Error generating recommendations: %s", str(e))
        
        return recommendations
    
    def publish_analysis_report(self, event):
        """发布分析报告"""
        try:
            if not self.intensity_history:
                return
            
            # 计算历史统计
            recent_data = list(self.intensity_history)[-10:]  # 最近10帧
            
            avg_quality = np.mean([data['anomaly_count'] for data in recent_data])
            avg_intensity = np.mean([data['mean_intensity'] for data in recent_data])
            
            # 生成报告
            report = {
                'timestamp': time.time(),
                'analysis_duration': time.time() - self.analysis_start_time,
                'total_points_analyzed': self.total_points_analyzed,
                'total_anomalies_detected': self.total_anomalies_detected,
                'recent_average_quality': float(avg_quality),
                'recent_average_intensity': float(avg_intensity),
                'analysis_window_size': len(self.intensity_history),
                'system_status': 'running',
                'recommendations': [
                    "System is running in simplified mode",
                    "For advanced analysis, install scikit-learn and scipy"
                ]
            }
            
            # 发布报告
            report_msg = String()
            # 使用安全序列化函数处理numpy类型
            safe_report = safe_json_serialize(report)
            report_msg.data = json.dumps(safe_report, indent=2)
            self.analysis_report_pub.publish(report_msg)
            
            rospy.loginfo("Published intensity analysis report - Quality: %.3f, Points: %d", 
                         avg_quality, self.total_points_analyzed)
            
        except Exception as e:
            rospy.logerr("Error publishing analysis report: %s", str(e))

def main():
    try:
        analyzer = SimpleIntensityAnalyzer()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Simple Intensity Analyzer: %s", str(e))

if __name__ == '__main__':
    main()
