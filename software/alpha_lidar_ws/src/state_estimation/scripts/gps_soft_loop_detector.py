#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS软约束回环检测器 - 深度优化版本
综合考虑GPS质量变化，不直接修改SLAM位置，通过智能引导实现回环检测
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque
import math

class GPSSoftLoopDetector:
    def __init__(self):
        rospy.init_node('gps_soft_loop_detector', anonymous=True)
        
        # GPS质量阈值
        self.gps_high_quality = rospy.get_param('~gps_high_quality_threshold', 4)
        self.gps_medium_quality = rospy.get_param('~gps_medium_quality_threshold', 1)
        
        # 不同质量GPS的接近阈值
        self.high_quality_proximity = rospy.get_param('~high_quality_proximity', 12.0)
        self.medium_quality_proximity = rospy.get_param('~medium_quality_proximity', 20.0)
        self.low_quality_proximity = rospy.get_param('~low_quality_proximity', 30.0)
        
        # SLAM轨迹相似度阈值
        self.slam_strict_threshold = rospy.get_param('~slam_strict_threshold', 6.0)
        self.slam_normal_threshold = rospy.get_param('~slam_normal_threshold', 12.0)
        self.slam_loose_threshold = rospy.get_param('~slam_loose_threshold', 20.0)
        
        # 其他参数
        self.min_time_gap = rospy.get_param('~min_time_gap', 25.0)
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 40.0)
        self.heading_threshold = rospy.get_param('~heading_threshold', 40.0)
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=1500)
        self.slam_trajectory = deque(maxlen=1500)
        self.quality_history = deque(maxlen=80)
        
        # 状态变量
        self.last_detection_time = 0
        self.total_detections = 0
        self.quality_stats = {'high': 0, 'medium': 0, 'low': 0, 'poor': 0}
        
        # GPS原点
        self.origin_set = False
        self.origin_lat = 0.0
        self.origin_lon = 0.0
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        
        # 发布器
        self.trigger_pub = rospy.Publisher('/gps_soft_loop_trigger', String, queue_size=1)
        self.confidence_pub = rospy.Publisher('/loop_confidence_score', Float64, queue_size=1)
        self.status_pub = rospy.Publisher('/gps_soft_loop_status', String, queue_size=1)
        self.quality_pub = rospy.Publisher('/gps_quality_report', String, queue_size=1)
        
        # 定时器
        self.detection_timer = rospy.Timer(rospy.Duration(1.2), self.detect_loops)
        self.status_timer = rospy.Timer(rospy.Duration(6.0), self.publish_status)
        
        rospy.loginfo("🎯 GPS Soft Loop Detector Started")
        rospy.loginfo("Quality thresholds - High: %d, Medium: %d", self.gps_high_quality, self.gps_medium_quality)
        rospy.loginfo("Proximity thresholds - High: %.1fm, Medium: %.1fm, Low: %.1fm", 
                     self.high_quality_proximity, self.medium_quality_proximity, self.low_quality_proximity)
    
    def classify_gps_quality(self, status):
        """GPS质量分类"""
        if status >= self.gps_high_quality:
            return 'high'
        elif status >= self.gps_medium_quality:
            return 'medium'
        elif status >= 0:
            return 'low'
        else:
            return 'poor'
    
    def get_thresholds(self, quality):
        """根据GPS质量获取阈值"""
        if quality == 'high':
            return self.high_quality_proximity, self.slam_strict_threshold
        elif quality == 'medium':
            return self.medium_quality_proximity, self.slam_normal_threshold
        else:
            return self.low_quality_proximity, self.slam_loose_threshold
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        with self.lock:
            current_time = rospy.Time.now().to_sec()
            
            # 设置GPS原点
            if not self.origin_set:
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                self.origin_set = True
                rospy.loginfo("🏁 GPS Origin: lat=%.6f, lon=%.6f", self.origin_lat, self.origin_lon)
            
            # 坐标转换
            local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            quality = self.classify_gps_quality(msg.status.status)
            
            gps_point = {
                'x': local_x, 'y': local_y, 'timestamp': current_time,
                'quality': quality, 'status': msg.status.status
            }
            
            self.gps_trajectory.append(gps_point)
            self.quality_history.append(quality)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x, 'y': msg.pose.position.y, 'z': msg.pose.position.z,
                'timestamp': current_time,
                'qx': msg.pose.orientation.x, 'qy': msg.pose.orientation.y,
                'qz': msg.pose.orientation.z, 'qw': msg.pose.orientation.w
            }
            
            self.slam_trajectory.append(slam_point)
    
    def gps_to_local(self, lat, lon):
        """GPS坐标转换"""
        if not self.origin_set:
            return 0.0, 0.0
        
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 高精度转换
        lat_rad = math.radians(self.origin_lat)
        R_lat = 6378137.0 / (1 - 0.00669437999014 * math.sin(lat_rad)**2)**0.5
        R_lon = R_lat * math.cos(lat_rad)
        
        x = lon_diff * R_lon * math.pi / 180.0
        y = lat_diff * R_lat * math.pi / 180.0
        
        return x, y
    
    def calculate_distance(self, p1, p2):
        """计算距离"""
        dx = p1['x'] - p2['x']
        dy = p1['y'] - p2['y']
        return math.sqrt(dx*dx + dy*dy)
    
    def calculate_heading_diff(self, pose1, pose2):
        """计算航向差异"""
        def quat_to_yaw(qx, qy, qz, qw):
            return math.atan2(2.0 * (qw * qz + qx * qy), 1.0 - 2.0 * (qy * qy + qz * qz))
        
        yaw1 = quat_to_yaw(pose1['qx'], pose1['qy'], pose1['qz'], pose1['qw'])
        yaw2 = quat_to_yaw(pose2['qx'], pose2['qy'], pose2['qz'], pose2['qw'])
        
        diff = abs(yaw1 - yaw2)
        diff = min(diff, 2*math.pi - diff)
        return math.degrees(diff)
    
    def detect_loops(self, event):
        """检测回环机会"""
        try:
            with self.lock:
                current_time = rospy.Time.now().to_sec()
                
                # 基本检查
                if (len(self.gps_trajectory) < 15 or len(self.slam_trajectory) < 15 or
                    current_time - self.last_detection_time < self.min_time_gap):
                    return
                
                # 轨迹长度检查
                if self.get_trajectory_length() < self.min_trajectory_length:
                    return
                
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                current_quality = current_gps['quality']
                
                # 获取阈值
                gps_threshold, slam_threshold = self.get_thresholds(current_quality)
                
                # 寻找候选
                candidates = self.find_candidates(current_gps, current_slam, gps_threshold, slam_threshold)
                
                if candidates:
                    best = self.select_best_candidate(current_slam, candidates)
                    if best:
                        self.trigger_loop_closure(current_slam, best, current_quality)
                        
        except Exception as e:
            rospy.logerr("Loop detection error: %s", str(e))
    
    def find_candidates(self, current_gps, current_slam, gps_threshold, slam_threshold):
        """寻找回环候选"""
        candidates = []
        current_time = current_gps['timestamp']
        
        for i, gps_point in enumerate(self.gps_trajectory[:-15]):
            # 时间检查
            if current_time - gps_point['timestamp'] < self.min_time_gap:
                continue
            
            # GPS距离检查
            gps_dist = self.calculate_distance(current_gps, gps_point)
            if gps_dist > gps_threshold:
                continue
            
            # SLAM距离检查
            if i >= len(self.slam_trajectory):
                continue
            
            historical_slam = self.slam_trajectory[i]
            slam_dist = self.calculate_distance(current_slam, historical_slam)
            if slam_dist > slam_threshold:
                continue
            
            # 航向检查
            heading_diff = self.calculate_heading_diff(current_slam, historical_slam)
            if heading_diff > self.heading_threshold:
                continue
            
            # 质量权重
            quality_weight = self.get_quality_weight(gps_point['quality'], current_gps['quality'])
            
            candidates.append({
                'gps_point': gps_point, 'slam_point': historical_slam, 'index': i,
                'gps_distance': gps_dist, 'slam_distance': slam_dist,
                'heading_diff': heading_diff, 'time_gap': current_time - gps_point['timestamp'],
                'quality_weight': quality_weight
            })
        
        return candidates
    
    def get_quality_weight(self, quality1, quality2):
        """计算质量权重"""
        weights = {'high': 1.0, 'medium': 0.7, 'low': 0.4, 'poor': 0.2}
        return (weights.get(quality1, 0.2) + weights.get(quality2, 0.2)) / 2.0
    
    def select_best_candidate(self, current_slam, candidates):
        """选择最佳候选"""
        if not candidates:
            return None
        
        best = None
        best_score = float('inf')
        
        for candidate in candidates:
            # 综合评分
            distance_score = candidate['slam_distance'] / self.slam_loose_threshold
            heading_score = candidate['heading_diff'] / self.heading_threshold
            time_score = min(candidate['time_gap'] / 180.0, 1.0)  # 3分钟最佳
            
            total_score = (distance_score * 0.4 + heading_score * 0.3 + time_score * 0.3) / candidate['quality_weight']
            
            if total_score < best_score:
                best_score = total_score
                best = candidate
                best['confidence'] = 1.0 / (1.0 + total_score)
        
        return best
    
    def trigger_loop_closure(self, current_pose, best_candidate, quality):
        """触发回环检测"""
        try:
            self.last_detection_time = rospy.Time.now().to_sec()
            self.total_detections += 1
            self.quality_stats[quality] += 1
            
            # 构建消息
            loop_data = {
                'type': 'gps_soft_constraint_loop_closure',
                'timestamp': self.last_detection_time,
                'current_pose': {'x': current_pose['x'], 'y': current_pose['y'], 'z': current_pose['z']},
                'target_pose': {'x': best_candidate['slam_point']['x'], 
                               'y': best_candidate['slam_point']['y'], 
                               'z': best_candidate['slam_point']['z']},
                'metrics': {
                    'gps_distance': best_candidate['gps_distance'],
                    'slam_distance': best_candidate['slam_distance'],
                    'heading_diff': best_candidate['heading_diff'],
                    'time_gap': best_candidate['time_gap'],
                    'gps_quality': quality,
                    'confidence': best_candidate['confidence']
                }
            }
            
            # 发布消息
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.trigger_pub.publish(trigger_msg)
            
            confidence_msg = Float64()
            confidence_msg.data = best_candidate['confidence']
            self.confidence_pub.publish(confidence_msg)
            
            rospy.loginfo("🎯 GPS Soft Loop Triggered!")
            rospy.loginfo("Quality: %s, GPS: %.2fm, SLAM: %.2fm, Confidence: %.3f", 
                         quality, best_candidate['gps_distance'], 
                         best_candidate['slam_distance'], best_candidate['confidence'])
            
        except Exception as e:
            rospy.logerr("Error triggering loop: %s", str(e))
    
    def get_trajectory_length(self):
        """计算轨迹长度"""
        if len(self.slam_trajectory) < 2:
            return 0.0
        
        length = 0.0
        for i in range(1, len(self.slam_trajectory)):
            length += self.calculate_distance(self.slam_trajectory[i-1], self.slam_trajectory[i])
        return length
    
    def publish_status(self, event):
        """发布状态"""
        try:
            with self.lock:
                # 质量分析
                if len(self.quality_history) > 5:
                    quality_counts = {'high': 0, 'medium': 0, 'low': 0, 'poor': 0}
                    for q in self.quality_history:
                        quality_counts[q] += 1
                    
                    total = len(self.quality_history)
                    quality_percentages = {k: (v/total)*100 for k, v in quality_counts.items()}
                    
                    quality_report = {
                        'timestamp': rospy.Time.now().to_sec(),
                        'quality_distribution': quality_percentages,
                        'recent_quality': list(self.quality_history)[-10:]
                    }
                    
                    quality_msg = String()
                    quality_msg.data = json.dumps(quality_report)
                    self.quality_pub.publish(quality_msg)
                
                # 状态报告
                status = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'total_detections': self.total_detections,
                    'quality_stats': self.quality_stats,
                    'gps_points': len(self.gps_trajectory),
                    'slam_points': len(self.slam_trajectory),
                    'trajectory_length': self.get_trajectory_length()
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status)
                self.status_pub.publish(status_msg)
                
                rospy.loginfo("GPS Soft Loop: %d detections, %.1fm trajectory", 
                             self.total_detections, self.get_trajectory_length())
                
        except Exception as e:
            rospy.logerr("Status error: %s", str(e))

def main():
    try:
        detector = GPSSoftLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("GPS Soft Loop Detector error: %s", str(e))

if __name__ == '__main__':
    main()
