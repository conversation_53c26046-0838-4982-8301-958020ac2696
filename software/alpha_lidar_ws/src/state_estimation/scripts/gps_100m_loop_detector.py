#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 100米回环检测器
专门检测GPS当前位置距离起点100米以内时触发SLAM回环匹配
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque
import math

class GPS100mLoopDetector:
    def __init__(self):
        rospy.init_node('gps_100m_loop_detector', anonymous=True)
        
        # 100米回环检测参数
        self.start_point_threshold = rospy.get_param('~start_point_threshold', 100.0)  # 距离起点100米阈值
        self.min_departure_distance = rospy.get_param('~min_departure_distance', 150.0)  # 最小离开距离150米
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 200.0)  # 最小轨迹长度200米
        self.loop_detection_cooldown = rospy.get_param('~loop_detection_cooldown', 30.0)  # 回环检测冷却时间30秒
        
        # GPS质量权重
        self.gps_quality_weights = {
            4: 1.0,    # RTK固定解
            3: 0.9,    # RTK浮点解
            2: 0.7,    # DGPS
            1: 0.5,    # GPS单点定位
            0: 0.3,    # 无效
            -1: 0.2    # 质量未知
        }
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=3000)
        self.slam_trajectory = deque(maxlen=3000)
        
        # 状态变量
        self.gps_origin = None
        self.origin_set = False
        self.has_departed = False  # 是否已经离开起点
        self.max_distance_from_start = 0.0  # 距离起点的最大距离
        self.last_loop_detection_time = 0
        self.total_loop_detections = 0
        self.current_trajectory_length = 0.0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        
        # 发布器
        self.loop_trigger_pub = rospy.Publisher('/gps_100m_loop_trigger', String, queue_size=1)
        self.distance_status_pub = rospy.Publisher('/gps_distance_to_start', Float64, queue_size=1)
        self.loop_status_pub = rospy.Publisher('/gps_100m_loop_status', String, queue_size=1)
        self.force_loop_pub = rospy.Publisher('/intelligent_force_loop_closure', String, queue_size=1)
        
        # 定时器
        self.detection_timer = rospy.Timer(rospy.Duration(2.0), self.check_100m_loop_opportunity)
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("GPS 100m Loop Detector Started")
        rospy.loginfo("Start point threshold: %.1f m, Min departure: %.1f m", 
                     self.start_point_threshold, self.min_departure_distance)
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # 获取GPS质量权重
            gps_quality = msg.status.status
            quality_weight = self.gps_quality_weights.get(gps_quality, 0.1)
            
            # 设置GPS起点
            if not self.origin_set:
                self.gps_origin = {
                    'lat': msg.latitude,
                    'lon': msg.longitude,
                    'x': 0.0,
                    'y': 0.0,
                    'timestamp': current_time,
                    'quality': gps_quality
                }
                self.origin_set = True
                local_x, local_y = 0.0, 0.0
                rospy.loginfo("🏁 GPS起点已设置: lat=%.6f, lon=%.6f, 质量=%d", 
                             msg.latitude, msg.longitude, gps_quality)
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            # 计算距离起点的距离
            distance_to_start = math.sqrt(local_x*local_x + local_y*local_y)
            
            # 更新最大距离
            if distance_to_start > self.max_distance_from_start:
                self.max_distance_from_start = distance_to_start
            
            # 检查是否已经离开起点
            if not self.has_departed and distance_to_start > self.min_departure_distance:
                self.has_departed = True
                rospy.loginfo("🚀 已离开起点 %.1f 米，开始监控100米回环", distance_to_start)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'timestamp': current_time,
                'quality': gps_quality,
                'quality_weight': quality_weight,
                'distance_to_start': distance_to_start,
                'lat': msg.latitude,
                'lon': msg.longitude
            }
            
            self.gps_trajectory.append(gps_point)
            
            # 发布距离起点的距离
            distance_msg = Float64()
            distance_msg.data = distance_to_start
            self.distance_status_pub.publish(distance_msg)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            self.slam_trajectory.append(slam_point)
            
            # 更新轨迹长度
            if len(self.slam_trajectory) >= 2:
                prev_point = self.slam_trajectory[-2]
                distance = self.calculate_distance(slam_point, prev_point)
                self.current_trajectory_length += distance
    
    def gps_to_local(self, lat, lon):
        """GPS坐标转换为局部坐标"""
        lat_diff = lat - self.gps_origin['lat']
        lon_diff = lon - self.gps_origin['lon']
        
        # 精确坐标转换
        x = lon_diff * 111320.0 * math.cos(math.radians(self.gps_origin['lat']))
        y = lat_diff * 110540.0
        
        return x, y
    
    def calculate_distance(self, point1, point2):
        """计算两点间距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        return math.sqrt(dx*dx + dy*dy)
    
    def check_100m_loop_opportunity(self, event):
        """检查100米回环机会"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查基本条件
                if not self.origin_set or not self.has_departed:
                    return
                
                if len(self.gps_trajectory) < 10 or len(self.slam_trajectory) < 10:
                    return
                
                # 检查冷却时间
                if current_time - self.last_loop_detection_time < self.loop_detection_cooldown:
                    return
                
                # 检查轨迹长度
                if self.current_trajectory_length < self.min_trajectory_length:
                    return
                
                # 获取当前GPS位置
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 检查是否在100米范围内
                distance_to_start = current_gps['distance_to_start']
                
                if distance_to_start <= self.start_point_threshold:
                    # 确保已经离开过起点足够远
                    if self.max_distance_from_start >= self.min_departure_distance:
                        self.trigger_100m_loop_closure(current_gps, current_slam, distance_to_start)
                        
        except Exception as e:
            rospy.logerr("Error checking 100m loop opportunity: %s", str(e))
    
    def trigger_100m_loop_closure(self, current_gps, current_slam, distance_to_start):
        """触发100米回环检测"""
        try:
            self.last_loop_detection_time = rospy.Time.now().to_sec()
            self.total_loop_detections += 1
            
            # 计算回环置信度
            confidence = self.calculate_loop_confidence(current_gps, distance_to_start)
            
            # 构建回环触发消息
            loop_data = {
                'type': 'gps_100m_loop_closure',
                'timestamp': self.last_loop_detection_time,
                'distance_to_start': distance_to_start,
                'max_distance_traveled': self.max_distance_from_start,
                'trajectory_length': self.current_trajectory_length,
                'gps_quality': current_gps['quality'],
                'quality_weight': current_gps['quality_weight'],
                'confidence': confidence,
                'current_position': {
                    'gps': {'x': current_gps['x'], 'y': current_gps['y']},
                    'slam': {'x': current_slam['x'], 'y': current_slam['y'], 'z': current_slam['z']}
                },
                'start_position': {
                    'gps': {'x': self.gps_origin['x'], 'y': self.gps_origin['y']},
                    'lat': self.gps_origin['lat'],
                    'lon': self.gps_origin['lon']
                }
            }
            
            # 发布回环触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.loop_trigger_pub.publish(trigger_msg)
            
            # 如果置信度足够高，触发强制回环
            if confidence >= 0.6:
                force_data = {
                    'type': 'gps_100m_return_to_start',
                    'distance_to_start': distance_to_start,
                    'confidence': confidence,
                    'gps_quality': current_gps['quality'],
                    'max_distance': self.max_distance_from_start,
                    'reason': 'gps_100m_loop_detection'
                }
                
                force_msg = String()
                force_msg.data = json.dumps(force_data)
                self.force_loop_pub.publish(force_msg)
                
                rospy.loginfo("🔥 高置信度100米回环触发强制匹配!")
            
            # 详细日志输出
            rospy.loginfo("=" * 70)
            rospy.loginfo("🎯 GPS 100米回环检测触发!")
            rospy.loginfo("=" * 70)
            rospy.loginfo("📍 位置信息:")
            rospy.loginfo("   距离起点: %.2f 米 (阈值: %.1f 米)", distance_to_start, self.start_point_threshold)
            rospy.loginfo("   最大离开距离: %.2f 米", self.max_distance_from_start)
            rospy.loginfo("   轨迹总长度: %.2f 米", self.current_trajectory_length)
            rospy.loginfo("")
            rospy.loginfo("📡 GPS信息:")
            rospy.loginfo("   当前GPS质量: %d (权重: %.2f)", current_gps['quality'], current_gps['quality_weight'])
            rospy.loginfo("   GPS坐标: (%.2f, %.2f)", current_gps['x'], current_gps['y'])
            rospy.loginfo("")
            rospy.loginfo("🔄 SLAM信息:")
            rospy.loginfo("   SLAM位置: (%.2f, %.2f, %.2f)", current_slam['x'], current_slam['y'], current_slam['z'])
            rospy.loginfo("")
            rospy.loginfo("📊 回环评估:")
            rospy.loginfo("   回环置信度: %.3f", confidence)
            rospy.loginfo("   总检测次数: %d", self.total_loop_detections)
            rospy.loginfo("=" * 70)
            
        except Exception as e:
            rospy.logerr("Error triggering 100m loop closure: %s", str(e))
    
    def calculate_loop_confidence(self, current_gps, distance_to_start):
        """计算回环置信度"""
        # 基础置信度
        base_confidence = 0.5
        
        # 距离因子 (距离越近置信度越高)
        distance_factor = max(0, (self.start_point_threshold - distance_to_start) / self.start_point_threshold) * 0.3
        
        # GPS质量因子
        quality_factor = current_gps['quality_weight'] * 0.2
        
        # 轨迹长度因子 (轨迹越长置信度越高)
        length_factor = min(self.current_trajectory_length / 1000.0, 1.0) * 0.15
        
        # 离开距离因子 (离开越远再回来置信度越高)
        departure_factor = min(self.max_distance_from_start / 500.0, 1.0) * 0.15
        
        total_confidence = base_confidence + distance_factor + quality_factor + length_factor + departure_factor
        
        return min(1.0, max(0.0, total_confidence))
    
    def publish_status(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                current_distance = self.gps_trajectory[-1]['distance_to_start'] if self.gps_trajectory else 0.0
                
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'detector_type': 'gps_100m_loop_detector',
                    'status': {
                        'origin_set': self.origin_set,
                        'has_departed': self.has_departed,
                        'current_distance_to_start': current_distance,
                        'max_distance_from_start': self.max_distance_from_start,
                        'trajectory_length': self.current_trajectory_length,
                        'total_loop_detections': self.total_loop_detections
                    },
                    'parameters': {
                        'start_point_threshold': self.start_point_threshold,
                        'min_departure_distance': self.min_departure_distance,
                        'min_trajectory_length': self.min_trajectory_length
                    },
                    'loop_opportunity': {
                        'within_100m': current_distance <= self.start_point_threshold,
                        'has_departed_enough': self.max_distance_from_start >= self.min_departure_distance,
                        'trajectory_long_enough': self.current_trajectory_length >= self.min_trajectory_length,
                        'ready_for_loop': (current_distance <= self.start_point_threshold and 
                                         self.max_distance_from_start >= self.min_departure_distance and
                                         self.current_trajectory_length >= self.min_trajectory_length)
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.loop_status_pub.publish(status_msg)
                
                # 简化日志
                if self.origin_set:
                    rospy.loginfo("GPS 100m检测器: 距起点%.1fm, 最大%.1fm, 轨迹%.1fm, 检测%d次", 
                                 current_distance, self.max_distance_from_start, 
                                 self.current_trajectory_length, self.total_loop_detections)
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        detector = GPS100mLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS 100m Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
