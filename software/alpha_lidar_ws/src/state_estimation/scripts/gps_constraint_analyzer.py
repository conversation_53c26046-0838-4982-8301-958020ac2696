#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS平面约束分析器
诊断GPS平面约束导致SLAM点云匹配错误的问题
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
from sensor_msgs.msg import NavSatFix, PointCloud2
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import String, Float64MultiArray
import json
import threading
from collections import deque
import time

class GPSConstraintAnalyzer:
    def __init__(self):
        rospy.init_node('gps_constraint_analyzer', anonymous=True)
        
        # 数据存储
        self.gps_data = deque(maxlen=1000)
        self.slam_poses = deque(maxlen=1000)
        self.constraint_corrections = deque(maxlen=1000)
        self.matching_errors = deque(maxlen=1000)
        
        # 分析参数
        self.analysis_window = 50
        self.error_threshold = 0.5
        self.time_sync_threshold = 0.1  # 100ms
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 统计信息
        self.total_constraints_applied = 0
        self.total_matching_errors = 0
        self.constraint_error_correlation = 0.0
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        self.pointcloud_sub = rospy.Subscriber('/cloud_registered', PointCloud2, self.pointcloud_callback, queue_size=1)
        
        # 发布器
        self.analysis_pub = rospy.Publisher('/gps_constraint_analysis', String, queue_size=1)
        self.recommendation_pub = rospy.Publisher('/gps_constraint_recommendations', String, queue_size=1)
        
        # 定时器
        self.analysis_timer = rospy.Timer(rospy.Duration(10.0), self.perform_analysis)
        
        rospy.loginfo("GPS Constraint Analyzer Started")
    
    def gps_callback(self, msg):
        """GPS数据回调"""
        with self.data_lock:
            gps_data = {
                'timestamp': msg.header.stamp.to_sec(),
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status,
                'covariance': list(msg.position_covariance) if len(msg.position_covariance) > 0 else None
            }
            self.gps_data.append(gps_data)
    
    def pose_callback(self, msg):
        """SLAM位姿回调"""
        with self.data_lock:
            pose_data = {
                'timestamp': msg.header.stamp.to_sec(),
                'position': [msg.pose.position.x, msg.pose.position.y, msg.pose.position.z],
                'orientation': [msg.pose.orientation.x, msg.pose.orientation.y, 
                              msg.pose.orientation.z, msg.pose.orientation.w]
            }
            self.slam_poses.append(pose_data)
    
    def pointcloud_callback(self, msg):
        """点云数据回调 - 用于检测匹配质量"""
        # 简化的匹配质量评估
        current_time = msg.header.stamp.to_sec()
        
        # 这里可以添加更复杂的匹配质量评估逻辑
        # 目前使用简单的时间间隔作为质量指标
        if len(self.matching_errors) > 0:
            time_diff = current_time - self.matching_errors[-1]['timestamp']
            if time_diff < 0.05:  # 如果处理太快，可能是匹配有问题
                error_data = {
                    'timestamp': current_time,
                    'error_type': 'fast_processing',
                    'error_value': time_diff
                }
                self.matching_errors.append(error_data)
                self.total_matching_errors += 1
    
    def perform_analysis(self, event):
        """执行GPS约束分析"""
        with self.data_lock:
            if len(self.gps_data) < 10 or len(self.slam_poses) < 10:
                return
            
            analysis_results = {
                'timestamp': time.time(),
                'data_quality': self.analyze_data_quality(),
                'time_synchronization': self.analyze_time_sync(),
                'constraint_impact': self.analyze_constraint_impact(),
                'coordinate_consistency': self.analyze_coordinate_consistency(),
                'recommendations': self.generate_recommendations()
            }
            
            # 发布分析结果
            analysis_msg = String()
            analysis_msg.data = json.dumps(analysis_results, indent=2)
            self.analysis_pub.publish(analysis_msg)
            
            # 发布建议
            recommendations_msg = String()
            recommendations_msg.data = json.dumps(analysis_results['recommendations'], indent=2)
            self.recommendation_pub.publish(recommendations_msg)
            
            # 控制台输出
            self.print_analysis_summary(analysis_results)
    
    def analyze_data_quality(self):
        """分析数据质量"""
        recent_gps = list(self.gps_data)[-self.analysis_window:]
        
        # GPS状态分析
        status_counts = {}
        for gps in recent_gps:
            status = gps['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 数据连续性分析
        time_gaps = []
        for i in range(1, len(recent_gps)):
            gap = recent_gps[i]['timestamp'] - recent_gps[i-1]['timestamp']
            time_gaps.append(gap)
        
        return {
            'gps_status_distribution': status_counts,
            'average_time_gap': np.mean(time_gaps) if time_gaps else 0,
            'max_time_gap': np.max(time_gaps) if time_gaps else 0,
            'data_continuity_score': 1.0 - (np.std(time_gaps) if time_gaps else 0)
        }
    
    def analyze_time_sync(self):
        """分析时间同步问题"""
        recent_gps = list(self.gps_data)[-20:]
        recent_poses = list(self.slam_poses)[-20:]
        
        if len(recent_gps) < 5 or len(recent_poses) < 5:
            return {'sync_quality': 'insufficient_data'}
        
        # 找到时间最接近的GPS和SLAM数据对
        sync_errors = []
        for pose in recent_poses:
            pose_time = pose['timestamp']
            closest_gps = min(recent_gps, key=lambda g: abs(g['timestamp'] - pose_time))
            time_diff = abs(closest_gps['timestamp'] - pose_time)
            sync_errors.append(time_diff)
        
        avg_sync_error = np.mean(sync_errors)
        max_sync_error = np.max(sync_errors)
        
        return {
            'average_sync_error': avg_sync_error,
            'max_sync_error': max_sync_error,
            'sync_quality': 'good' if avg_sync_error < self.time_sync_threshold else 'poor',
            'sync_errors': sync_errors
        }
    
    def analyze_constraint_impact(self):
        """分析约束对SLAM的影响"""
        recent_poses = list(self.slam_poses)[-self.analysis_window:]
        
        if len(recent_poses) < 10:
            return {'impact_analysis': 'insufficient_data'}
        
        # 计算位置变化的平滑度
        position_changes = []
        for i in range(1, len(recent_poses)):
            pos1 = np.array(recent_poses[i-1]['position'])
            pos2 = np.array(recent_poses[i]['position'])
            change = np.linalg.norm(pos2 - pos1)
            position_changes.append(change)
        
        # 检测异常跳跃
        mean_change = np.mean(position_changes)
        std_change = np.std(position_changes)
        anomaly_threshold = mean_change + 2 * std_change
        
        anomalies = [change for change in position_changes if change > anomaly_threshold]
        
        return {
            'average_position_change': mean_change,
            'position_change_std': std_change,
            'anomaly_count': len(anomalies),
            'anomaly_ratio': len(anomalies) / len(position_changes),
            'trajectory_smoothness': 1.0 / (1.0 + std_change)
        }
    
    def analyze_coordinate_consistency(self):
        """分析坐标系一致性"""
        recent_gps = list(self.gps_data)[-20:]
        recent_poses = list(self.slam_poses)[-20:]
        
        if len(recent_gps) < 5 or len(recent_poses) < 5:
            return {'consistency': 'insufficient_data'}
        
        # 简化的坐标一致性检查
        # 检查GPS和SLAM轨迹的相对变化是否一致
        gps_changes = []
        slam_changes = []
        
        for i in range(1, min(len(recent_gps), len(recent_poses))):
            # GPS变化（简化为高度变化）
            gps_alt_change = recent_gps[i]['altitude'] - recent_gps[i-1]['altitude']
            gps_changes.append(gps_alt_change)
            
            # SLAM Z轴变化
            slam_z_change = recent_poses[i]['position'][2] - recent_poses[i-1]['position'][2]
            slam_changes.append(slam_z_change)
        
        if len(gps_changes) > 3 and len(slam_changes) > 3:
            correlation = np.corrcoef(gps_changes, slam_changes)[0, 1] if len(gps_changes) == len(slam_changes) else 0
        else:
            correlation = 0
        
        return {
            'height_correlation': correlation,
            'consistency_score': abs(correlation),
            'coordinate_alignment': 'good' if abs(correlation) > 0.5 else 'poor'
        }
    
    def generate_recommendations(self):
        """生成优化建议"""
        recommendations = []
        
        # 基于分析结果生成建议
        data_quality = self.analyze_data_quality()
        time_sync = self.analyze_time_sync()
        constraint_impact = self.analyze_constraint_impact()
        coord_consistency = self.analyze_coordinate_consistency()
        
        # GPS数据质量建议
        if data_quality.get('data_continuity_score', 0) < 0.8:
            recommendations.append({
                'type': 'data_quality',
                'priority': 'high',
                'issue': 'GPS data discontinuity detected',
                'solution': 'Check GPS antenna and receiver, consider increasing GPS buffer size'
            })
        
        # 时间同步建议
        if time_sync.get('sync_quality') == 'poor':
            recommendations.append({
                'type': 'time_sync',
                'priority': 'high',
                'issue': 'Poor time synchronization between GPS and SLAM',
                'solution': 'Disable GPS plane constraint or increase time sync threshold'
            })
        
        # 约束影响建议
        if constraint_impact.get('anomaly_ratio', 0) > 0.1:
            recommendations.append({
                'type': 'constraint_impact',
                'priority': 'medium',
                'issue': 'GPS constraints causing trajectory anomalies',
                'solution': 'Reduce GPS constraint weight or increase correction threshold'
            })
        
        # 坐标一致性建议
        if coord_consistency.get('coordinate_alignment') == 'poor':
            recommendations.append({
                'type': 'coordinate_consistency',
                'priority': 'high',
                'issue': 'Poor coordinate system alignment',
                'solution': 'Check GPS coordinate transformation and SLAM initialization'
            })
        
        # 如果没有发现问题
        if not recommendations:
            recommendations.append({
                'type': 'status',
                'priority': 'info',
                'issue': 'No major issues detected',
                'solution': 'GPS constraint system appears to be working normally'
            })
        
        return recommendations
    
    def print_analysis_summary(self, results):
        """打印分析摘要"""
        rospy.loginfo("=== GPS Constraint Analysis Summary ===")
        
        # 数据质量
        data_quality = results['data_quality']
        rospy.loginfo("Data Quality Score: %.3f", data_quality.get('data_continuity_score', 0))
        
        # 时间同步
        time_sync = results['time_synchronization']
        if isinstance(time_sync, dict) and 'average_sync_error' in time_sync:
            rospy.loginfo("Time Sync Error: %.3f seconds", time_sync['average_sync_error'])
        
        # 约束影响
        constraint_impact = results['constraint_impact']
        if isinstance(constraint_impact, dict) and 'anomaly_ratio' in constraint_impact:
            rospy.loginfo("Trajectory Anomaly Ratio: %.3f", constraint_impact['anomaly_ratio'])
        
        # 建议数量
        recommendations = results['recommendations']
        high_priority = len([r for r in recommendations if r.get('priority') == 'high'])
        rospy.loginfo("High Priority Issues: %d", high_priority)
        
        rospy.loginfo("========================================")

def main():
    try:
        analyzer = GPSConstraintAnalyzer()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS Constraint Analyzer: %s", str(e))

if __name__ == '__main__':
    main()
