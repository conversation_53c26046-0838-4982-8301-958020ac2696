#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强度值质量监控脚本
监控点云强度值的保持质量和统计信息
"""

import rospy
import numpy as np
from sensor_msgs.msg import PointCloud2
from std_msgs.msg import String
import sensor_msgs.point_cloud2 as pc2
import json
import time
from collections import deque

def safe_json_serialize(obj):
    """安全的JSON序列化，处理numpy类型"""
    if isinstance(obj, dict):
        return {k: safe_json_serialize(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_json_serialize(v) for v in obj]
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)) or str(type(obj)).find('bool') != -1:
        return bool(obj)
    elif isinstance(obj, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64)) or str(type(obj)).find('int') != -1:
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)) or str(type(obj)).find('float') != -1:
        return float(obj)
    else:
        return obj

class IntensityQualityMonitor:
    def __init__(self):
        rospy.init_node('intensity_quality_monitor', anonymous=True)
        
        # 参数
        self.monitor_topic = rospy.get_param('~monitor_topic', '/enhanced_pointcloud_with_intensity')
        self.statistics_topic = rospy.get_param('~statistics_topic', '/intensity_statistics')
        self.quality_report_topic = rospy.get_param('~quality_report_topic', '/pointcloud_quality_report')
        
        # 监控参数
        self.report_interval = rospy.get_param('~report_interval', 10.0)  # 秒
        self.history_window_size = rospy.get_param('~history_window_size', 100)
        self.intensity_range_min = rospy.get_param('~intensity_range_min', 0.0)
        self.intensity_range_max = rospy.get_param('~intensity_range_max', 255.0)
        
        # 数据存储
        self.intensity_history = deque(maxlen=self.history_window_size)
        self.point_count_history = deque(maxlen=self.history_window_size)
        self.timestamp_history = deque(maxlen=self.history_window_size)
        
        # 统计信息
        self.total_points_processed = 0
        self.total_frames_processed = 0
        self.start_time = time.time()
        
        # 质量指标
        self.quality_metrics = {
            'intensity_preservation_rate': 0.0,
            'intensity_range_compliance': 0.0,
            'point_density_stability': 0.0,
            'processing_consistency': 0.0,
            'overall_quality_score': 0.0
        }
        
        # 订阅器
        self.pointcloud_sub = rospy.Subscriber(
            self.monitor_topic, PointCloud2, self.pointcloud_callback, queue_size=1)
        
        # 发布器
        self.quality_report_pub = rospy.Publisher(
            self.quality_report_topic, String, queue_size=1)
        
        # 定时器
        self.report_timer = rospy.Timer(
            rospy.Duration(self.report_interval), self.publish_quality_report)
        
        rospy.loginfo("Intensity Quality Monitor Started")
        rospy.loginfo("Monitoring topic: %s", self.monitor_topic)
        rospy.loginfo("Report interval: %.1f seconds", self.report_interval)
    
    def pointcloud_callback(self, msg):
        """处理点云消息"""
        try:
            # 解析点云数据
            points = list(pc2.read_points(msg, field_names=("x", "y", "z", "intensity"), skip_nans=True))
            
            if not points:
                rospy.logwarn("Received empty pointcloud")
                return
            
            # 提取强度值
            intensities = [point[3] for point in points]
            intensities = np.array(intensities)
            
            # 更新历史数据
            self.intensity_history.append(intensities)
            self.point_count_history.append(len(points))
            self.timestamp_history.append(msg.header.stamp.to_sec())
            
            # 更新统计
            self.total_points_processed += len(points)
            self.total_frames_processed += 1
            
            # 计算质量指标
            self.calculate_quality_metrics()
            
        except Exception as e:
            rospy.logerr("Error processing pointcloud: %s", str(e))
    
    def calculate_quality_metrics(self):
        """计算质量指标"""
        if len(self.intensity_history) < 2:
            return
        
        current_intensities = self.intensity_history[-1]
        
        # 1. 强度值保持率
        valid_intensities = current_intensities[
            (current_intensities >= self.intensity_range_min) & 
            (current_intensities <= self.intensity_range_max)
        ]
        self.quality_metrics['intensity_preservation_rate'] = len(valid_intensities) / len(current_intensities)
        
        # 2. 强度值范围合规性
        in_range_count = np.sum(
            (current_intensities >= self.intensity_range_min) & 
            (current_intensities <= self.intensity_range_max)
        )
        self.quality_metrics['intensity_range_compliance'] = in_range_count / len(current_intensities)
        
        # 3. 点密度稳定性
        if len(self.point_count_history) >= 10:
            recent_counts = list(self.point_count_history)[-10:]
            mean_count = np.mean(recent_counts)
            std_count = np.std(recent_counts)
            if mean_count > 0:
                stability = 1.0 - (std_count / mean_count)
                self.quality_metrics['point_density_stability'] = max(0.0, stability)
        
        # 4. 处理一致性
        if len(self.intensity_history) >= 5:
            recent_intensities = list(self.intensity_history)[-5:]
            mean_intensities = [np.mean(intensities) for intensities in recent_intensities]
            if len(mean_intensities) > 1:
                consistency = 1.0 - (np.std(mean_intensities) / np.mean(mean_intensities))
                self.quality_metrics['processing_consistency'] = max(0.0, consistency)
        
        # 5. 总体质量分数
        scores = [
            self.quality_metrics['intensity_preservation_rate'],
            self.quality_metrics['intensity_range_compliance'],
            self.quality_metrics['point_density_stability'],
            self.quality_metrics['processing_consistency']
        ]
        self.quality_metrics['overall_quality_score'] = np.mean([s for s in scores if s > 0])
    
    def get_intensity_statistics(self):
        """获取强度值统计信息"""
        if not self.intensity_history:
            return {}
        
        current_intensities = self.intensity_history[-1]
        
        stats = {
            'min_intensity': float(np.min(current_intensities)),
            'max_intensity': float(np.max(current_intensities)),
            'mean_intensity': float(np.mean(current_intensities)),
            'std_intensity': float(np.std(current_intensities)),
            'median_intensity': float(np.median(current_intensities)),
            'point_count': len(current_intensities),
            'intensity_range': float(np.max(current_intensities) - np.min(current_intensities))
        }
        
        # 强度值分布
        hist, bins = np.histogram(current_intensities, bins=20)
        stats['intensity_distribution'] = {
            'histogram': hist.tolist(),
            'bins': bins.tolist()
        }
        
        return stats
    
    def get_processing_statistics(self):
        """获取处理统计信息"""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        stats = {
            'total_frames_processed': self.total_frames_processed,
            'total_points_processed': self.total_points_processed,
            'elapsed_time_seconds': elapsed_time,
            'average_fps': self.total_frames_processed / elapsed_time if elapsed_time > 0 else 0,
            'average_points_per_second': self.total_points_processed / elapsed_time if elapsed_time > 0 else 0
        }
        
        if self.point_count_history:
            stats['current_points_per_frame'] = self.point_count_history[-1]
            stats['average_points_per_frame'] = np.mean(list(self.point_count_history))
        
        return stats
    
    def publish_quality_report(self, event):
        """发布质量报告"""
        try:
            # 构建质量报告
            report = {
                'timestamp': rospy.Time.now().to_sec(),
                'quality_metrics': self.quality_metrics,
                'intensity_statistics': self.get_intensity_statistics(),
                'processing_statistics': self.get_processing_statistics(),
                'system_status': self.get_system_status()
            }
            
            # 发布报告
            report_msg = String()
            # 使用安全序列化函数处理numpy类型
            safe_report = safe_json_serialize(report)
            report_msg.data = json.dumps(safe_report, indent=2)
            self.quality_report_pub.publish(report_msg)
            
            # 打印摘要
            self.print_quality_summary(report)
            
        except Exception as e:
            rospy.logerr("Error publishing quality report: %s", str(e))
    
    def get_system_status(self):
        """获取系统状态"""
        status = {
            'monitoring_active': True,
            'data_flow_healthy': bool(len(self.intensity_history) > 0),
            'quality_score_acceptable': bool(float(self.quality_metrics['overall_quality_score']) > 0.8),
            'intensity_range_valid': bool(float(self.quality_metrics['intensity_range_compliance']) > 0.95),
            'processing_stable': bool(float(self.quality_metrics['point_density_stability']) > 0.8)
        }

        # 总体健康状态
        health_checks = [
            status['data_flow_healthy'],
            status['quality_score_acceptable'],
            status['intensity_range_valid'],
            status['processing_stable']
        ]
        status['overall_health'] = bool(all(health_checks))

        return status
    
    def print_quality_summary(self, report):
        """打印质量摘要"""
        rospy.loginfo("=== Intensity Quality Monitor Report ===")
        
        # 质量指标
        metrics = report['quality_metrics']
        rospy.loginfo("Quality Metrics:")
        rospy.loginfo("  Overall Quality Score: %.3f", metrics['overall_quality_score'])
        rospy.loginfo("  Intensity Preservation: %.3f", metrics['intensity_preservation_rate'])
        rospy.loginfo("  Range Compliance: %.3f", metrics['intensity_range_compliance'])
        rospy.loginfo("  Density Stability: %.3f", metrics['point_density_stability'])
        
        # 强度统计
        if 'intensity_statistics' in report and report['intensity_statistics']:
            intensity_stats = report['intensity_statistics']
            rospy.loginfo("Intensity Statistics:")
            rospy.loginfo("  Range: %.1f - %.1f", 
                         intensity_stats['min_intensity'], 
                         intensity_stats['max_intensity'])
            rospy.loginfo("  Mean: %.1f ± %.1f", 
                         intensity_stats['mean_intensity'], 
                         intensity_stats['std_intensity'])
            rospy.loginfo("  Points: %d", intensity_stats['point_count'])
        
        # 处理统计
        proc_stats = report['processing_statistics']
        rospy.loginfo("Processing Statistics:")
        rospy.loginfo("  Frames: %d, Points: %d", 
                     proc_stats['total_frames_processed'],
                     proc_stats['total_points_processed'])
        rospy.loginfo("  FPS: %.1f, Points/sec: %.0f", 
                     proc_stats['average_fps'],
                     proc_stats['average_points_per_second'])
        
        # 系统状态
        status = report['system_status']
        health_status = "HEALTHY" if status['overall_health'] else "WARNING"
        rospy.loginfo("System Status: %s", health_status)
        
        rospy.loginfo("=" * 45)

def main():
    try:
        monitor = IntensityQualityMonitor()
        
        rospy.loginfo("Intensity Quality Monitor running...")
        rospy.loginfo("Monitoring pointcloud intensity preservation quality")
        
        rospy.spin()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("Intensity Quality Monitor stopped")
    except Exception as e:
        rospy.logerr("Intensity Quality Monitor exception: %s", str(e))

if __name__ == '__main__':
    main()
