#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS软约束回环检测器
不直接修改SLAM位置，而是通过GPS信息智能触发回环检测
综合考虑GPS质量好坏的情况
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
import json
import threading
from collections import deque
import math

class GPSSoftConstraintLoopDetector:
    def __init__(self):
        rospy.init_node('gps_soft_constraint_loop_detector', anonymous=True)
        
        # 参数配置 - 根据GPS质量动态调整
        self.good_gps_threshold = rospy.get_param('~good_gps_threshold', 15.0)  # 好GPS的接近阈值
        self.poor_gps_threshold = rospy.get_param('~poor_gps_threshold', 30.0)  # 差GPS的接近阈值
        self.trajectory_similarity_threshold = rospy.get_param('~trajectory_similarity_threshold', 20.0)
        self.min_time_gap = rospy.get_param('~min_time_gap', 30.0)
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 50.0)
        self.heading_similarity_threshold = rospy.get_param('~heading_similarity_threshold', 45.0)
        
        # GPS质量权重配置
        self.gps_quality_weights = {
            4: 1.0,    # RTK固定解
            3: 0.9,    # RTK浮点解
            2: 0.7,    # DGPS
            1: 0.5,    # GPS单点定位
            0: 0.3,    # 无效
            -1: 0.2    # 质量未知但可用
        }
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.slam_trajectory = deque(maxlen=2000)
        self.gps_quality_history = deque(maxlen=2000)
        
        # 状态变量
        self.last_loop_detection_time = 0
        self.total_loop_detections = 0
        self.successful_loops = 0
        self.origin_set = False
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        
        # 发布器
        self.loop_trigger_pub = rospy.Publisher('/gps_soft_loop_trigger', String, queue_size=1)
        self.loop_confidence_pub = rospy.Publisher('/loop_confidence_score', Float64, queue_size=1)
        self.status_pub = rospy.Publisher('/gps_soft_loop_status', String, queue_size=1)
        self.force_loop_pub = rospy.Publisher('/intelligent_force_loop_closure', String, queue_size=1)
        
        # 定时器
        self.detection_timer = rospy.Timer(rospy.Duration(2.0), self.detect_loop_opportunities)
        self.status_timer = rospy.Timer(rospy.Duration(15.0), self.publish_status)
        
        rospy.loginfo("GPS Soft Constraint Loop Detector Started")
        rospy.loginfo("Good GPS threshold: %.1f m, Poor GPS threshold: %.1f m", 
                     self.good_gps_threshold, self.poor_gps_threshold)
    
    def gps_callback(self, msg):
        """处理GPS数据，考虑质量因素"""
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # 获取GPS质量
            gps_quality = msg.status.status
            quality_weight = self.gps_quality_weights.get(gps_quality, 0.1)
            
            # 设置原点
            if not self.origin_set:
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                self.origin_set = True
                local_x, local_y = 0.0, 0.0
                rospy.loginfo("🏁 GPS Origin Set: lat=%.6f, lon=%.6f, quality=%d", 
                             msg.latitude, msg.longitude, gps_quality)
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'timestamp': current_time,
                'quality': gps_quality,
                'quality_weight': quality_weight,
                'lat': msg.latitude,
                'lon': msg.longitude
            }
            
            self.gps_trajectory.append(gps_point)
            self.gps_quality_history.append(gps_quality)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            self.slam_trajectory.append(slam_point)
    
    def gps_to_local(self, lat, lon):
        """GPS坐标转换为局部坐标"""
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 精确的坐标转换
        x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
        y = lat_diff * 110540.0
        
        return x, y
    
    def calculate_distance(self, point1, point2):
        """计算两点间距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        return math.sqrt(dx*dx + dy*dy)
    
    def calculate_weighted_gps_distance(self, current_gps, historical_gps):
        """计算加权GPS距离，考虑质量因素"""
        base_distance = self.calculate_distance(current_gps, historical_gps)
        
        # 质量权重影响距离阈值
        current_weight = current_gps['quality_weight']
        historical_weight = historical_gps['quality_weight']
        combined_weight = (current_weight + historical_weight) / 2.0
        
        # 质量好的GPS使用更严格的阈值，质量差的使用更宽松的阈值
        if combined_weight >= 0.8:  # 高质量GPS
            effective_threshold = self.good_gps_threshold
        elif combined_weight >= 0.5:  # 中等质量GPS
            effective_threshold = (self.good_gps_threshold + self.poor_gps_threshold) / 2.0
        else:  # 低质量GPS
            effective_threshold = self.poor_gps_threshold
        
        return base_distance, effective_threshold, combined_weight
    
    def calculate_heading_similarity(self, pose1, pose2):
        """计算航向相似度"""
        def quat_to_yaw(q):
            return math.atan2(2.0 * (q['w'] * q['z'] + q['x'] * q['y']),
                            1.0 - 2.0 * (q['y'] * q['y'] + q['z'] * q['z']))
        
        yaw1 = quat_to_yaw(pose1['orientation'])
        yaw2 = quat_to_yaw(pose2['orientation'])
        
        angle_diff = abs(yaw1 - yaw2)
        angle_diff = min(angle_diff, 2*math.pi - angle_diff)
        
        return math.degrees(angle_diff)
    
    def detect_loop_opportunities(self, event):
        """检测回环机会"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查数据充足性
                if len(self.gps_trajectory) < 20 or len(self.slam_trajectory) < 20:
                    return
                
                # 检查时间间隔
                if current_time - self.last_loop_detection_time < self.min_time_gap:
                    return
                
                # 检查轨迹长度
                if self.calculate_trajectory_length() < self.min_trajectory_length:
                    return
                
                # 获取当前位置
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 寻找GPS接近的候选位置
                loop_candidates = self.find_quality_aware_candidates(current_gps)
                
                if loop_candidates:
                    # 验证并选择最佳候选
                    best_candidate = self.validate_loop_candidates(current_slam, loop_candidates)
                    
                    if best_candidate:
                        self.trigger_soft_loop_closure(current_slam, current_gps, best_candidate)
                        
        except Exception as e:
            rospy.logerr("Error in loop detection: %s", str(e))
    
    def find_quality_aware_candidates(self, current_gps):
        """寻找考虑质量的GPS候选位置"""
        candidates = []
        current_time = current_gps['timestamp']
        
        # 跳过最近的20个点，避免短期回环
        for i, gps_point in enumerate(self.gps_trajectory[:-20]):
            # 检查时间间隔
            if current_time - gps_point['timestamp'] < self.min_time_gap:
                continue
            
            # 计算加权GPS距离
            distance, threshold, quality_weight = self.calculate_weighted_gps_distance(current_gps, gps_point)
            
            # 检查是否在阈值内
            if distance <= threshold:
                candidates.append({
                    'gps_point': gps_point,
                    'slam_index': i,
                    'gps_distance': distance,
                    'gps_threshold': threshold,
                    'quality_weight': quality_weight,
                    'time_gap': current_time - gps_point['timestamp'],
                    'current_quality': current_gps['quality'],
                    'historical_quality': gps_point['quality']
                })
        
        # 按质量权重和距离综合排序
        candidates.sort(key=lambda x: x['gps_distance'] / x['quality_weight'])
        return candidates[:8]  # 返回最好的8个候选
    
    def validate_loop_candidates(self, current_slam, candidates):
        """验证回环候选"""
        best_candidate = None
        best_score = float('inf')
        
        for candidate in candidates:
            slam_index = candidate['slam_index']
            if slam_index >= len(self.slam_trajectory):
                continue
                
            historical_slam = self.slam_trajectory[slam_index]
            
            # 计算SLAM轨迹距离
            slam_distance = self.calculate_distance(current_slam, historical_slam)
            
            # 计算航向相似度
            heading_diff = self.calculate_heading_similarity(current_slam, historical_slam)
            
            # 综合评分 - 考虑GPS质量
            distance_score = slam_distance
            heading_score = heading_diff / 180.0 * 30.0
            time_bonus = min(candidate['time_gap'] / 300.0, 1.0) * 10.0
            quality_bonus = candidate['quality_weight'] * 15.0
            
            total_score = distance_score + heading_score - time_bonus - quality_bonus
            
            # 动态调整SLAM距离阈值
            slam_threshold = self.trajectory_similarity_threshold * (2.0 - candidate['quality_weight'])
            
            # 检查是否满足条件
            if (slam_distance <= slam_threshold and 
                heading_diff <= self.heading_similarity_threshold and
                total_score < best_score):
                
                best_score = total_score
                best_candidate = {
                    'candidate': candidate,
                    'historical_slam': historical_slam,
                    'slam_distance': slam_distance,
                    'heading_diff': heading_diff,
                    'score': total_score,
                    'confidence': self.calculate_confidence(candidate, slam_distance, heading_diff)
                }
        
        return best_candidate
    
    def calculate_confidence(self, candidate, slam_distance, heading_diff):
        """计算回环置信度"""
        # 基础置信度
        base_confidence = 0.5
        
        # GPS质量贡献
        quality_contribution = candidate['quality_weight'] * 0.3
        
        # 距离贡献 (距离越近置信度越高)
        distance_contribution = max(0, (50.0 - candidate['gps_distance']) / 50.0) * 0.2
        
        # SLAM一致性贡献
        slam_contribution = max(0, (30.0 - slam_distance) / 30.0) * 0.2
        
        # 航向一致性贡献
        heading_contribution = max(0, (45.0 - heading_diff) / 45.0) * 0.15
        
        # 时间间隔贡献
        time_contribution = min(candidate['time_gap'] / 300.0, 1.0) * 0.15
        
        total_confidence = (base_confidence + quality_contribution + distance_contribution + 
                          slam_contribution + heading_contribution + time_contribution)
        
        return min(1.0, max(0.0, total_confidence))
    
    def trigger_soft_loop_closure(self, current_slam, current_gps, best_candidate):
        """触发软约束回环检测"""
        try:
            self.last_loop_detection_time = rospy.Time.now().to_sec()
            self.total_loop_detections += 1
            
            candidate = best_candidate['candidate']
            confidence = best_candidate['confidence']
            
            # 构建回环触发消息
            loop_data = {
                'type': 'gps_soft_constraint_loop_closure',
                'timestamp': self.last_loop_detection_time,
                'confidence': confidence,
                'gps_info': {
                    'distance': candidate['gps_distance'],
                    'threshold': candidate['gps_threshold'],
                    'current_quality': candidate['current_quality'],
                    'historical_quality': candidate['historical_quality'],
                    'quality_weight': candidate['quality_weight']
                },
                'slam_info': {
                    'distance': best_candidate['slam_distance'],
                    'heading_diff': best_candidate['heading_diff']
                },
                'poses': {
                    'current': {
                        'x': current_slam['x'],
                        'y': current_slam['y'],
                        'z': current_slam['z']
                    },
                    'target': {
                        'x': best_candidate['historical_slam']['x'],
                        'y': best_candidate['historical_slam']['y'],
                        'z': best_candidate['historical_slam']['z']
                    }
                },
                'time_gap': candidate['time_gap']
            }
            
            # 发布软约束回环触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.loop_trigger_pub.publish(trigger_msg)
            
            # 发布置信度分数
            confidence_msg = Float64()
            confidence_msg.data = confidence
            self.loop_confidence_pub.publish(confidence_msg)
            
            # 如果置信度足够高，触发强制回环
            if confidence >= 0.7:
                force_data = {
                    'type': 'gps_soft_constraint_precise_match',
                    'distance_to_start': candidate['gps_distance'],
                    'confidence': confidence,
                    'gps_quality': candidate['quality_weight'],
                    'reason': 'high_confidence_gps_soft_constraint'
                }
                
                force_msg = String()
                force_msg.data = json.dumps(force_data)
                self.force_loop_pub.publish(force_msg)
                
                rospy.loginfo("🔥 High Confidence GPS Soft Loop Triggered!")
            
            rospy.loginfo("🎯 GPS Soft Loop Detected!")
            rospy.loginfo("GPS: %.1fm (Q:%d→%d, W:%.2f), SLAM: %.1fm, Heading: %.1f°, Confidence: %.2f", 
                         candidate['gps_distance'], candidate['current_quality'], 
                         candidate['historical_quality'], candidate['quality_weight'],
                         best_candidate['slam_distance'], best_candidate['heading_diff'], confidence)
            
        except Exception as e:
            rospy.logerr("Error triggering soft loop closure: %s", str(e))
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.slam_trajectory) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(1, min(len(self.slam_trajectory), 500)):  # 限制计算范围
            total_length += self.calculate_distance(self.slam_trajectory[i-1], self.slam_trajectory[i])
        
        return total_length
    
    def publish_status(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                # 计算GPS质量统计
                quality_stats = {}
                for quality in self.gps_quality_history:
                    quality_stats[quality] = quality_stats.get(quality, 0) + 1
                
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'detector_type': 'gps_soft_constraint_loop_detector',
                    'statistics': {
                        'total_detections': self.total_loop_detections,
                        'successful_loops': self.successful_loops,
                        'gps_points': len(self.gps_trajectory),
                        'slam_points': len(self.slam_trajectory),
                        'trajectory_length': self.calculate_trajectory_length(),
                        'gps_quality_distribution': quality_stats
                    },
                    'current_status': {
                        'recent_gps_quality': list(self.gps_quality_history)[-10:] if self.gps_quality_history else [],
                        'average_gps_quality': np.mean(list(self.gps_quality_history)[-50:]) if len(self.gps_quality_history) >= 50 else 0
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.status_pub.publish(status_msg)
                
                if len(self.gps_trajectory) > 0:
                    avg_quality = np.mean(list(self.gps_quality_history)[-20:]) if len(self.gps_quality_history) >= 20 else 0
                    rospy.loginfo("GPS Soft Loop: %d detections, %.1fm trajectory, avg GPS quality: %.1f", 
                                 self.total_loop_detections, self.calculate_trajectory_length(), avg_quality)
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        detector = GPSSoftConstraintLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS Soft Constraint Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
