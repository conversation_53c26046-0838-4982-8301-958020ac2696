#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS质量自适应的回环触发器
根据GPS质量动态调整回环检测策略
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
import json
import threading
from collections import deque

class AdaptiveLoopTrigger:
    def __init__(self):
        rospy.init_node('adaptive_loop_trigger', anonymous=True)
        
        # 自适应参数
        self.quality_thresholds = {
            'excellent': {'min_confidence': 0.6, 'trigger_frequency': 1.0},
            'good': {'min_confidence': 0.7, 'trigger_frequency': 0.8},
            'fair': {'min_confidence': 0.8, 'trigger_frequency': 0.6},
            'poor': {'min_confidence': 0.9, 'trigger_frequency': 0.3}
        }
        
        # 状态跟踪
        self.recent_triggers = deque(maxlen=10)
        self.current_gps_quality = 'good'
        self.last_trigger_time = 0
        self.trigger_cooldown = 15.0  # 基础冷却时间
        
        # 统计信息
        self.trigger_stats = {
            'total_candidates': 0,
            'triggered_loops': 0,
            'quality_filtered': 0,
            'frequency_filtered': 0,
            'confidence_filtered': 0
        }
        
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.loop_candidate_sub = rospy.Subscriber('/advanced_gps_loop_trigger', String, 
                                                  self.loop_candidate_callback, queue_size=5)
        self.quality_analysis_sub = rospy.Subscriber('/gps_quality_analysis', String,
                                                    self.quality_analysis_callback, queue_size=1)
        
        # 发布器
        self.final_trigger_pub = rospy.Publisher('/final_loop_closure_trigger', String, queue_size=1)
        self.trigger_decision_pub = rospy.Publisher('/loop_trigger_decision', String, queue_size=1)
        self.adaptive_status_pub = rospy.Publisher('/adaptive_trigger_status', String, queue_size=1)
        
        # 定时器
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("Adaptive Loop Trigger Started")
        rospy.loginfo("Quality-based adaptive triggering enabled")
    
    def loop_candidate_callback(self, msg):
        """处理回环候选"""
        try:
            with self.data_lock:
                self.trigger_stats['total_candidates'] += 1
                
                # 解析候选数据
                candidate_data = json.loads(msg.data)
                current_time = rospy.Time.now().to_sec()
                
                # 获取置信度
                confidence = candidate_data.get('confidence', 0.0)
                
                # 自适应决策
                decision = self.make_adaptive_decision(candidate_data, confidence, current_time)
                
                # 发布决策信息
                self.publish_decision(candidate_data, decision)
                
                # 如果决策是触发，则发布最终触发信号
                if decision['trigger']:
                    self.trigger_final_loop_closure(candidate_data, decision)
                    
        except Exception as e:
            rospy.logerr("Error processing loop candidate: %s", str(e))
    
    def quality_analysis_callback(self, msg):
        """处理GPS质量分析"""
        try:
            quality_data = json.loads(msg.data)
            avg_weight = quality_data.get('average_weight', 0.5)
            
            # 更新当前GPS质量等级
            if avg_weight > 0.8:
                self.current_gps_quality = 'excellent'
            elif avg_weight > 0.6:
                self.current_gps_quality = 'good'
            elif avg_weight > 0.4:
                self.current_gps_quality = 'fair'
            else:
                self.current_gps_quality = 'poor'
                
        except Exception as e:
            rospy.logerr("Error processing quality analysis: %s", str(e))
    
    def make_adaptive_decision(self, candidate_data, confidence, current_time):
        """基于GPS质量的自适应决策"""
        decision = {
            'trigger': False,
            'reason': '',
            'confidence_required': 0.0,
            'frequency_factor': 0.0,
            'quality_level': self.current_gps_quality,
            'filters_applied': []
        }
        
        # 获取当前质量等级的阈值
        quality_config = self.quality_thresholds[self.current_gps_quality]
        decision['confidence_required'] = quality_config['min_confidence']
        decision['frequency_factor'] = quality_config['trigger_frequency']
        
        # 过滤器1: 置信度检查
        if confidence < quality_config['min_confidence']:
            decision['reason'] = f"Confidence {confidence:.3f} below threshold {quality_config['min_confidence']:.3f}"
            decision['filters_applied'].append('confidence_filter')
            self.trigger_stats['confidence_filtered'] += 1
            return decision
        
        # 过滤器2: 频率控制
        adaptive_cooldown = self.trigger_cooldown / quality_config['trigger_frequency']
        time_since_last = current_time - self.last_trigger_time
        
        if time_since_last < adaptive_cooldown:
            decision['reason'] = f"Cooldown active: {time_since_last:.1f}s < {adaptive_cooldown:.1f}s"
            decision['filters_applied'].append('frequency_filter')
            self.trigger_stats['frequency_filtered'] += 1
            return decision
        
        # 过滤器3: GPS质量特殊处理
        if self.current_gps_quality == 'poor':
            # 对于差质量GPS，需要额外验证
            gps_distance = candidate_data.get('metrics', {}).get('gps_distance', float('inf'))
            slam_distance = candidate_data.get('metrics', {}).get('slam_distance', float('inf'))
            
            # 要求GPS和SLAM距离都很小
            if gps_distance > 8.0 or slam_distance > 5.0:
                decision['reason'] = f"Poor GPS quality requires stricter distance: GPS={gps_distance:.1f}, SLAM={slam_distance:.1f}"
                decision['filters_applied'].append('quality_filter')
                self.trigger_stats['quality_filtered'] += 1
                return decision
        
        # 过滤器4: 几何一致性检查
        geometric_score = candidate_data.get('metrics', {}).get('geometric_score', 0.0)
        min_geometric_score = 0.7 if self.current_gps_quality in ['poor', 'fair'] else 0.5
        
        if geometric_score < min_geometric_score:
            decision['reason'] = f"Geometric consistency {geometric_score:.3f} below threshold {min_geometric_score:.3f}"
            decision['filters_applied'].append('geometric_filter')
            return decision
        
        # 所有检查通过，触发回环
        decision['trigger'] = True
        decision['reason'] = f"All checks passed - Quality: {self.current_gps_quality}, Confidence: {confidence:.3f}"
        
        return decision
    
    def trigger_final_loop_closure(self, candidate_data, decision):
        """触发最终回环闭合"""
        try:
            current_time = rospy.Time.now().to_sec()
            self.last_trigger_time = current_time
            self.trigger_stats['triggered_loops'] += 1
            
            # 记录触发历史
            trigger_record = {
                'timestamp': current_time,
                'confidence': candidate_data.get('confidence', 0.0),
                'quality_level': self.current_gps_quality,
                'gps_distance': candidate_data.get('metrics', {}).get('gps_distance', 0.0),
                'slam_distance': candidate_data.get('metrics', {}).get('slam_distance', 0.0)
            }
            self.recent_triggers.append(trigger_record)
            
            # 构建最终触发消息
            final_trigger_data = {
                'type': 'adaptive_quality_loop_closure',
                'timestamp': current_time,
                'original_candidate': candidate_data,
                'adaptive_decision': decision,
                'trigger_context': {
                    'gps_quality_level': self.current_gps_quality,
                    'adaptive_confidence_threshold': decision['confidence_required'],
                    'frequency_factor': decision['frequency_factor'],
                    'recent_trigger_count': len(self.recent_triggers)
                },
                'enhanced_confidence': self.calculate_enhanced_confidence(candidate_data, decision)
            }
            
            # 发布最终触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(final_trigger_data, indent=2)
            self.final_trigger_pub.publish(trigger_msg)
            
            rospy.loginfo("🚀 ADAPTIVE LOOP CLOSURE TRIGGERED!")
            rospy.loginfo("Quality: %s | Confidence: %.3f | GPS: %.2fm | SLAM: %.2fm",
                         self.current_gps_quality,
                         candidate_data.get('confidence', 0.0),
                         candidate_data.get('metrics', {}).get('gps_distance', 0.0),
                         candidate_data.get('metrics', {}).get('slam_distance', 0.0))
            
        except Exception as e:
            rospy.logerr("Error triggering final loop closure: %s", str(e))
    
    def calculate_enhanced_confidence(self, candidate_data, decision):
        """计算增强置信度"""
        base_confidence = candidate_data.get('confidence', 0.0)
        
        # 质量增强因子
        quality_factors = {
            'excellent': 1.1,
            'good': 1.0,
            'fair': 0.9,
            'poor': 0.8
        }
        
        quality_factor = quality_factors[self.current_gps_quality]
        
        # 历史成功率增强
        if len(self.recent_triggers) > 0:
            # 简化的成功率估计（实际应该基于回环验证结果）
            success_rate = 0.8  # 假设80%成功率
            history_factor = 0.9 + 0.2 * success_rate
        else:
            history_factor = 1.0
        
        # 几何一致性增强
        geometric_score = candidate_data.get('metrics', {}).get('geometric_score', 0.5)
        geometric_factor = 0.8 + 0.4 * geometric_score
        
        enhanced_confidence = base_confidence * quality_factor * history_factor * geometric_factor
        return min(1.0, enhanced_confidence)
    
    def publish_decision(self, candidate_data, decision):
        """发布决策信息"""
        try:
            decision_data = {
                'timestamp': rospy.Time.now().to_sec(),
                'candidate_confidence': candidate_data.get('confidence', 0.0),
                'decision': decision,
                'statistics': self.trigger_stats.copy()
            }
            
            decision_msg = String()
            decision_msg.data = json.dumps(decision_data, indent=2)
            self.trigger_decision_pub.publish(decision_msg)
            
        except Exception as e:
            rospy.logerr("Error publishing decision: %s", str(e))
    
    def publish_status(self, event):
        """发布状态信息"""
        try:
            with self.data_lock:
                # 计算统计信息
                total_candidates = max(1, self.trigger_stats['total_candidates'])
                trigger_rate = self.trigger_stats['triggered_loops'] / total_candidates
                
                status_data = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'current_gps_quality': self.current_gps_quality,
                    'quality_thresholds': self.quality_thresholds[self.current_gps_quality],
                    'statistics': self.trigger_stats.copy(),
                    'performance_metrics': {
                        'trigger_rate': trigger_rate,
                        'confidence_filter_rate': self.trigger_stats['confidence_filtered'] / total_candidates,
                        'frequency_filter_rate': self.trigger_stats['frequency_filtered'] / total_candidates,
                        'quality_filter_rate': self.trigger_stats['quality_filtered'] / total_candidates
                    },
                    'recent_triggers': list(self.recent_triggers)[-5:],  # 最近5次触发
                    'adaptive_parameters': {
                        'current_cooldown': self.trigger_cooldown / self.quality_thresholds[self.current_gps_quality]['trigger_frequency'],
                        'time_since_last_trigger': rospy.Time.now().to_sec() - self.last_trigger_time
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_data, indent=2)
                self.adaptive_status_pub.publish(status_msg)
                
                # 控制台输出
                rospy.loginfo("Adaptive Trigger Status: Quality=%s | Triggers=%d/%d (%.1f%%) | Filters: C=%d F=%d Q=%d",
                             self.current_gps_quality,
                             self.trigger_stats['triggered_loops'],
                             self.trigger_stats['total_candidates'],
                             trigger_rate * 100,
                             self.trigger_stats['confidence_filtered'],
                             self.trigger_stats['frequency_filtered'],
                             self.trigger_stats['quality_filtered'])
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        trigger = AdaptiveLoopTrigger()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Adaptive Loop Trigger: %s", str(e))

if __name__ == '__main__':
    main()
