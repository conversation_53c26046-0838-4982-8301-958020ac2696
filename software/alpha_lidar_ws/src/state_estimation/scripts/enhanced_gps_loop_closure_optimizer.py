#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced GPS Loop Closure Optimizer
Supports intermediate loop detection and matching
Includes start-end loops, intermediate crossing loops, and revisit loops
"""

import sys
import os

# 设置环境变量解决编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LC_ALL'] = 'C.UTF-8'
os.environ['LANG'] = 'C.UTF-8'

try:
    import rospy
    import numpy as np
    import math
    from sensor_msgs.msg import NavSatFix
    from geometry_msgs.msg import PoseStamped, Point
    from std_msgs.msg import Bool, Float64, Int32
    from nav_msgs.msg import Path
    from threading import Lock
    import time
    from collections import deque

    # 可选的tf2导入（避免PyKDL依赖问题）
    try:
        import tf2_ros
        import tf2_geometry_msgs
        TF2_AVAILABLE = True
    except ImportError:
        TF2_AVAILABLE = False
        rospy.logwarn("tf2 not available, some features may be limited")

    # 可选的scikit-learn导入
    try:
        from sklearn.cluster import DBSCAN
        SKLEARN_AVAILABLE = True
    except ImportError:
        SKLEARN_AVAILABLE = False
        rospy.logwarn("scikit-learn not available, using simplified clustering")

except ImportError as e:
    print(f"Import error: {e}")
    print("Please install required packages:")
    print("pip install PyYAML numpy")
    sys.exit(1)

class EnhancedGPSLoopClosureOptimizer:
    def __init__(self):
        rospy.init_node('enhanced_gps_loop_closure_optimizer', anonymous=True)
        
        # 基础参数配置
        self.loop_closure_distance_threshold = rospy.get_param('~loop_closure_distance_threshold', 5.0)
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 50.0)
        self.gps_quality_threshold = rospy.get_param('~gps_quality_threshold', -1)
        self.check_interval = rospy.get_param('~check_interval', 1.0)
        
        # 增强回环检测参数
        self.intermediate_loop_threshold = rospy.get_param('~intermediate_loop_threshold', 8.0)
        self.min_loop_separation = rospy.get_param('~min_loop_separation', 30.0)
        self.trajectory_window_size = rospy.get_param('~trajectory_window_size', 100)
        self.revisit_threshold = rospy.get_param('~revisit_threshold', 10.0)

        # 智能首尾回环检测参数
        self.start_departure_threshold = rospy.get_param('~start_departure_threshold', 30.0)  # 离开起点30米后开始监控
        self.intelligent_detection_threshold = rospy.get_param('~intelligent_detection_threshold', 50.0)  # 50米内触发精细匹配
        self.enable_intelligent_detection = rospy.get_param('~enable_intelligent_detection', True)  # 启用智能检测

        # GPS质量自适应阈值
        self.quality_adaptive_thresholds = {
            4: 20.0,  # RTK固定解：20米
            3: 30.0,  # RTK浮点解：30米
            2: 40.0,  # DGPS：40米
            1: 50.0,  # GPS单点定位：50米
            0: 60.0,  # 无效：60米
            -1: 50.0  # 未知：50米
        }
        
        # GPS话题
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        
        # 轨迹数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.pose_trajectory = deque(maxlen=2000)
        self.trajectory_timestamps = deque(maxlen=2000)
        
        # 回环检测状态
        self.detected_loops = []
        self.loop_candidates = []
        self.last_loop_detection_time = 0
        self.loop_detection_cooldown = 5.0
        
        # 线程锁
        self.data_lock = Lock()
        
        # 发布器
        self.force_loop_closure_pub = rospy.Publisher('/force_loop_closure', Bool, queue_size=1)
        self.loop_distance_pub = rospy.Publisher('/loop_closure_distance', Float64, queue_size=1)
        self.intermediate_loop_pub = rospy.Publisher('/intermediate_loop_detected', Bool, queue_size=1)
        self.loop_candidates_pub = rospy.Publisher('/loop_candidates_count', Int32, queue_size=1)
        self.trajectory_analysis_pub = rospy.Publisher('/trajectory_analysis', Bool, queue_size=1)
        
        # 订阅器
        self.gps_sub = rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback)

        # 智能首尾回环检测订阅器
        self.intelligent_loop_sub = rospy.Subscriber('/intelligent_force_loop_closure', String, self.intelligent_loop_callback, queue_size=5)
        
        # 定时器
        self.analysis_timer = rospy.Timer(rospy.Duration(self.check_interval), self.analyze_trajectory)
        
        rospy.loginfo("Enhanced GPS Loop Closure Optimizer Started")
        rospy.loginfo("Basic loop distance threshold: {:.1f}m".format(self.loop_closure_distance_threshold))
        rospy.loginfo("Intermediate loop distance threshold: {:.1f}m".format(self.intermediate_loop_threshold))
        rospy.loginfo("Minimum loop separation distance: {:.1f}m".format(self.min_loop_separation))
        
    def gps_callback(self, msg):
        """GPS回调函数"""
        with self.data_lock:
            # 检查GPS质量
            if hasattr(msg, 'status') and msg.status.status < self.gps_quality_threshold:
                return
            
            current_time = rospy.Time.now().to_sec()
            gps_data = {
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status if hasattr(msg, 'status') else -1,
                'timestamp': current_time,
                'local_coords': None
            }
            
            # 转换为本地坐标
            if len(self.gps_trajectory) > 0:
                origin = self.gps_trajectory[0]
                local_coords = self.gps_to_local_coords(gps_data, origin)
                gps_data['local_coords'] = local_coords
            else:
                gps_data['local_coords'] = [0.0, 0.0, 0.0]
                rospy.loginfo("GPS origin set: lat={:.8f}, lon={:.8f}".format(msg.latitude, msg.longitude))
            
            self.gps_trajectory.append(gps_data)
            self.trajectory_timestamps.append(current_time)
    
    def pose_callback(self, msg):
        """SLAM位姿回调函数"""
        with self.data_lock:
            pose_data = {
                'position': [msg.pose.position.x, msg.pose.position.y, msg.pose.position.z],
                'orientation': [msg.pose.orientation.x, msg.pose.orientation.y, 
                              msg.pose.orientation.z, msg.pose.orientation.w],
                'timestamp': msg.header.stamp.to_sec()
            }
            self.pose_trajectory.append(pose_data)

    def intelligent_loop_callback(self, msg):
        """智能首尾回环检测回调函数"""
        try:
            # 解析智能回环检测消息
            loop_data = json.loads(msg.data)

            if loop_data.get('type') == 'intelligent_start_end':
                rospy.logwarn("🎯 Received INTELLIGENT start-end loop detection request!")
                rospy.logwarn("   Distance to start: %.2f m", loop_data.get('distance_to_start', 0))
                rospy.logwarn("   Trajectory length: %.2f m", loop_data.get('trajectory_length', 0))

                # 强制触发首尾回环检测
                self.force_start_end_loop_detection(loop_data)

        except (json.JSONDecodeError, KeyError) as e:
            rospy.logwarn("Error parsing intelligent loop message: %s", str(e))
        except Exception as e:
            rospy.logerr("Error in intelligent loop callback: %s", str(e))

    def force_start_end_loop_detection(self, loop_data):
        """强制执行首尾回环检测"""
        try:
            with self.data_lock:
                if len(self.gps_trajectory) < 10:
                    rospy.logwarn("Insufficient GPS trajectory data for intelligent detection")
                    return

                # 获取起点和当前点
                start_point = self.gps_trajectory[0]
                current_point = self.gps_trajectory[-1]

                # 计算距离
                distance = self.calculate_gps_distance(start_point, current_point)

                # 创建强制回环信息
                loop_info = {
                    'type': 'start_end',
                    'distance': distance,
                    'start_idx': 0,
                    'end_idx': len(self.gps_trajectory) - 1,
                    'confidence': 1.0,  # 智能检测给予最高置信度
                    'trigger_method': 'intelligent_gps_detection',
                    'force_global_matching': loop_data.get('force_global_matching', True)
                }

                rospy.logwarn("🚀 FORCING intelligent start-end loop closure!")
                rospy.logwarn("   GPS distance: %.2f m", distance)
                rospy.logwarn("   Trajectory points: %d", len(self.gps_trajectory))
                rospy.logwarn("   Force global matching: %s", loop_info['force_global_matching'])

                # 触发回环检测
                if self.trigger_loop_closure(loop_info):
                    rospy.loginfo("✅ Intelligent start-end loop closure triggered successfully!")
                else:
                    rospy.logwarn("❌ Intelligent start-end loop closure failed to trigger")

        except Exception as e:
            rospy.logerr("Error in force start-end loop detection: %s", str(e))

    def gps_to_local_coords(self, gps_data, origin):
        """将GPS坐标转换为本地坐标"""
        lat_diff = gps_data['latitude'] - origin['latitude']
        lon_diff = gps_data['longitude'] - origin['longitude']
        alt_diff = gps_data['altitude'] - origin['altitude']
        
        # 转换为米
        x = lon_diff * 111320.0 * math.cos(math.radians(origin['latitude']))
        y = lat_diff * 110540.0
        z = alt_diff
        
        return [x, y, z]
    
    def analyze_trajectory(self, event):
        """分析轨迹并检测各种类型的回环"""
        with self.data_lock:
            if len(self.gps_trajectory) < 10:
                return
            
            current_time = time.time()
            if (current_time - self.last_loop_detection_time) < self.loop_detection_cooldown:
                return
            
            # 1. 检测起点-终点回环
            self.detect_start_end_loop()
            
            # 2. 检测中间区域回环
            self.detect_intermediate_loops()
            
            # 3. 检测重复路径回环
            self.detect_revisit_loops()
            
            # 4. 发布分析结果
            self.publish_analysis_results()
    
    def detect_start_end_loop(self):
        """检测起点-终点回环"""
        if len(self.gps_trajectory) < 2:
            return
        
        start_pos = np.array(self.gps_trajectory[0]['local_coords'])
        current_pos = np.array(self.gps_trajectory[-1]['local_coords'])
        
        distance = np.linalg.norm(current_pos - start_pos)
        trajectory_length = self.calculate_trajectory_length()
        
        # 发布距离信息
        self.loop_distance_pub.publish(Float64(data=distance))
        
        if (distance <= self.loop_closure_distance_threshold and 
            trajectory_length >= self.min_trajectory_length):
            
            loop_info = {
                'type': 'start_end',
                'distance': distance,
                'start_idx': 0,
                'end_idx': len(self.gps_trajectory) - 1,
                'confidence': self.calculate_loop_confidence(0, len(self.gps_trajectory) - 1)
            }
            
            if self.is_new_loop(loop_info):
                self.trigger_loop_closure(loop_info)
    
    def detect_intermediate_loops(self):
        """检测中间区域回环"""
        if len(self.gps_trajectory) < self.trajectory_window_size:
            return
        
        # 获取最近的轨迹窗口
        recent_trajectory = list(self.gps_trajectory)[-self.trajectory_window_size:]
        positions = np.array([point['local_coords'] for point in recent_trajectory])
        
        # 使用滑动窗口检测回环
        window_size = min(50, len(positions) // 4)
        
        for i in range(len(positions) - window_size):
            current_segment = positions[i:i+window_size]
            
            # 在之前的轨迹中搜索相似区域
            for j in range(0, max(0, i - int(self.min_loop_separation))):
                if j + window_size >= len(positions):
                    continue
                
                candidate_segment = positions[j:j+window_size]
                
                # 计算段间距离
                segment_distance = self.calculate_segment_distance(current_segment, candidate_segment)
                
                if segment_distance <= self.intermediate_loop_threshold:
                    loop_info = {
                        'type': 'intermediate',
                        'distance': segment_distance,
                        'start_idx': j,
                        'end_idx': i,
                        'confidence': self.calculate_segment_confidence(current_segment, candidate_segment)
                    }
                    
                    if self.is_new_loop(loop_info):
                        self.trigger_loop_closure(loop_info)
                        return
    
    def detect_revisit_loops(self):
        """Detect revisit loops in trajectory"""
        if len(self.gps_trajectory) < 20:
            return

        # Simplified revisit detection: check if current position is close to historical positions
        current_pos = np.array(self.gps_trajectory[-1]['local_coords'])

        for i in range(0, len(self.gps_trajectory) - 20):
            hist_pos = np.array(self.gps_trajectory[i]['local_coords'])
            distance = np.linalg.norm(current_pos - hist_pos)

            if distance <= self.revisit_threshold:
                # Check time interval
                time_gap = self.trajectory_timestamps[-1] - self.trajectory_timestamps[i]

                if time_gap > 10.0:  # At least 10 seconds interval
                    loop_info = {
                        'type': 'revisit',
                        'distance': distance,
                        'start_idx': i,
                        'end_idx': len(self.gps_trajectory) - 1,
                        'confidence': min(1.0, time_gap / 60.0)
                    }

                    if self.is_new_loop(loop_info):
                        self.trigger_loop_closure(loop_info)
                        return
    
    def calculate_segment_distance(self, segment1, segment2):
        """计算两个轨迹段之间的距离"""
        if len(segment1) != len(segment2):
            return float('inf')
        
        distances = np.linalg.norm(segment1 - segment2, axis=1)
        return np.mean(distances)
    
    def calculate_segment_confidence(self, segment1, segment2):
        """计算段匹配的置信度"""
        distances = np.linalg.norm(segment1 - segment2, axis=1)
        mean_distance = np.mean(distances)
        
        confidence = 1.0 / (1.0 + mean_distance)
        return confidence
    
    def calculate_loop_confidence(self, start_idx, end_idx):
        """计算回环的置信度"""
        if start_idx >= end_idx or end_idx >= len(self.gps_trajectory):
            return 0.0
        
        start_pos = np.array(self.gps_trajectory[start_idx]['local_coords'])
        end_pos = np.array(self.gps_trajectory[end_idx]['local_coords'])
        
        distance = np.linalg.norm(end_pos - start_pos)
        trajectory_span = end_idx - start_idx
        
        confidence = 1.0 / (1.0 + distance) * min(1.0, trajectory_span / 100.0)
        return confidence
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.gps_trajectory) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(1, len(self.gps_trajectory)):
            pos1 = np.array(self.gps_trajectory[i-1]['local_coords'])
            pos2 = np.array(self.gps_trajectory[i]['local_coords'])
            total_length += np.linalg.norm(pos2 - pos1)
        
        return total_length
    
    def is_new_loop(self, loop_info):
        """检查是否是新的回环"""
        for existing_loop in self.detected_loops:
            if existing_loop['type'] == loop_info['type']:
                if loop_info['type'] == 'start_end':
                    return False
                elif loop_info['type'] == 'intermediate':
                    if (abs(existing_loop['start_idx'] - loop_info['start_idx']) < 20 and
                        abs(existing_loop['end_idx'] - loop_info['end_idx']) < 20):
                        return False
                elif loop_info['type'] == 'revisit':
                    # 检查revisit回环是否重复：相同的起始和结束索引
                    if (abs(existing_loop['start_idx'] - loop_info['start_idx']) < 10 and
                        abs(existing_loop['end_idx'] - loop_info['end_idx']) < 10):
                        return False

        return True
    
    def trigger_loop_closure(self, loop_info):
        """触发回环闭合"""
        current_time = time.time()
        
        rospy.logwarn("Loop detected: {} type".format(loop_info['type']))
        rospy.logwarn("Loop info: {}".format(loop_info))

        # Publish force loop closure signal
        self.force_loop_closure_pub.publish(Bool(data=True))

        if loop_info['type'] == 'intermediate':
            self.intermediate_loop_pub.publish(Bool(data=True))

        # Record loop
        loop_info['detection_time'] = current_time
        self.detected_loops.append(loop_info)
        self.last_loop_detection_time = current_time

        rospy.loginfo("Loop recorded, total: {} loops".format(len(self.detected_loops)))
    
    def publish_analysis_results(self):
        """发布分析结果"""
        self.loop_candidates_pub.publish(Int32(data=len(self.loop_candidates)))
        
        analysis_active = len(self.gps_trajectory) > self.trajectory_window_size
        self.trajectory_analysis_pub.publish(Bool(data=analysis_active))
    
    def get_status_info(self):
        """Get detailed status information"""
        with self.data_lock:
            if len(self.gps_trajectory) == 0:
                return "Waiting for GPS data..."

            trajectory_length = self.calculate_trajectory_length()
            detected_loops_info = []

            for loop in self.detected_loops:
                loop_desc = "{} loop (confidence: {:.3f})".format(
                    loop['type'], loop.get('confidence', 0))
                detected_loops_info.append(loop_desc)

            status = """
Enhanced GPS Loop Closure Status:
Trajectory points: {}
Trajectory length: {:.1f}m
Detected loops: {} loops
   {}
Start-end distance threshold: {:.1f}m
Intermediate loop threshold: {:.1f}m
            """.format(
                len(self.gps_trajectory),
                trajectory_length,
                len(self.detected_loops),
                ', '.join(detected_loops_info) if detected_loops_info else 'None',
                self.loop_closure_distance_threshold,
                self.intermediate_loop_threshold
            )
            return status.strip()

def main():
    try:
        optimizer = EnhancedGPSLoopClosureOptimizer()
        
        # 状态监控
        def print_status(event):
            rospy.loginfo_throttle(15, optimizer.get_status_info())
        
        status_timer = rospy.Timer(rospy.Duration(15.0), print_status)
        
        rospy.loginfo("Enhanced GPS Loop Closure Optimizer running...")
        rospy.loginfo("Supported loop types:")
        rospy.loginfo("   Start-end loops: Traditional trajectory closure")
        rospy.loginfo("   Intermediate loops: Path crossing loops")
        rospy.loginfo("   Revisit loops: Multiple visits to same area")

        rospy.spin()

    except rospy.ROSInterruptException:
        rospy.loginfo("Enhanced GPS Loop Closure Optimizer stopped")

if __name__ == '__main__':
    main()
