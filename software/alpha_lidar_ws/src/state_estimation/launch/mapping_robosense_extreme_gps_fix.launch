<?xml version="1.0"?>
<launch>
    <!-- 极端GPS偏移修复启动文件 - 针对首尾偏离几十米的问题 -->
    
    <!-- 数据包路径参数 -->
    <arg name="bag_path" default="" />
    
    <!-- 极端偏移修复预设参数 -->
    <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="extreme_offset_fix_preset" />
    
    <!-- 基础SLAM系统配置 -->
    <rosparam command="load" file="$(find state_estimation)/config/rs16_rotation_v2.yaml"/>
    
    <!-- 覆盖GPS参数为极端修复模式 -->
    <rosparam>
        gps:
            enable_correction: true
            enable_loop_closure: true
            enable_plane_constraint: true
            enable_icp_loop_closure: true
            
            # 极端校正参数
            height_correction_threshold: 0.01      # 1cm阈值，极度敏感
            correction_rate: 0.95                  # 95%校正率，接近完全校正
            loop_closure_distance: 150.0           # 150m回环检测距离
            loop_closure_min_distance: 5.0         # 5m最小轨迹长度
            
            # 极端ICP参数
            icp_trigger_distance: 200.0            # 200m ICP触发距离
            keyframe_skip: 5                       # 5帧间隔，更密集关键帧
            icp_fitness_threshold: 10.0            # 极度放宽ICP阈值
            icp_max_correspondence_distance: 5.0   # 5m最大对应距离
            
            # 极端平面约束参数
            plane_constraint_weight: 0.98          # 98%约束权重，接近最大
            xy_correction_threshold: 0.005         # 5mm阈值，极度敏感
            xy_correction_rate: 0.98               # 98%校正率，接近完全校正
            constraint_window_size: 20             # 20帧约束窗口
    </rosparam>
    
    <!-- 启动核心SLAM节点 -->
    <node pkg="state_estimation" type="state_estimation_node" name="state_estimation_node" output="screen">
        <remap from="/gps/fix" to="/rtk/gnss"/>
        
        <!-- 极端模式专用参数 -->
        <param name="gps/enable_reference_enhancement" value="true" />
        <param name="gps/reference_radius" value="200.0" />
        <param name="gps/enhancement_radius" value="50.0" />
        <param name="gps/start_point_return_threshold" value="200.0" />
    </node>
    
    <!-- RS to Velodyne转换 -->
    <node pkg="state_estimation" type="rs_to_velodyne" name="rs_to_velodyne" output="screen" args="XYZIRT XYZIRT">
        <remap from="/rslidar_points" to="/rslidar_points"/>
    </node>
    
    <!-- 增强版GPS回环检测优化器 -->
    <node name="enhanced_gps_loop_closure_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
        <!-- 极端偏移修复参数 -->
        <param name="gps_topic" value="/rtk/gnss" />
        <param name="loop_closure_distance_threshold" value="150.0" />
        <param name="min_trajectory_length" value="10.0" />
        <param name="gps_quality_threshold" value="-1" />
        <param name="check_interval" value="0.1" />
        <param name="loop_detection_cooldown" value="0.1" />
        
        <!-- 中间回环参数 -->
        <param name="intermediate_loop_threshold" value="12.0" />
        <param name="min_loop_separation" value="20.0" />
        <param name="trajectory_window_size" value="200" />
        
        <!-- 重访回环参数 -->
        <param name="revisit_threshold" value="15.0" />
        <param name="clustering_eps" value="5.0" />
        <param name="min_cluster_size" value="2" />
        
        <remap from="/rtk/gnss" to="/rtk/gnss" />
    </node>
    
    <!-- 增强版SLAM回环检测集成模块 -->
    <node name="enhanced_slam_loop_closure_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
        <!-- 极端搜索参数 -->
        <param name="force_search_radius" value="250.0" />
        <param name="intermediate_search_radius" value="30.0" />
        <param name="revisit_search_radius" value="25.0" />
        
        <!-- 极端匹配阈值 -->
        <param name="start_end_score_threshold" value="5.0" />
        <param name="intermediate_score_threshold" value="0.60" />
        <param name="revisit_score_threshold" value="0.50" />
        
        <!-- 处理参数 -->
        <param name="voxel_leaf_size" value="1.5" />
        <param name="max_search_candidates" value="150" />
        <param name="min_keyframes_for_loop" value="10" />
        <param name="sliding_window_size" value="200" />
        <param name="temporal_consistency_weight" value="0.1" />
        
        <!-- NDT参数 -->
        <param name="ndt_transformation_epsilon" value="0.1" />
        <param name="ndt_step_size" value="0.2" />
        <param name="ndt_resolution" value="2.0" />
        <param name="ndt_max_iterations" value="100" />
        
        <!-- ICP参数 -->
        <param name="icp_max_iterations" value="200" />
        <param name="icp_transformation_epsilon" value="1e-4" />
        <param name="icp_euclidean_fitness_epsilon" value="1e-4" />
    </node>
    
    <!-- RViz可视化 -->
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz"
          args="-d $(find state_estimation)/config/rviz_cfg/rs16.rviz"/>
    
    <!-- 原始点云对比可视化 -->
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz_raw"
          args="-d $(find state_estimation)/config/rviz_cfg/raw_rs16.rviz"/>
    
    <!-- 数据包播放（如果指定） -->
    <group if="$(eval arg('bag_path') != '')">
        <node pkg="rosbag" type="play" name="player" output="screen" args="$(arg bag_path) --pause"/>
    </group>
    
    <!-- 性能监控节点 -->
    <node name="gps_performance_monitor" pkg="state_estimation" type="gps_performance_monitor.py" output="screen" if="false">
        <param name="monitor_interval" value="1.0" />
        <param name="log_file" value="/tmp/extreme_gps_fix_performance.log" />
    </node>
    
</launch>
