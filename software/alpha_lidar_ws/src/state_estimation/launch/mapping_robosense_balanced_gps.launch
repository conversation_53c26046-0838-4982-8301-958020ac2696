<?xml version="1.0"?>
<launch>
    <!-- 平衡GPS约束启动文件 - 避免点云匹配错乱的同时保持校正能力 -->
    
    <!-- 数据包路径参数 -->
    <arg name="bag_path" default="" />
    
    <!-- 基础SLAM系统配置 -->
    <rosparam command="load" file="$(find state_estimation)/config/rs16_rotation_v2.yaml"/>
    
    <!-- 平衡GPS参数配置 -->
    <rosparam>
        gps:
            enable_correction: true
            enable_loop_closure: true
            enable_plane_constraint: true
            enable_icp_loop_closure: true
            
            # 平衡校正参数 - 避免过度校正
            height_correction_threshold: 0.2           # 20cm阈值，适中敏感度
            correction_rate: 0.25                      # 25%校正率，温和校正
            loop_closure_distance: 25.0                # 25m回环检测距离
            loop_closure_min_distance: 30.0            # 30m最小轨迹长度
            
            # 平衡ICP参数
            icp_trigger_distance: 30.0                 # 30m ICP触发距离
            keyframe_skip: 8                           # 8帧间隔，适中密度
            icp_fitness_threshold: 0.4                 # 适中ICP阈值
            icp_max_correspondence_distance: 1.5       # 1.5m最大对应距离
            
            # 平衡平面约束参数
            plane_constraint_weight: 0.3               # 30%约束权重，适中强度
            xy_correction_threshold: 0.3               # 30cm阈值，适中敏感度
            xy_correction_rate: 0.15                   # 15%校正率，温和校正
            constraint_window_size: 30                 # 30帧约束窗口
    </rosparam>
    
    <!-- 启动核心SLAM节点 -->
    <node pkg="state_estimation" type="state_estimation_node" name="state_estimation_node" output="screen">
        <remap from="/gps/fix" to="/rtk/gnss"/>
        
        <!-- 平衡模式专用参数 -->
        <param name="gps/enable_reference_enhancement" value="true" />
        <param name="gps/reference_radius" value="50.0" />
        <param name="gps/enhancement_radius" value="15.0" />
        <param name="gps/start_point_return_threshold" value="50.0" />
    </node>
    
    <!-- RS to Velodyne转换 -->
    <node pkg="state_estimation" type="rs_to_velodyne" name="rs_to_velodyne" output="screen" args="XYZIRT XYZIRT">
        <remap from="/rslidar_points" to="/rslidar_points"/>
    </node>
    
    <!-- 平衡版GPS回环检测优化器 -->
    <node name="enhanced_gps_loop_closure_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
        <!-- 平衡回环检测参数 -->
        <param name="gps_topic" value="/rtk/gnss" />
        <param name="loop_closure_distance_threshold" value="25.0" />
        <param name="min_trajectory_length" value="40.0" />
        <param name="gps_quality_threshold" value="-1" />
        <param name="check_interval" value="1.0" />
        <param name="loop_detection_cooldown" value="3.0" />
        
        <!-- 中间回环参数 -->
        <param name="intermediate_loop_threshold" value="8.0" />
        <param name="min_loop_separation" value="30.0" />
        <param name="trajectory_window_size" value="100" />
        
        <!-- 重访回环参数 -->
        <param name="revisit_threshold" value="10.0" />
        <param name="clustering_eps" value="3.0" />
        <param name="min_cluster_size" value="3" />
        
        <remap from="/rtk/gnss" to="/rtk/gnss" />
    </node>
    
    <!-- 平衡版SLAM回环检测集成模块 -->
    <node name="enhanced_slam_loop_closure_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
        <!-- 平衡搜索参数 -->
        <param name="force_search_radius" value="40.0" />
        <param name="intermediate_search_radius" value="20.0" />
        <param name="revisit_search_radius" value="15.0" />
        
        <!-- 平衡匹配阈值 -->
        <param name="start_end_score_threshold" value="0.6" />
        <param name="intermediate_score_threshold" value="0.4" />
        <param name="revisit_score_threshold" value="0.35" />
        
        <!-- 处理参数 */
        <param name="voxel_leaf_size" value="0.3" />
        <param name="max_search_candidates" value="20" />
        <param name="min_keyframes_for_loop" value="30" />
        <param name="sliding_window_size" value="100" />
        <param name="temporal_consistency_weight" value="0.2" />
        
        <!-- NDT参数 -->
        <param name="ndt_transformation_epsilon" value="0.01" />
        <param name="ndt_step_size" value="0.1" />
        <param name="ndt_resolution" value="1.0" />
        <param name="ndt_max_iterations" value="35" />
        
        <!-- ICP参数 -->
        <param name="icp_max_iterations" value="50" />
        <param name="icp_transformation_epsilon" value="1e-6" />
        <param name="icp_euclidean_fitness_epsilon" value="1e-6" />
    </node>
    
    <!-- RViz可视化 -->
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz"
          args="-d $(find state_estimation)/config/rviz_cfg/rs16.rviz"/>
    
    <!-- 原始点云对比可视化 -->
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz_raw"
          args="-d $(find state_estimation)/config/rviz_cfg/raw_rs16.rviz"/>
    
    <!-- 数据包播放（如果指定） -->
    <group if="$(eval arg('bag_path') != '')">
        <node pkg="rosbag" type="play" name="player" output="screen" args="$(arg bag_path) --pause"/>
    </group>
    
</launch>
