<?xml version="1.0"?>
<launch>
    <!-- 简化版优化SLAM系统启动文件 -->
    <!-- 适用于缺少复杂依赖包的环境 -->
    
    <!-- 参数配置 -->
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="gps_loop_preset" default="poor_gps_preset" />
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/optimized_output" />
    
    <!-- 功能开关 -->
    <arg name="enable_gps_loop_closure" default="true" />
    <arg name="enable_intensity_preservation" default="true" />
    <arg name="enable_adaptive_optimization" default="true" />
    <arg name="enable_simple_analysis" default="true" />
    <arg name="enable_performance_dashboard" default="false" />
    <arg name="enable_intelligent_detection" default="true" />
    <arg name="enable_gps_constraint_diagnosis" default="false" />
    
    <!-- 配置文件路径 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="gps_config_file" default="$(find state_estimation)/config/gps_loop_closure_params.yaml" />
    
    <!-- 基础SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch" />

    <!-- GPS软约束配置参数 -->
    <rosparam param="/state_estimation_node/gps/enable_plane_constraint">false</rosparam>
    <rosparam param="/state_estimation_node/gps/enable_soft_constraint">true</rosparam>
    <rosparam param="/state_estimation_node/gps/soft_constraint_weight">0.1</rosparam>
    
    <!-- GPS回环检测系统 -->
    <group if="$(arg enable_gps_loop_closure)">
        <!-- 加载GPS回环检测配置 -->
        <rosparam file="$(arg gps_config_file)" command="load" />
        <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)" />
        
        <!-- GPS回环检测优化器 -->
        <node name="enhanced_gps_loop_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_gps_loop_closure_optimizer" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_gps_loop_closure_optimizer" />
            <remap from="/rtk/gnss" to="/rtk/gnss" />
        </node>
        
        <!-- SLAM回环检测集成模块 -->
        <node name="enhanced_slam_loop_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_slam_loop_closure_integration" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_slam_loop_closure_integration" />
        </node>
    </group>
    
    <!-- 强度值保持系统 -->
    <group if="$(arg enable_intensity_preservation)">
        <!-- 加载强度保持配置 -->
        <rosparam file="$(arg intensity_config_file)" command="load" />
        <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
        
        <!-- 强度保持PCD保存模块 -->
        <node name="intensity_preserving_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <param name="save_directory" value="$(arg save_directory)" />
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
        
        <!-- 强度值质量监控 -->
        <node name="intensity_quality_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
            <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="statistics_topic" value="/intensity_statistics" />
            <param name="quality_report_topic" value="/pointcloud_quality_report" />
            <param name="report_interval" value="10.0" />
        </node>
    </group>
    
    <!-- 简化强度分析系统 -->
    <group if="$(arg enable_simple_analysis)">
        <node name="simple_intensity_analyzer" pkg="state_estimation" type="simple_intensity_analyzer.py" output="screen">
            <param name="input_topic" value="/velodyne_points" />
            <param name="analysis_report_topic" value="/intensity_analysis_report" />
            <param name="analysis_window_size" value="50" />
            <param name="report_interval" value="15.0" />
            <param name="intensity_outlier_factor" value="3.0" />
            <param name="expected_intensity_min" value="0.0" />
            <param name="expected_intensity_max" value="255.0" />
        </node>
    </group>
    
    <!-- 自适应参数优化系统 -->
    <group if="$(arg enable_adaptive_optimization)">
        <node name="adaptive_parameter_optimizer" pkg="state_estimation" type="adaptive_parameter_optimizer" output="screen">
            <param name="optimization_interval" value="30.0" />
            <param name="learning_rate" value="0.1" />
            <param name="stability_weight" value="0.6" />
            <param name="performance_weight" value="0.4" />
            <param name="history_window_size" value="100" />
            
            <!-- 初始参数 -->
            <param name="initial_loop_distance_threshold" value="8.0" />
            <param name="initial_intermediate_threshold" value="10.0" />
            <param name="initial_search_radius" value="15.0" />
            <param name="initial_score_threshold" value="0.35" />
            <param name="initial_voxel_size" value="0.1" />
        </node>
    </group>
    
    <!-- 简化性能监控（可选） -->
    <group if="$(arg enable_performance_dashboard)">
        <node name="simple_performance_monitor" pkg="state_estimation" type="simple_performance_monitor.py" output="screen">
            <param name="report_interval" value="30.0" />
            <param name="max_history" value="100" />
        </node>
    </group>

    <!-- 智能首尾回环检测系统 -->
    <group if="$(arg enable_intelligent_detection)">
        <node name="intelligent_start_end_detector" pkg="state_estimation" type="intelligent_start_end_detector.py" output="screen">
            <param name="gps_topic" value="/rtk/gnss" />
            <param name="departure_threshold" value="30.0" />
            <param name="return_threshold" value="50.0" />
            <param name="min_trajectory_points" value="100" />
            <param name="gps_quality_threshold" value="-1" />
        </node>

        <!-- 强制首尾回环精细匹配器 -->
        <node name="force_start_end_loop_matcher" pkg="state_estimation" type="force_start_end_loop_matcher" output="screen">
            <param name="voxel_size" value="0.05" />
            <param name="search_radius" value="50.0" />
            <param name="max_correspondence_distance" value="1.0" />
            <param name="max_iterations" value="200" />
            <param name="transformation_epsilon" value="1e-8" />
            <param name="euclidean_fitness_epsilon" value="1e-6" />
            <param name="use_reciprocal_correspondences" value="true" />
            <param name="outlier_rejection_threshold" value="0.5" />
            <param name="max_keyframes" value="1000" />
        </node>

        <!-- 智能GPS约束控制器 -->
        <node name="intelligent_gps_constraint_controller" pkg="state_estimation" type="intelligent_gps_constraint_controller.py" output="screen">
            <param name="icp_fitness_threshold" value="0.3" />
            <param name="constraint_disable_fitness" value="0.5" />
            <param name="constraint_enable_fitness" value="0.2" />
            <param name="velocity_threshold" value="2.0" />
            <param name="angular_velocity_threshold" value="0.5" />
            <param name="constraint_cooldown" value="5.0" />
        </node>

        <!-- GPS软约束回环检测器 -->
        <node name="gps_soft_constraint_loop_detector" pkg="state_estimation" type="gps_soft_constraint_loop_detector.py" output="screen">
            <param name="good_gps_threshold" value="15.0" />
            <param name="poor_gps_threshold" value="30.0" />
            <param name="trajectory_similarity_threshold" value="20.0" />
            <param name="min_time_gap" value="30.0" />
            <param name="min_trajectory_length" value="50.0" />
            <param name="heading_similarity_threshold" value="45.0" />
        </node>

        <!-- GPS参考位置增强SLAM匹配器 -->
        <node name="gps_reference_enhanced_slam" pkg="state_estimation" type="gps_reference_enhanced_slam.py" output="screen">
            <param name="gps_reference_radius" value="100.0" />
            <param name="slam_enhancement_radius" value="100.0" />
            <param name="min_time_gap" value="30.0" />
            <param name="trajectory_length_threshold" value="200.0" />
            <param name="enhanced_search_radius" value="120.0" />
            <param name="enhanced_voxel_size" value="0.02" />
            <param name="enhanced_max_iterations" value="600" />
            <param name="multi_resolution_levels" value="3" />
        </node>

        <!-- GPS 100米回环检测器 -->
        <node name="gps_100m_loop_detector" pkg="state_estimation" type="gps_100m_loop_detector.py" output="screen">
            <param name="start_point_threshold" value="100.0" />
            <param name="min_departure_distance" value="150.0" />
            <param name="min_trajectory_length" value="200.0" />
            <param name="loop_detection_cooldown" value="30.0" />
        </node>
    </group>

    <!-- GPS约束诊断系统 -->
    <group if="$(arg enable_gps_constraint_diagnosis)">
        <node name="gps_constraint_analyzer" pkg="state_estimation" type="gps_constraint_analyzer.py" output="screen">
            <param name="analysis_window" value="50" />
            <param name="error_threshold" value="0.5" />
            <param name="time_sync_threshold" value="0.1" />
        </node>

        <node name="smart_gps_constraint_controller" pkg="state_estimation" type="smart_gps_constraint_controller.py" output="screen">
            <param name="matching_quality_threshold" value="0.7" />
            <param name="constraint_enable_delay" value="10.0" />
            <param name="constraint_disable_delay" value="5.0" />
            <param name="evaluation_window" value="20" />
        </node>
    </group>

    <!-- 全局参数配置 -->
    <rosparam>
        # 简化系统配置
        optimized_slam_system_simple:
            # 强度值保持配置
            intensity_preservation:
                preserve_original_intensity: true
                enable_intensity_restoration: true
                save_with_loop_correction: true
                intensity_range_validation: true
                geometric_consistency_check: true
                save_incremental_maps: true
                save_final_optimized_map: true
                publish_intensity_statistics: true
                log_processing_performance: true
            
            # GPS回环检测配置
            gps_loop_closure:
                enable_intermediate_loops: true
                enable_revisit_loops: true
                adaptive_thresholds: true
                quality_based_weighting: true
                multi_hypothesis_tracking: true
            
            # 自适应优化配置
            adaptive_optimization:
                enable_real_time_tuning: true
                performance_based_adjustment: true
                quality_feedback_loop: true
                resource_aware_optimization: true
                learning_enabled: true
            
            # 简化分析配置
            simple_analysis:
                enable_basic_anomaly_detection: true
                enable_quality_monitoring: true
                enable_statistical_analysis: true
                disable_advanced_ml: true
                disable_complex_clustering: true
    </rosparam>
    
    <!-- 系统状态监控 -->
    <node name="system_status_monitor" pkg="state_estimation" type="system_status_monitor.py" output="screen" if="false">
        <!-- 简化的系统状态监控器（可选） -->
        <param name="check_interval" value="30.0" />
        <param name="status_report_topic" value="/system_status_report" />
    </node>
    
</launch>
