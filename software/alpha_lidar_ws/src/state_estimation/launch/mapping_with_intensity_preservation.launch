<?xml version="1.0"?>
<launch>
    <!-- 带强度值保持的完整SLAM系统启动文件 -->
    <!-- 确保输出的PCD文件包含真实的点云强度值 -->
    
    <!-- 强度保持配置参数 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="gps_loop_preset" default="poor_gps_preset" />
    
    <!-- 保存目录配置 -->
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/intensity_preserved" />
    <arg name="enable_intensity_preservation" default="true" />
    <arg name="enable_gps_loop_closure" default="true" />
    
    <!-- GPS回环检测系统（如果启用） -->
    <group if="$(arg enable_gps_loop_closure)">
        <!-- 只包含GPS回环检测部分，避免重复包含基础SLAM系统 -->

        <!-- 包含原始SLAM系统 -->
        <include file="$(find state_estimation)/launch/mapping_robosense.launch">
            <!-- 传递必要的参数 -->
        </include>

        <!-- 加载GPS回环检测配置 -->
        <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" />

        <!-- 加载预设配置 -->
        <group if="$(eval arg('gps_loop_preset') != 'none')">
            <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="$(arg gps_loop_preset)" />
        </group>

        <!-- GPS回环检测优化器 -->
        <node name="enhanced_gps_loop_closure_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
            <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="enhanced_gps_loop_closure_optimizer" />
            <group if="$(eval arg('gps_loop_preset') != 'none')">
                <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="$(arg gps_loop_preset)/enhanced_gps_loop_closure_optimizer" />
            </group>
            <remap from="/rtk/gnss" to="/rtk/gnss" />
        </node>

        <!-- SLAM回环检测集成模块 -->
        <node name="enhanced_slam_loop_closure_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
            <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="enhanced_slam_loop_closure_integration" />
            <group if="$(eval arg('gps_loop_preset') != 'none')">
                <rosparam file="$(find state_estimation)/config/gps_loop_closure_params.yaml" command="load" ns="$(arg gps_loop_preset)/enhanced_slam_loop_closure_integration" />
            </group>
        </node>
    </group>

    <!-- 如果不启用GPS回环检测，只启动基础SLAM系统 -->
    <group unless="$(arg enable_gps_loop_closure)">
        <include file="$(find state_estimation)/launch/mapping_robosense.launch">
            <!-- 传递必要的参数 -->
        </include>
    </group>
    
    <!-- 强度值保持的PCD保存模块 -->
    <group if="$(arg enable_intensity_preservation)">
        <node name="intensity_preserving_pcd_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <!-- 加载强度保持配置 -->
            <rosparam file="$(arg intensity_config_file)" command="load" />
            
            <!-- 加载预设配置 -->
            <group if="$(eval arg('intensity_preset') != 'none')">
                <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
            </group>
            
            <!-- 覆盖保存目录 -->
            <param name="save_directory" value="$(arg save_directory)" />
            
            <!-- 话题重映射 -->
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
        
        <!-- 强度值质量监控节点 -->
        <node name="intensity_quality_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
            <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="statistics_topic" value="/intensity_statistics" />
            <param name="quality_report_topic" value="/pointcloud_quality_report" />
        </node>
    </group>
    
    <!-- 参数服务器配置 -->
    <rosparam>
        # 强度值保持系统配置
        intensity_preservation:
            # 核心功能
            preserve_original_intensity: true      # 保持原始强度值
            enable_intensity_restoration: true     # 启用强度值恢复
            save_with_loop_correction: true        # 保存回环校正后的点云
            
            # 质量控制
            intensity_range_validation: true       # 强度值范围验证
            geometric_consistency_check: true      # 几何一致性检查
            
            # 文件保存
            save_incremental_maps: true            # 保存增量地图
            save_final_optimized_map: true         # 保存最终优化地图
            
            # 监控和统计
            publish_intensity_statistics: true     # 发布强度统计
            log_processing_performance: true       # 记录处理性能
    </rosparam>
    
    <!-- 日志配置 -->
    <env name="ROSCONSOLE_CONFIG_FILE" value="$(find state_estimation)/config/rosconsole.conf"/>
    
</launch>
