<?xml version="1.0"?>
<launch>
    <!-- GPS软约束SLAM系统 - 深度优化配置 -->
    
    <!-- 参数配置 -->
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/gps_soft_constraint" />
    <arg name="gps_topic" default="/rtk/gnss" />
    <arg name="lidar_topic" default="/velodyne_points" />
    <arg name="enable_rviz" default="true" />
    <arg name="enable_intensity_save" default="true" />
    <arg name="enable_performance_monitor" default="false" />
    
    <!-- GPS软约束回环检测参数 -->
    <arg name="gps_high_quality_threshold" default="4" />
    <arg name="gps_medium_quality_threshold" default="1" />
    <arg name="high_quality_proximity" default="12.0" />
    <arg name="medium_quality_proximity" default="20.0" />
    <arg name="low_quality_proximity" default="30.0" />
    <arg name="slam_strict_threshold" default="6.0" />
    <arg name="slam_normal_threshold" default="12.0" />
    <arg name="slam_loose_threshold" default="20.0" />
    <arg name="min_time_gap" default="25.0" />
    <arg name="heading_threshold" default="40.0" />
    
    <!-- SLAM核心参数 -->
    <arg name="voxel_size" default="0.5" />
    <arg name="max_iterations" default="30" />
    <arg name="transformation_epsilon" default="1e-6" />
    <arg name="euclidean_fitness_epsilon" default="1e-6" />
    
    <!-- 禁用GPS平面约束 -->
    <param name="/state_estimation_node/gps/enable_plane_constraint" value="false" />
    <param name="/state_estimation_node/gps/use_soft_constraint" value="true" />
    
    <!-- SLAM核心节点 -->
    <node name="state_estimation_node" pkg="state_estimation" type="state_estimation_node" output="screen">
        <param name="lidar_topic" value="$(arg lidar_topic)" />
        <param name="gps_topic" value="$(arg gps_topic)" />
        <param name="voxel_size" value="$(arg voxel_size)" />
        <param name="max_iterations" value="$(arg max_iterations)" />
        <param name="transformation_epsilon" value="$(arg transformation_epsilon)" />
        <param name="euclidean_fitness_epsilon" value="$(arg euclidean_fitness_epsilon)" />
        
        <!-- 禁用硬约束，启用软约束 -->
        <param name="enable_gps_plane_constraint" value="false" />
        <param name="enable_gps_soft_guidance" value="true" />
        <param name="gps_guidance_weight" value="0.1" />
        
        <!-- 回环检测参数 -->
        <param name="loop_closure_search_radius" value="50.0" />
        <param name="loop_closure_fitness_threshold" value="0.3" />
        <param name="enable_adaptive_loop_closure" value="true" />
    </node>
    
    <!-- GPS软约束回环检测器 -->
    <node name="gps_soft_loop_detector" pkg="state_estimation" type="gps_soft_loop_detector.py" output="screen">
        <param name="gps_high_quality_threshold" value="$(arg gps_high_quality_threshold)" />
        <param name="gps_medium_quality_threshold" value="$(arg gps_medium_quality_threshold)" />
        <param name="high_quality_proximity" value="$(arg high_quality_proximity)" />
        <param name="medium_quality_proximity" value="$(arg medium_quality_proximity)" />
        <param name="low_quality_proximity" value="$(arg low_quality_proximity)" />
        <param name="slam_strict_threshold" value="$(arg slam_strict_threshold)" />
        <param name="slam_normal_threshold" value="$(arg slam_normal_threshold)" />
        <param name="slam_loose_threshold" value="$(arg slam_loose_threshold)" />
        <param name="min_time_gap" value="$(arg min_time_gap)" />
        <param name="heading_threshold" value="$(arg heading_threshold)" />
        <param name="min_trajectory_length" value="40.0" />
    </node>
    
    <!-- 软约束回环处理器 -->
    <node name="soft_constraint_loop_processor" pkg="state_estimation" type="soft_constraint_loop_processor.py" output="screen">
        <param name="confidence_threshold" value="0.6" />
        <param name="max_correction_distance" value="5.0" />
        <param name="correction_weight" value="0.3" />
        <param name="enable_gradual_correction" value="true" />
    </node>
    
    <!-- 强度保持模块 -->
    <group if="$(arg enable_intensity_save)">
        <node name="intensity_preserving_pcd_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <param name="save_directory" value="$(arg save_directory)" />
            <param name="save_interval" value="8.0" />
            <param name="enable_intensity_preservation" value="true" />
            <param name="intensity_scale_factor" value="1.0" />
            <param name="min_intensity_threshold" value="0.0" />
            <param name="max_intensity_threshold" value="255.0" />
        </node>
    </group>
    
    <!-- 智能首尾检测器 -->
    <node name="intelligent_start_end_detector" pkg="state_estimation" type="intelligent_start_end_detector.py" output="screen">
        <param name="gps_topic" value="$(arg gps_topic)" />
        <param name="departure_threshold" value="25.0" />
        <param name="return_threshold" value="35.0" />
        <param name="min_trajectory_points" value="80" />
        <param name="gps_quality_threshold" value="-1" />
        <param name="enable_quality_filtering" value="true" />
    </node>
    
    <!-- 性能监控器 (可选) -->
    <group if="$(arg enable_performance_monitor)">
        <node name="performance_monitor" pkg="state_estimation" type="simple_performance_monitor.py" output="screen">
            <param name="monitor_interval" value="5.0" />
            <param name="enable_cpu_monitor" value="true" />
            <param name="enable_memory_monitor" value="true" />
        </node>
    </group>
    
    <!-- 质量分析器 -->
    <node name="gps_quality_analyzer" pkg="state_estimation" type="gps_quality_analyzer.py" output="screen">
        <param name="analysis_window" value="100" />
        <param name="report_interval" value="10.0" />
        <param name="enable_quality_prediction" value="true" />
    </node>
    
    <!-- RViz可视化 -->
    <group if="$(arg enable_rviz)">
        <node name="rviz" pkg="rviz" type="rviz" args="-d $(find state_estimation)/rviz/gps_soft_constraint_slam.rviz" output="screen" />
    </group>
    
    <!-- 参数服务器设置 -->
    <rosparam>
        # GPS软约束系统参数
        gps_soft_constraint:
          enable_quality_adaptation: true
          quality_adaptation_rate: 0.1
          min_confidence_threshold: 0.4
          max_correction_per_step: 2.0
          
        # 回环检测优化参数
        loop_closure_optimization:
          enable_multi_hypothesis: true
          hypothesis_count: 3
          consensus_threshold: 0.7
          outlier_rejection_ratio: 0.2
          
        # 轨迹平滑参数
        trajectory_smoothing:
          enable_smoothing: true
          smoothing_window: 10
          smoothing_weight: 0.3
          preserve_sharp_turns: true
          
        # 质量自适应参数
        quality_adaptation:
          high_quality_weight: 1.0
          medium_quality_weight: 0.7
          low_quality_weight: 0.4
          poor_quality_weight: 0.2
          quality_transition_smoothing: 0.8
    </rosparam>
    
</launch>
