<launch>
    <!-- Launch file for velodyne16 VLP-16 LiDAR -->

    <arg name="rviz" default="true"/>
    <arg name="debug" default="false"/>
    <arg name="from_packets" default="false"/>
    <arg name="bag_path" default=""/>

    <rosparam command="load" file="$(find state_estimation)/config/rs16_rotation_v2.yaml"/>

    <group if="$(eval arg('debug') == False)">
        <node pkg="state_estimation" type="state_estimation_node" name="state_estimation_node" output="screen">
            <!-- GPS topic remapping - 修改为实际的GPS topic -->
            <remap from="/gps/fix" to="/rtk/gnss"/>
        </node>
    </group>

    <!--- RS to Velodyne -->
    <node pkg="state_estimation" type="rs_to_velodyne" name="rs_to_velodyne" output="screen" args="XYZIRT XYZIRT">
        <remap from="/rslidar_points" to="/rslidar_points"/>
    </node>

    <group if="$(arg rviz)">
        <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz"
              args="-d $(find state_estimation)/config/rviz_cfg/rs16.rviz"/>
    </group>
    <group if="$(arg rviz)">
        <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz_raw"
              args="-d $(find state_estimation)/config/rviz_cfg/raw_rs16.rviz"/>
    </group>

    <!--- result logging - 暂时关闭 -->
    <arg name="logging" default="false"/>
    <group if="$(arg logging)">
        <node pkg="state_estimation" type="logger.py" name="intermediate_result_logger" output="screen"/>
    </group>

    <group if="$(eval arg('bag_path') != '')">
        <node pkg="rosbag" type="play" name="player" output="screen" args="$(arg bag_path) --pause"/>
    </group>

</launch>
