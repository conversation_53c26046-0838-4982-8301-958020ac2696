<?xml version="1.0"?>
<launch>
    <!-- 完整优化的SLAM系统启动文件 -->
    <!-- 包含所有高级功能：GPS回环检测、强度保持、自适应优化、性能监控 -->
    
    <!-- 参数配置 -->
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="gps_loop_preset" default="poor_gps_preset" />
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/optimized_output" />
    
    <!-- 功能开关 -->
    <arg name="enable_gps_loop_closure" default="true" />
    <arg name="enable_intensity_preservation" default="true" />
    <arg name="enable_adaptive_optimization" default="true" />
    <arg name="enable_advanced_analysis" default="true" />
    <arg name="enable_performance_dashboard" default="true" />
    
    <!-- 配置文件路径 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="gps_config_file" default="$(find state_estimation)/config/gps_loop_closure_params.yaml" />
    
    <!-- 基础SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch" />
    
    <!-- GPS回环检测系统 -->
    <group if="$(arg enable_gps_loop_closure)">
        <!-- 加载GPS回环检测配置 -->
        <rosparam file="$(arg gps_config_file)" command="load" />
        <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)" />
        
        <!-- GPS回环检测优化器 -->
        <node name="enhanced_gps_loop_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_gps_loop_closure_optimizer" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_gps_loop_closure_optimizer" />
            <remap from="/rtk/gnss" to="/rtk/gnss" />
        </node>
        
        <!-- SLAM回环检测集成模块 -->
        <node name="enhanced_slam_loop_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_slam_loop_closure_integration" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_slam_loop_closure_integration" />
        </node>
    </group>
    
    <!-- 强度值保持系统 -->
    <group if="$(arg enable_intensity_preservation)">
        <!-- 加载强度保持配置 -->
        <rosparam file="$(arg intensity_config_file)" command="load" />
        <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
        
        <!-- 强度保持PCD保存模块 -->
        <node name="intensity_preserving_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <param name="save_directory" value="$(arg save_directory)" />
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
        
        <!-- 强度值质量监控 -->
        <node name="intensity_quality_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
            <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="statistics_topic" value="/intensity_statistics" />
            <param name="quality_report_topic" value="/pointcloud_quality_report" />
            <param name="report_interval" value="10.0" />
        </node>
    </group>
    
    <!-- 高级强度分析系统 -->
    <group if="$(arg enable_advanced_analysis)">
        <!-- 尝试使用高级分析器，如果失败则使用简化版本 -->
        <node name="advanced_intensity_analyzer" pkg="state_estimation" type="advanced_intensity_analyzer.py" output="screen" respawn="false">
            <param name="input_topic" value="/velodyne_points" />
            <param name="output_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="analysis_report_topic" value="/intensity_analysis_report" />
            <param name="corrected_intensity_topic" value="/corrected_intensity_cloud" />
            <param name="analysis_window_size" value="50" />
            <param name="anomaly_detection_threshold" value="0.1" />
            <param name="report_interval" value="15.0" />
        </node>

        <!-- 备用简化分析器 -->
        <node name="simple_intensity_analyzer_backup" pkg="state_estimation" type="simple_intensity_analyzer.py" output="screen" respawn="false" if="false">
            <param name="input_topic" value="/velodyne_points" />
            <param name="analysis_report_topic" value="/intensity_analysis_report" />
            <param name="analysis_window_size" value="50" />
            <param name="report_interval" value="15.0" />
        </node>
    </group>
    
    <!-- 自适应参数优化系统 -->
    <group if="$(arg enable_adaptive_optimization)">
        <node name="adaptive_parameter_optimizer" pkg="state_estimation" type="adaptive_parameter_optimizer" output="screen">
            <param name="optimization_interval" value="30.0" />
            <param name="learning_rate" value="0.1" />
            <param name="stability_weight" value="0.6" />
            <param name="performance_weight" value="0.4" />
            <param name="history_window_size" value="100" />
            
            <!-- 初始参数 -->
            <param name="initial_loop_distance_threshold" value="8.0" />
            <param name="initial_intermediate_threshold" value="10.0" />
            <param name="initial_search_radius" value="15.0" />
            <param name="initial_score_threshold" value="0.35" />
            <param name="initial_voxel_size" value="0.1" />
        </node>
    </group>
    
    <!-- 性能监控仪表板 -->
    <group if="$(arg enable_performance_dashboard)">
        <node name="performance_dashboard" pkg="state_estimation" type="performance_dashboard.py" output="screen">
            <!-- 仪表板将在单独的GUI窗口中运行 -->
        </node>
    </group>
    
    <!-- 全局参数配置 -->
    <rosparam>
        # 系统级配置
        optimized_slam_system:
            # 强度值保持配置
            intensity_preservation:
                preserve_original_intensity: true
                enable_intensity_restoration: true
                save_with_loop_correction: true
                intensity_range_validation: true
                geometric_consistency_check: true
                save_incremental_maps: true
                save_final_optimized_map: true
                publish_intensity_statistics: true
                log_processing_performance: true
            
            # GPS回环检测配置
            gps_loop_closure:
                enable_intermediate_loops: true
                enable_revisit_loops: true
                adaptive_thresholds: true
                quality_based_weighting: true
                multi_hypothesis_tracking: true
            
            # 自适应优化配置
            adaptive_optimization:
                enable_real_time_tuning: true
                performance_based_adjustment: true
                quality_feedback_loop: true
                resource_aware_optimization: true
                learning_enabled: true
            
            # 性能监控配置
            performance_monitoring:
                enable_real_time_dashboard: true
                enable_detailed_logging: true
                enable_anomaly_detection: true
                enable_predictive_analysis: true
                export_performance_data: true
    </rosparam>
    
    <!-- 系统健康检查 -->
    <node name="system_health_checker" pkg="state_estimation" type="system_health_checker.py" output="screen" if="false">
        <!-- 系统健康检查器（可选） -->
        <param name="check_interval" value="60.0" />
        <param name="health_report_topic" value="/system_health_report" />
    </node>
    
</launch>
