#!/bin/bash

# 最终稳定解决方案

echo "=========================================="
echo "🎯 SLAM崩溃最终稳定解决方案"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo ""
echo "选择解决方案:"
echo "1) 完整重新编译和修复（推荐）"
echo "2) 仅使用内存安全启动"
echo "3) GDB调试模式"
echo "4) 极简模式（最小功能）"
echo ""

read -p "请选择 (1-4): " solution_choice

case $solution_choice in
    1)
        echo ""
        echo "🔧 完整重新编译和修复"
        echo "===================="
        
        echo "步骤1: 重新编译系统"
        if [ ! -f "rebuild_with_safety_checks.sh" ]; then
            echo "❌ 重新编译脚本不存在"
            exit 1
        fi
        
        chmod +x rebuild_with_safety_checks.sh
        ./rebuild_with_safety_checks.sh
        
        if [ $? -ne 0 ]; then
            echo "❌ 重新编译失败"
            exit 1
        fi
        
        echo ""
        echo "步骤2: 深度诊断"
        chmod +x deep_fix_slam_crash.sh
        ./deep_fix_slam_crash.sh "$BAG_FILE"
        
        echo ""
        echo "步骤3: 内存安全启动"
        if [ -f "start_slam_memory_safe.sh" ]; then
            chmod +x start_slam_memory_safe.sh
            ./start_slam_memory_safe.sh "$BAG_FILE"
        else
            echo "❌ 内存安全启动脚本不存在"
            exit 1
        fi
        ;;
        
    2)
        echo ""
        echo "🛡️  内存安全启动模式"
        echo "=================="
        
        # 检查编译结果
        if [ ! -f "devel/lib/state_estimation/state_estimation_node" ]; then
            echo "❌ SLAM节点未编译，请先选择选项1"
            exit 1
        fi
        
        # 运行深度诊断
        if [ -f "deep_fix_slam_crash.sh" ]; then
            chmod +x deep_fix_slam_crash.sh
            ./deep_fix_slam_crash.sh "$BAG_FILE"
        fi
        
        # 运行内存安全启动
        if [ -f "start_slam_memory_safe.sh" ]; then
            chmod +x start_slam_memory_safe.sh
            ./start_slam_memory_safe.sh "$BAG_FILE"
        else
            echo "创建临时内存安全启动..."
            
            # 创建临时的内存安全启动
            cat > /tmp/temp_memory_safe.sh << 'EOFTEMP'
#!/bin/bash
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 设置内存限制
ulimit -v 4194304
ulimit -c unlimited

# 启动roscore
if ! pgrep -x "roscore" > /dev/null; then
    roscore &
    sleep 3
fi

# 设置极度保守参数
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/min_points_threshold 2000
rosparam set /state_estimation_node/max_points_threshold 50000
rosparam set /state_estimation_node/voxel_size 2.0
rosparam set /state_estimation_node/downsample_ratio 0.1
rosparam set /state_estimation_node/max_iterations 5
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false

echo "启动bag播放..."
rosbag play "$1" --clock --pause &
BAG_PID=$!
sleep 5

echo "启动SLAM节点..."
timeout 300 rosrun state_estimation state_estimation_node &
SLAM_PID=$!
sleep 10

if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM节点启动成功"
    kill $BAG_PID 2>/dev/null
    sleep 2
    rosbag play "$1" --clock --rate=0.1 &
    BAG_PID=$!
    echo "✅ 开始播放数据（0.1倍速）"
    
    trap 'kill $SLAM_PID $BAG_PID 2>/dev/null; exit 0' SIGINT
    wait
else
    echo "❌ SLAM节点启动失败"
    kill $BAG_PID 2>/dev/null
    exit 1
fi
EOFTEMP
            
            chmod +x /tmp/temp_memory_safe.sh
            /tmp/temp_memory_safe.sh "$BAG_FILE"
        fi
        ;;
        
    3)
        echo ""
        echo "🐛 GDB调试模式"
        echo "=============="
        
        if [ -f "debug_slam_crash.sh" ]; then
            chmod +x debug_slam_crash.sh
            ./debug_slam_crash.sh "$BAG_FILE"
        else
            echo "创建临时GDB调试..."
            
            # 检查GDB
            if ! command -v gdb &> /dev/null; then
                echo "安装GDB..."
                sudo apt install -y gdb
            fi
            
            # 启用核心转储
            ulimit -c unlimited
            
            # 启动bag播放
            rosbag play "$BAG_FILE" --clock --pause &
            BAG_PID=$!
            sleep 3
            
            # 设置安全参数
            rosparam set /state_estimation_node/enable_gps false
            rosparam set /state_estimation_node/skip_empty_scans true
            rosparam set /state_estimation_node/voxel_size 2.0
            
            echo "启动GDB调试..."
            echo "在GDB中输入 'run' 开始运行"
            gdb --args devel/lib/state_estimation/state_estimation_node
            
            kill $BAG_PID 2>/dev/null
        fi
        ;;
        
    4)
        echo ""
        echo "🔧 极简模式（最小功能）"
        echo "===================="
        
        # 检查roscore
        if ! pgrep -x "roscore" > /dev/null; then
            echo "启动 roscore..."
            roscore &
            sleep 3
        fi
        
        # 设置极简参数
        echo "设置极简参数..."
        rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
        rosparam set /state_estimation_node/imu_topic "/imu/data"
        
        # 极度保守的参数
        rosparam set /state_estimation_node/skip_empty_scans true
        rosparam set /state_estimation_node/min_points_threshold 5000
        rosparam set /state_estimation_node/max_points_threshold 30000
        rosparam set /state_estimation_node/voxel_size 3.0
        rosparam set /state_estimation_node/downsample_ratio 0.05
        rosparam set /state_estimation_node/max_iterations 3
        rosparam set /state_estimation_node/transformation_epsilon 1e-2
        
        # 禁用所有非必要功能
        rosparam set /state_estimation_node/enable_gps false
        rosparam set /state_estimation_node/enable_loop_closure false
        rosparam set /state_estimation_node/enable_icp_loop_closure false
        rosparam set /state_estimation_node/enable_intensity_processing false
        rosparam set /state_estimation_node/enable_feature_extraction false
        rosparam set /state_estimation_node/enable_plane_constraint false
        
        echo "✅ 极简参数设置完成"
        
        echo ""
        echo "启动极简SLAM系统..."
        
        # 启动bag播放
        echo "启动bag播放（暂停状态）..."
        rosbag play "$BAG_FILE" --clock --pause &
        BAG_PID=$!
        sleep 5
        
        # 启动SLAM节点
        echo "启动SLAM节点..."
        rosrun state_estimation state_estimation_node &
        SLAM_PID=$!
        sleep 15
        
        if ps -p $SLAM_PID > /dev/null; then
            echo "✅ SLAM节点启动成功"
            
            # 开始播放数据
            echo "开始播放数据（极慢速度）..."
            kill $BAG_PID 2>/dev/null
            sleep 2
            rosbag play "$BAG_FILE" --clock --rate=0.05 &
            BAG_PID=$!
            
            echo "✅ 极简模式启动成功"
            echo ""
            echo "系统信息："
            echo "  播放速度: 0.05倍速（极慢）"
            echo "  体素大小: 3.0m（极大）"
            echo "  最大迭代: 3次（极少）"
            echo "  点云限制: 5000-30000点"
            echo ""
            echo "监控命令："
            echo "  rostopic hz /velodyne_points"
            echo "  rostopic echo /aft_mapped_to_init"
            echo ""
            echo "按 Ctrl+C 停止系统"
            
            trap 'kill $SLAM_PID $BAG_PID 2>/dev/null; echo "系统已停止"; exit 0' SIGINT
            
            # 监控循环
            while ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; do
                sleep 15
                echo "极简模式运行中... $(date '+%H:%M:%S')"
                
                # 检查内存使用
                MEMORY_MB=$(ps -p $SLAM_PID -o rss= 2>/dev/null | awk '{print int($1/1024)}')
                if [ -n "$MEMORY_MB" ]; then
                    echo "内存使用: ${MEMORY_MB}MB"
                fi
            done
            
            if ! ps -p $SLAM_PID > /dev/null; then
                echo "❌ SLAM节点意外停止"
            fi
            
            if ! ps -p $BAG_PID > /dev/null; then
                echo "✅ 数据播放完成"
            fi
            
            kill $SLAM_PID $BAG_PID 2>/dev/null
            
        else
            echo "❌ SLAM节点启动失败"
            kill $BAG_PID 2>/dev/null
            
            echo ""
            echo "极简模式也失败，建议："
            echo "1. 检查系统资源: free -h && df -h"
            echo "2. 重新编译: ./rebuild_with_safety_checks.sh"
            echo "3. 使用GDB调试: 选择选项3"
            echo "4. 检查bag文件: rosbag info $BAG_FILE"
            
            exit 1
        fi
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "最终稳定解决方案执行完成"
echo "=========================================="
