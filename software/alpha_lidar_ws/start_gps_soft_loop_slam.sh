#!/bin/bash

# GPS软约束回环SLAM系统启动脚本
# 使用GPS引导的软约束回环检测，避免点云拼接问题

echo "=========================================="
echo "🎯 GPS软约束回环SLAM系统启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "✅ 禁用GPS平面约束，避免点云拼接问题"
echo "✅ 启用GPS软约束回环检测"
echo "✅ 根据GPS质量动态调整检测阈值"
echo "✅ 保留所有其他优化功能"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_soft_loop_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "GPS软约束配置选项："
echo "1) 高质量GPS环境 (RTK/DGPS为主)"
echo "2) 混合质量GPS环境 (推荐)"
echo "3) 低质量GPS环境 (单点定位为主)"
echo ""

read -p "请选择GPS环境 (1-3): " gps_env_choice

case $gps_env_choice in
    1)
        echo "配置高质量GPS环境..."
        GOOD_GPS_THRESHOLD=10.0
        POOR_GPS_THRESHOLD=20.0
        TRAJECTORY_THRESHOLD=15.0
        MIN_TIME_GAP=25.0
        HEADING_THRESHOLD=30.0
        echo "✅ 高质量GPS配置"
        ;;
    2)
        echo "配置混合质量GPS环境..."
        GOOD_GPS_THRESHOLD=15.0
        POOR_GPS_THRESHOLD=30.0
        TRAJECTORY_THRESHOLD=20.0
        MIN_TIME_GAP=30.0
        HEADING_THRESHOLD=45.0
        echo "✅ 混合质量GPS配置"
        ;;
    3)
        echo "配置低质量GPS环境..."
        GOOD_GPS_THRESHOLD=25.0
        POOR_GPS_THRESHOLD=50.0
        TRAJECTORY_THRESHOLD=30.0
        MIN_TIME_GAP=40.0
        HEADING_THRESHOLD=60.0
        echo "✅ 低质量GPS配置"
        ;;
    *)
        echo "使用默认混合质量配置..."
        GOOD_GPS_THRESHOLD=15.0
        POOR_GPS_THRESHOLD=30.0
        TRAJECTORY_THRESHOLD=20.0
        MIN_TIME_GAP=30.0
        HEADING_THRESHOLD=45.0
        ;;
esac

echo ""
echo "启动GPS软约束SLAM系统..."

# 启动优化的SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=false \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 10

echo ""
echo "设置GPS软约束参数..."

# 确保禁用GPS平面约束
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/gps/enable_soft_constraint true
rosparam set /state_estimation_node/gps/soft_constraint_weight 0.1

# 设置GPS软约束检测器参数
rosparam set /gps_soft_constraint_loop_detector/good_gps_threshold $GOOD_GPS_THRESHOLD
rosparam set /gps_soft_constraint_loop_detector/poor_gps_threshold $POOR_GPS_THRESHOLD
rosparam set /gps_soft_constraint_loop_detector/trajectory_similarity_threshold $TRAJECTORY_THRESHOLD
rosparam set /gps_soft_constraint_loop_detector/min_time_gap $MIN_TIME_GAP
rosparam set /gps_soft_constraint_loop_detector/heading_similarity_threshold $HEADING_THRESHOLD

# 设置智能约束控制器参数 (更保守)
rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.3
rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.1
rosparam set /intelligent_gps_constraint_controller/velocity_threshold 2.5

# 设置智能检测参数
rosparam set /intelligent_start_end_detector/return_threshold 40.0
rosparam set /intelligent_start_end_detector/departure_threshold 25.0

# 设置强制匹配器参数
rosparam set /force_start_end_loop_matcher/voxel_size 0.03
rosparam set /force_start_end_loop_matcher/outlier_rejection_threshold 0.3
rosparam set /force_start_end_loop_matcher/max_iterations 300

echo "✅ 参数设置完成"

echo ""
echo "=========================================="
echo "🚀 GPS软约束SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统配置:"
echo "  输出目录: $OUTPUT_DIR"
echo "  GPS平面约束: ❌ 已禁用"
echo "  GPS软约束: ✅ 已启用"
echo "  高质量GPS阈值: ${GOOD_GPS_THRESHOLD}米"
echo "  低质量GPS阈值: ${POOR_GPS_THRESHOLD}米"
echo ""
echo "工作原理:"
echo "🎯 GPS软约束不直接修改SLAM位置"
echo "📍 根据GPS质量动态调整检测阈值"
echo "🔄 GPS接近时触发智能回环检测"
echo "💎 使用多算法精细匹配验证回环"
echo ""
echo "监控命令:"
echo "  GPS软约束状态: rostopic echo /gps_soft_loop_status"
echo "  回环置信度:    rostopic echo /loop_confidence_score"
echo "  软约束触发:    rostopic echo /gps_soft_loop_trigger"
echo "  强制匹配:      rostopic echo /force_match_score"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果:"
echo "✅ 避免GPS平面约束导致的点云拼接问题"
echo "✅ GPS质量好时严格阈值，质量差时宽松阈值"
echo "✅ 软约束引导的智能回环检测"
echo "✅ 首尾偏差显著减少"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
