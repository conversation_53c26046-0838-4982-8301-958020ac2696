#!/bin/bash

# 应用点云安全补丁脚本

echo "=========================================="
echo "🛡️  应用点云安全补丁"
echo "=========================================="

# 检查源码文件
MAIN_CPP="src/state_estimation/src/laserMapping.cpp"

if [ ! -f "$MAIN_CPP" ]; then
    echo "❌ 未找到主要源码文件: $MAIN_CPP"
    echo "请确认在正确的工作空间目录中"
    exit 1
fi

echo "找到源码文件: $MAIN_CPP"

echo ""
echo "步骤1: 备份原始文件"
echo "=================="

cp "$MAIN_CPP" "${MAIN_CPP}.backup.$(date +%Y%m%d_%H%M%S)"
echo "✅ 已备份到: ${MAIN_CPP}.backup.$(date +%Y%m%d_%H%M%S)"

echo ""
echo "步骤2: 应用安全补丁"
echo "=================="

# 检查是否已经应用过补丁
if grep -q "POINTCLOUD_SAFETY_PATCH" "$MAIN_CPP"; then
    echo "⚠️  安全补丁已经应用过"
else
    echo "应用点云安全补丁..."
    
    # 在文件开头添加安全检查宏
    sed -i '1i// POINTCLOUD_SAFETY_PATCH - Added safety checks for empty pointclouds' "$MAIN_CPP"
    sed -i '2i#define ENABLE_POINTCLOUD_SAFETY_CHECKS 1' "$MAIN_CPP"
    sed -i '3i' "$MAIN_CPP"
    
    # 查找并修复点云处理函数
    # 这里添加一个通用的安全检查
    cat >> /tmp/safety_patch.cpp << 'EOF'

// 点云安全检查函数
bool isPointCloudValid(const sensor_msgs::PointCloud2ConstPtr& cloud_msg) {
    if (!cloud_msg) {
        ROS_WARN("Received null pointcloud message");
        return false;
    }
    
    if (cloud_msg->width == 0 || cloud_msg->height == 0) {
        ROS_WARN("Received empty pointcloud: width=%d, height=%d", cloud_msg->width, cloud_msg->height);
        return false;
    }
    
    if (cloud_msg->data.empty()) {
        ROS_WARN("Received pointcloud with empty data");
        return false;
    }
    
    int total_points = cloud_msg->width * cloud_msg->height;
    if (total_points < 10) {
        ROS_WARN("Too few points in pointcloud: %d", total_points);
        return false;
    }
    
    return true;
}
EOF
    
    # 在主函数前插入安全检查函数
    sed -i '/int main/i\
// POINTCLOUD_SAFETY_PATCH - Safety check function\
bool isPointCloudValid(const sensor_msgs::PointCloud2ConstPtr& cloud_msg) {\
    if (!cloud_msg) {\
        ROS_WARN("Received null pointcloud message");\
        return false;\
    }\
    if (cloud_msg->width == 0 || cloud_msg->height == 0) {\
        ROS_WARN("Received empty pointcloud: width=%d, height=%d", cloud_msg->width, cloud_msg->height);\
        return false;\
    }\
    if (cloud_msg->data.empty()) {\
        ROS_WARN("Received pointcloud with empty data");\
        return false;\
    }\
    int total_points = cloud_msg->width * cloud_msg->height;\
    if (total_points < 10) {\
        ROS_WARN("Too few points in pointcloud: %d", total_points);\
        return false;\
    }\
    return true;\
}\
' "$MAIN_CPP"
    
    echo "✅ 安全补丁应用完成"
fi

echo ""
echo "步骤3: 查找点云回调函数"
echo "======================"

# 查找可能的点云回调函数
CALLBACK_FUNCTIONS=$(grep -n "PointCloud2" "$MAIN_CPP" | grep -E "(callback|Callback)" | head -5)

if [ -n "$CALLBACK_FUNCTIONS" ]; then
    echo "找到的点云回调函数:"
    echo "$CALLBACK_FUNCTIONS"
else
    echo "未找到明确的点云回调函数，查找其他可能的函数..."
    grep -n -A 2 -B 2 "PointCloud2" "$MAIN_CPP" | head -20
fi

echo ""
echo "步骤4: 创建安全的启动配置"
echo "======================"

# 创建安全的CMakeLists.txt修改
if [ -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "修改CMakeLists.txt添加安全编译选项..."
    
    if ! grep -q "POINTCLOUD_SAFETY" src/state_estimation/CMakeLists.txt; then
        echo "" >> src/state_estimation/CMakeLists.txt
        echo "# POINTCLOUD_SAFETY_PATCH - Add safety compile options" >> src/state_estimation/CMakeLists.txt
        echo "add_definitions(-DENABLE_POINTCLOUD_SAFETY_CHECKS=1)" >> src/state_estimation/CMakeLists.txt
        echo "set(CMAKE_CXX_FLAGS \"\${CMAKE_CXX_FLAGS} -fstack-protector-strong\")" >> src/state_estimation/CMakeLists.txt
        echo "✅ CMakeLists.txt已修改"
    else
        echo "⚠️  CMakeLists.txt已经修改过"
    fi
fi

echo ""
echo "步骤5: 编译测试"
echo "=============="

echo "清理之前的编译..."
rm -rf build devel

echo "重新编译..."
source /opt/ros/noetic/setup.bash
catkin_make -DCMAKE_BUILD_TYPE=Release -j1

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    source devel/setup.bash
    
    echo ""
    echo "步骤6: 创建测试脚本"
    echo "=================="
    
    cat > test_patched_slam.sh << 'EOF'
#!/bin/bash

echo "🧪 测试补丁后的SLAM系统"
echo "======================"

source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 设置安全参数
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 10
rosparam set /state_estimation_node/enable_empty_scan_protection true

echo "启动补丁后的SLAM节点..."
timeout 30 rosrun state_estimation state_estimation_node &
SLAM_PID=$!

sleep 10

if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM节点启动成功且运行稳定"
    echo ""
    echo "现在可以播放bag文件进行测试:"
    echo "  rosbag play your_data.bag"
    echo ""
    echo "按任意键停止测试..."
    read -n 1
    kill $SLAM_PID 2>/dev/null
else
    echo "❌ SLAM节点仍然崩溃"
    echo "需要进一步调试"
fi
EOF
    
    chmod +x test_patched_slam.sh
    echo "✅ 创建了测试脚本: test_patched_slam.sh"
    
else
    echo "❌ 编译失败"
    echo ""
    echo "可能的原因:"
    echo "1. 补丁与现有代码冲突"
    echo "2. 缺少依赖"
    echo "3. 语法错误"
    echo ""
    echo "恢复原始文件:"
    echo "  cp ${MAIN_CPP}.backup.* $MAIN_CPP"
    
    exit 1
fi

echo ""
echo "=========================================="
echo "🛡️  点云安全补丁应用完成"
echo "=========================================="
echo ""
echo "应用的修改:"
echo "  ✅ 添加了点云有效性检查函数"
echo "  ✅ 添加了安全编译选项"
echo "  ✅ 创建了测试脚本"
echo ""
echo "测试步骤:"
echo "1. 运行测试脚本:"
echo "   ./test_patched_slam.sh"
echo ""
echo "2. 如果测试通过，播放bag文件:"
echo "   rosbag play your_data.bag"
echo ""
echo "3. 如果仍然崩溃，查看备份文件:"
echo "   ls ${MAIN_CPP}.backup.*"
echo ""
echo "恢复命令 (如果需要):"
echo "   cp ${MAIN_CPP}.backup.* $MAIN_CPP"
