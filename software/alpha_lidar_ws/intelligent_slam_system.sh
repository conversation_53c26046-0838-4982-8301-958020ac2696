#!/bin/bash

# 智能首尾回环检测SLAM系统启动脚本
# 实现您提出的优化策略

echo "=========================================="
echo "🎯 智能首尾回环检测SLAM系统"
echo "=========================================="
echo ""
echo "核心功能："
echo "✅ 起点离开30米后开始监控"
echo "✅ 实时GPS位置与起点比较"
echo "✅ 质量非0的GPS也参与检测"
echo "✅ 直线距离<50米时强制匹配"
echo "✅ SLAM首尾帧范围全局精细匹配"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/intelligent_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "选择智能检测配置："
echo "1) 标准智能检测 (离开30m, 返回50m)"
echo "2) 敏感智能检测 (离开20m, 返回40m)"
echo "3) 宽松智能检测 (离开40m, 返回60m)"
echo "4) 超宽松智能检测 (离开50m, 返回80m)"
echo "5) 自定义配置"
echo ""

read -p "请选择配置 (1-5): " choice

case $choice in
    1)
        echo "启动标准智能检测配置..."
        DEPARTURE_THRESHOLD=30.0
        RETURN_THRESHOLD=50.0
        ;;
    2)
        echo "启动敏感智能检测配置..."
        DEPARTURE_THRESHOLD=20.0
        RETURN_THRESHOLD=40.0
        ;;
    3)
        echo "启动宽松智能检测配置..."
        DEPARTURE_THRESHOLD=40.0
        RETURN_THRESHOLD=60.0
        ;;
    4)
        echo "启动超宽松智能检测配置..."
        DEPARTURE_THRESHOLD=50.0
        RETURN_THRESHOLD=80.0
        ;;
    5)
        echo "自定义配置："
        read -p "离开起点阈值 (米, 默认30): " DEPARTURE_THRESHOLD
        read -p "返回起点阈值 (米, 默认50): " RETURN_THRESHOLD
        DEPARTURE_THRESHOLD=${DEPARTURE_THRESHOLD:-30.0}
        RETURN_THRESHOLD=${RETURN_THRESHOLD:-50.0}
        ;;
    *)
        echo "使用默认配置..."
        DEPARTURE_THRESHOLD=30.0
        RETURN_THRESHOLD=50.0
        ;;
esac

echo ""
echo "智能检测参数："
echo "  离开起点阈值: ${DEPARTURE_THRESHOLD}米"
echo "  返回起点阈值: ${RETURN_THRESHOLD}米"
echo "  GPS质量阈值: 接受所有质量（包括status=-1）"
echo ""

# 启动智能SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=true \
    enable_intelligent_detection:=true \
    departure_threshold:="$DEPARTURE_THRESHOLD" \
    return_threshold:="$RETURN_THRESHOLD" &

# 等待系统启动
sleep 5

echo ""
echo "=========================================="
echo "🚀 智能SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "📍 实时监控GPS位置与起点距离"
echo "🎯 智能触发首尾回环精细匹配"
echo "💎 完整保持强度值"
echo "⚡ 自适应参数优化"
echo ""
echo "监控命令："
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo "  强制回环信号: rostopic echo /intelligent_force_loop_closure"
echo "  GPS回环状态:  rostopic echo /enhanced_gps_loop_closure_status"
echo "  精细匹配信号: rostopic echo /force_precise_start_end_match"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
