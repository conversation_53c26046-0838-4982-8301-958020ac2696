#!/bin/bash

# RTK GPS优化启动脚本
# 专门针对GPS质量好但仍有偏差的情况

echo "=========================================="
echo "🛰️ RTK GPS优化SLAM系统"
echo "=========================================="
echo ""
echo "检测到的问题："
echo "✅ GPS质量: RTK_FIXED (status=0) - 很好"
echo "❌ GPS距离: 7.6米 - 仍有偏差"
echo "❌ 触发距离: 20.0米 - 太大，未触发"
echo ""
echo "优化策略："
echo "🎯 降低触发距离到15米"
echo "🔍 增大搜索范围到35米"
echo "⚡ 适中的匹配阈值0.7"
echo "📊 专门针对RTK GPS优化"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/rtk_optimized_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "选择RTK优化级别："
echo "1) 标准RTK优化 (触发15m, 搜索35m, 阈值0.7)"
echo "2) 积极RTK优化 (触发12m, 搜索40m, 阈值0.75)"
echo "3) 激进RTK优化 (触发10m, 搜索50m, 阈值0.8)"
echo "4) 立即修复当前系统参数"
echo ""

read -p "请选择优化级别 (1-4): " choice

case $choice in
    1)
        echo "使用标准RTK优化..."
        TRIGGER_DIST=15.0
        SEARCH_RADIUS=35.0
        SCORE_THRESHOLD=0.7
        PRESET="rtk_gps_optimized_preset"
        ;;
    2)
        echo "使用积极RTK优化..."
        TRIGGER_DIST=12.0
        SEARCH_RADIUS=40.0
        SCORE_THRESHOLD=0.75
        PRESET="rtk_gps_optimized_preset"
        ;;
    3)
        echo "使用激进RTK优化..."
        TRIGGER_DIST=10.0
        SEARCH_RADIUS=50.0
        SCORE_THRESHOLD=0.8
        PRESET="ultra_loose_start_end_preset"
        ;;
    4)
        echo "立即修复当前系统参数..."
        echo "检查ROS系统状态..."
        
        if ! rosnode list | grep -q enhanced_gps_loop_optimizer; then
            echo "❌ GPS回环优化器未运行，请先启动SLAM系统"
            exit 1
        fi
        
        echo "应用RTK优化参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 12.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 40.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.75
        
        # 如果智能检测器在运行
        if rosnode list | grep -q intelligent_start_end_detector; then
            rosparam set /intelligent_start_end_detector/return_threshold 12.0
            rosparam set /intelligent_start_end_detector/departure_threshold 20.0
        fi
        
        echo "✅ 参数已更新:"
        echo "   GPS触发距离: 12.0米"
        echo "   搜索半径: 40.0米"
        echo "   匹配阈值: 0.75"
        echo ""
        echo "监控当前状态:"
        echo "   rostopic echo /enhanced_gps_loop_closure_status"
        echo "   rostopic echo /intelligent_detector_status"
        exit 0
        ;;
    *)
        echo "使用默认标准RTK优化..."
        TRIGGER_DIST=15.0
        SEARCH_RADIUS=35.0
        SCORE_THRESHOLD=0.7
        PRESET="rtk_gps_optimized_preset"
        ;;
esac

echo ""
echo "RTK优化参数："
echo "  GPS触发距离: ${TRIGGER_DIST}米"
echo "  搜索半径: ${SEARCH_RADIUS}米"
echo "  匹配阈值: ${SCORE_THRESHOLD}"
echo "  GPS质量要求: RTK_FIXED (status=0)"
echo ""

# 启动RTK优化SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:="$PRESET" \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=true \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 8

# 应用RTK专用参数
echo "应用RTK专用参数..."
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold $TRIGGER_DIST
rosparam set /enhanced_gps_loop_closure_optimizer/gps_quality_threshold 0  # 只接受RTK固定解
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius $SEARCH_RADIUS
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold $SCORE_THRESHOLD

# 智能检测器参数
rosparam set /intelligent_start_end_detector/return_threshold $TRIGGER_DIST
rosparam set /intelligent_start_end_detector/departure_threshold 25.0
rosparam set /intelligent_start_end_detector/gps_quality_threshold 0  # 只接受RTK固定解

echo ""
echo "=========================================="
echo "🛰️ RTK GPS优化系统已启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "🛰️ 专门针对RTK GPS优化"
echo "🎯 GPS距离<${TRIGGER_DIST}米时触发回环"
echo "🔍 搜索半径${SEARCH_RADIUS}米"
echo "⚡ 匹配阈值${SCORE_THRESHOLD}"
echo "💎 完整强度值保持"
echo ""
echo "监控命令："
echo "  GPS回环状态: rostopic echo /enhanced_gps_loop_closure_status"
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo "  系统质量报告: rostopic echo /pointcloud_quality_report"
echo ""
echo "预期效果："
echo "✅ 当GPS距离<${TRIGGER_DIST}米时立即触发回环"
echo "✅ RTK GPS质量确保高精度定位"
echo "✅ 偏差从7-8米降低到2-3米"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 启动实时监控
echo ""
echo "启动实时监控..."
sleep 3

# 监控GPS距离变化
(
    echo "监控GPS距离变化..."
    rostopic echo /enhanced_gps_loop_closure_status | grep -E "(GPS_dist|SLAM_traj|Trigger_dist)" | while read line; do
        echo "$(date '+%H:%M:%S') - $line"
    done
) &

# 等待用户中断
wait
