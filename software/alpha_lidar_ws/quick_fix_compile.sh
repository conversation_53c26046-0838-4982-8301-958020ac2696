#!/bin/bash

# 快速修复编译错误的脚本

echo "=========================================="
echo "🔧 快速修复编译错误"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤2: 安装必要依赖"
sudo apt update
sudo apt install -y \
    ros-noetic-std-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-tf \
    ros-noetic-tf2-ros \
    libpcl-dev \
    pcl-tools

echo ""
echo "步骤3: 清理编译目录"
rm -rf build devel

echo ""
echo "步骤4: 检查代码修复"
echo "检查voxelMapping.cpp..."

# 检查是否包含必要的头文件
if ! grep -q "#include <std_msgs/Bool.h>" src/state_estimation/src/voxelMapping.cpp; then
    echo "添加std_msgs/Bool.h头文件..."
    sed -i '/^#include <std_msgs\/Float64.h>/a #include <std_msgs/Bool.h>' src/state_estimation/src/voxelMapping.cpp
fi

echo "✓ 代码检查完成"

echo ""
echo "步骤5: 编译系统"
echo "使用单线程编译..."

# 首先尝试编译state_estimation包
catkin_make --only-pkg-with-deps state_estimation -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ state_estimation包编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    # 设置权限
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤6: 验证编译结果"
    
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点编译成功"
        SLAM_AVAILABLE=true
    else
        echo "❌ SLAM核心节点编译失败"
        SLAM_AVAILABLE=false
    fi
    
    if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
        echo "✅ 强度保持模块编译成功"
    fi
    
    if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
        echo "✅ 智能检测脚本存在"
    fi
    
    echo ""
    if [ "$SLAM_AVAILABLE" = true ]; then
        echo "🎉 编译成功! 系统已准备就绪"
        echo ""
        echo "可用的启动选项:"
        echo "1. 基础SLAM系统:"
        echo "   roslaunch state_estimation mapping_robosense.launch"
        echo ""
        echo "2. 智能检测系统:"
        echo "   ./start_slam_system_robust.sh"
        echo ""
        echo "3. 手动启动核心组件:"
        echo "   rosrun state_estimation state_estimation_node"
        echo ""
        echo "监控命令:"
        echo "   rostopic echo /aft_mapped_to_init"
        echo "   rostopic echo /cloud_registered"
        echo ""
    else
        echo "⚠️  部分编译成功，但核心节点有问题"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "尝试最小化编译..."
    
    # 尝试禁用有问题的功能
    echo "临时禁用GPS约束功能..."
    
    # 备份原文件
    cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup
    
    # 注释掉GPS约束相关代码
    sed -i 's/^#ifdef USE_GPS_INTEGRATION/#if 0  \/\/ 临时禁用GPS集成/' src/state_estimation/src/voxelMapping.cpp
    
    # 重新编译
    catkin_make --only-pkg-with-deps state_estimation -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 最小化编译成功!"
        echo "GPS集成功能已临时禁用"
        echo ""
        echo "可用功能:"
        echo "✅ 基础SLAM"
        echo "✅ 强度保持"
        echo "❌ GPS集成 (已禁用)"
        echo ""
        echo "启动命令:"
        echo "   roslaunch state_estimation mapping_robosense.launch"
        echo ""
        echo "如需恢复GPS功能:"
        echo "   cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp"
        
        source devel/setup.bash
        chmod +x *.sh
        chmod +x src/state_estimation/scripts/*.py
        
    else
        echo ""
        echo "❌ 最小化编译也失败!"
        echo ""
        echo "恢复原文件..."
        if [ -f "src/state_estimation/src/voxelMapping.cpp.backup" ]; then
            cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp
        fi
        
        echo ""
        echo "建议的解决方案:"
        echo "1. 检查ROS安装:"
        echo "   sudo apt install ros-noetic-desktop-full"
        echo ""
        echo "2. 检查编译器版本:"
        echo "   g++ --version"
        echo ""
        echo "3. 检查具体错误信息并手动修复"
        echo ""
        echo "4. 或者使用预编译的ROS包"
        
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "编译修复完成!"
echo "=========================================="
