#!/bin/bash

# 超宽松首尾回环检测启动脚本
# 专门针对首尾区域有大偏移的情况

echo "=========================================="
echo "超宽松首尾回环检测系统启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "参数配置："
echo "  首尾距离阈值: 50.0米"
echo "  搜索半径: 60.0米"
echo "  匹配阈值: 0.9 (超宽松)"
echo ""

# 启动超宽松配置的SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:=/home/<USER>/slam_share/aLidar/ultra_loose_output \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=true

echo ""
echo "系统已启动，使用超宽松首尾回环检测配置"
echo ""
echo "监控命令："
echo "  rostopic echo /enhanced_gps_loop_closure_status"
echo "  rostopic echo /force_loop_closure"
echo "  rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold"
