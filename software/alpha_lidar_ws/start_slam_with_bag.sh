#!/bin/bash

# SLAM与bag文件同步启动脚本

echo "=========================================="
echo "🎯 SLAM与bag文件同步启动"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 /path/to/your.bag"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/synced_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "📊 分析bag文件"
echo "=============="
rosbag info "$BAG_FILE"

echo ""
echo "🔧 设置SLAM参数"
echo "==============="

# 设置安全的SLAM参数
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 点云处理安全参数
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 100
rosparam set /state_estimation_node/max_points_threshold 200000
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/wait_for_pointcloud true
rosparam set /state_estimation_node/pointcloud_timeout 10.0

# 处理参数
rosparam set /state_estimation_node/voxel_size 0.6
rosparam set /state_estimation_node/downsample_ratio 0.4
rosparam set /state_estimation_node/max_iterations 20
rosparam set /state_estimation_node/transformation_epsilon 1e-5

# 禁用复杂功能避免崩溃
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false

# 调试参数
rosparam set /state_estimation_node/enable_debug_output true
rosparam set /state_estimation_node/log_pointcloud_stats true

echo "✅ SLAM参数设置完成"

echo ""
echo "🚀 同步启动流程"
echo "==============="

echo "步骤1: 预播放bag文件（暂停状态）"
rosbag play "$BAG_FILE" --pause --clock --immediate &
BAG_PID=$!
echo "✅ bag文件已加载 (PID: $BAG_PID)"

echo ""
echo "步骤2: 等待topic就绪"
sleep 5

# 检查topic
echo "检查可用topic..."
TOPICS=$(rostopic list)
echo "$TOPICS"

if echo "$TOPICS" | grep -q "/velodyne_points"; then
    echo "✅ 点云topic已就绪"
else
    echo "❌ 点云topic未找到"
    kill $BAG_PID 2>/dev/null
    exit 1
fi

if echo "$TOPICS" | grep -q "/imu/data"; then
    echo "✅ IMU topic已就绪"
else
    echo "⚠️  IMU topic未找到"
fi

echo ""
echo "步骤3: 启动SLAM节点"
rosrun state_estimation state_estimation_node &
SLAM_PID=$!
echo "✅ SLAM节点已启动 (PID: $SLAM_PID)"

echo ""
echo "步骤4: 等待SLAM初始化"
echo "等待SLAM节点初始化..."
sleep 8

# 检查SLAM是否还在运行
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM节点启动失败或崩溃"
    kill $BAG_PID 2>/dev/null
    exit 1
fi

echo "✅ SLAM节点初始化完成"

echo ""
echo "步骤5: 恢复bag播放"
echo "恢复bag文件播放..."

# 方法1: 尝试使用rosservice
if rosservice list | grep -q "/rosbag/unpause"; then
    echo "使用rosservice恢复播放..."
    rosservice call /rosbag/unpause
    if [ $? -eq 0 ]; then
        echo "✅ bag文件播放已恢复"
    else
        echo "⚠️  rosservice恢复失败，尝试其他方法"
        # 方法2: 发送空格键信号
        echo "发送空格键信号恢复播放..."
        echo " " > /proc/$BAG_PID/fd/0 2>/dev/null || {
            echo "⚠️  无法发送信号，请手动按空格键恢复播放"
            echo "在bag播放窗口按空格键继续..."
        }
    fi
else
    echo "⚠️  rosbag服务不可用，请手动恢复播放"
    echo "在bag播放窗口按空格键继续..."
fi

echo ""
echo "步骤6: 监控系统运行"
echo "等待系统稳定运行..."
sleep 10

# 检查系统状态
if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM系统运行稳定"
    
    # 检查输出topic
    echo ""
    echo "检查输出topic:"
    if rostopic list | grep -q "/aft_mapped_to_init"; then
        echo "✅ 位姿输出正常"
    else
        echo "⚠️  位姿输出未找到"
    fi
    
    if rostopic list | grep -q "/cloud_registered"; then
        echo "✅ 点云输出正常"
    else
        echo "⚠️  点云输出未找到"
    fi
    
    # 检查数据流
    echo ""
    echo "检查数据流频率:"
    echo "点云频率:"
    timeout 10 rostopic hz /velodyne_points 2>/dev/null || echo "无法获取点云频率"
    
    echo ""
    echo "🎉 SLAM系统成功启动并运行!"
    echo ""
    echo "系统状态："
    echo "  SLAM节点: 运行中 (PID: $SLAM_PID)"
    echo "  bag播放: 运行中 (PID: $BAG_PID)"
    echo "  输出目录: $OUTPUT_DIR"
    echo ""
    echo "监控命令："
    echo "  rostopic echo /aft_mapped_to_init"
    echo "  rostopic echo /cloud_registered"
    echo "  rostopic hz /velodyne_points"
    echo ""
    echo "可选功能启用："
    echo "  启用GPS: rosparam set /state_estimation_node/enable_gps true"
    echo "  启用回环: rosparam set /state_estimation_node/enable_loop_closure true"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    # 创建停止函数
    cleanup() {
        echo ""
        echo "正在停止系统..."
        echo "停止SLAM节点..."
        kill $SLAM_PID 2>/dev/null
        echo "停止bag播放..."
        kill $BAG_PID 2>/dev/null
        echo "✅ 系统已停止"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup SIGINT
    
    # 等待用户中断或进程结束
    while ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; do
        sleep 5
    done
    
    # 如果进程意外结束
    if ! ps -p $SLAM_PID > /dev/null; then
        echo ""
        echo "❌ SLAM节点意外停止"
        echo "查看日志: tail ~/.ros/log/latest/state_estimation_node-*.log"
    fi
    
    if ! ps -p $BAG_PID > /dev/null; then
        echo ""
        echo "✅ bag文件播放完成"
    fi
    
    cleanup
    
else
    echo "❌ SLAM系统启动失败"
    echo ""
    echo "可能的原因："
    echo "1. 点云数据格式不兼容"
    echo "2. 内存不足"
    echo "3. 参数配置问题"
    echo ""
    echo "建议的调试步骤："
    echo "1. 查看详细日志:"
    echo "   tail -f ~/.ros/log/latest/state_estimation_node-*.log"
    echo ""
    echo "2. 使用GDB调试:"
    echo "   gdb --args devel/lib/state_estimation/state_estimation_node"
    echo ""
    echo "3. 检查点云数据:"
    echo "   rostopic echo /velodyne_points -n 1"
    
    kill $BAG_PID 2>/dev/null
    exit 1
fi
