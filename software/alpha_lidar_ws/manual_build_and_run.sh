#!/bin/bash
# 手工编译和运行GPS回环检测系统

set -e

echo "=========================================="
echo "手工编译和运行GPS回环检测系统"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

print_step "步骤1: 安装系统依赖"
sudo apt update
sudo apt-get install -y \
    ros-noetic-pcl-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-std-msgs \
    ros-noetic-visualization-msgs \
    ros-noetic-tf2-ros \
    python3-pykdl \
    python3-tf2-ros \
    build-essential \
    cmake \
    python3-pip \
    python3-dev

print_step "步骤2: 配置pip镜像源"
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

print_step "步骤3: 安装Python依赖"
python3 -m pip install --upgrade pip
python3 -m pip install --user rospkg catkin_pkg PyYAML numpy scipy

print_step "步骤4: 设置环境变量"
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
export PYTHONPATH=$PYTHONPATH:~/.local/lib/python3.8/site-packages:/opt/ros/noetic/lib/python3/dist-packages

print_step "步骤5: 验证Python导入"
python3 -c "
import rospy, rospkg, yaml, numpy
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import Bool
print('✅ 所有模块导入成功')
" || {
    print_error "Python模块导入失败"
    exit 1
}

print_step "步骤6: 设置ROS环境"
source /opt/ros/noetic/setup.bash

print_step "步骤7: 构建工作空间"
if [ ! -f "src/CMakeLists.txt" ]; then
    print_error "请在catkin工作空间根目录运行此脚本"
    exit 1
fi

rm -rf build/ devel/
catkin_make -DCMAKE_BUILD_TYPE=Release -j$(nproc) || {
    print_error "构建失败"
    exit 1
}

source devel/setup.bash

print_step "步骤8: 设置脚本权限"
chmod +x src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py

print_step "步骤9: 验证构建结果"
if [ -f "devel/lib/state_estimation/enhanced_gps_loop_closure_optimizer.py" ]; then
    print_status "Python脚本构建成功"
else
    print_error "Python脚本构建失败"
    exit 1
fi

if [ -f "devel/lib/state_estimation/enhanced_slam_loop_closure_integration" ]; then
    print_status "C++可执行文件构建成功"
else
    print_warning "C++可执行文件未找到，但继续..."
fi

print_step "步骤10: 系统就绪"
print_status "手工编译完成！"

echo ""
echo "启动选项："
echo ""
echo "1. 基础启动："
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch"
echo ""
echo "2. 优化配置启动（推荐）："
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \\"
echo "     gps_quality_threshold:=-1 \\"
echo "     loop_closure_distance_threshold:=8.0 \\"
echo "     intermediate_loop_threshold:=10.0 \\"
echo "     min_trajectory_length:=30.0 \\"
echo "     --screen"
echo ""
echo "3. 分步启动（调试用）："
echo "   # 终端1: roslaunch state_estimation mapping_robosense.launch"
echo "   # 终端2: rosrun state_estimation enhanced_gps_loop_closure_optimizer.py"
echo "   # 终端3: rosrun state_estimation enhanced_slam_loop_closure_integration"
echo ""

read -p "是否立即启动系统？(y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "启动GPS回环检测系统..."
    roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \
        gps_quality_threshold:=-1 \
        loop_closure_distance_threshold:=8.0 \
        intermediate_loop_threshold:=10.0 \
        min_trajectory_length:=30.0 \
        --screen
else
    print_status "系统就绪，请手动启动"
fi
