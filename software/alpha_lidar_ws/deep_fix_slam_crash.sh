#!/bin/bash

# SLAM崩溃深度修复脚本

echo "=========================================="
echo "🔍 SLAM崩溃深度诊断和修复"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo ""
echo "步骤1: 系统环境检查"
echo "=================="

# 检查系统资源
echo "内存信息:"
free -h
echo ""
echo "磁盘空间:"
df -h . | head -2
echo ""
echo "CPU信息:"
nproc
echo ""

# 检查核心转储设置
echo "核心转储设置:"
ulimit -c
if [ "$(ulimit -c)" = "0" ]; then
    echo "启用核心转储..."
    ulimit -c unlimited
    echo "核心转储已启用"
fi

echo ""
echo "步骤2: 编译环境深度检查"
echo "======================"

# 检查编译结果
if [ ! -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "❌ SLAM节点未编译"
    exit 1
fi

echo "检查可执行文件..."
ls -la devel/lib/state_estimation/state_estimation_node

echo ""
echo "检查动态库依赖..."
ldd devel/lib/state_estimation/state_estimation_node | head -20

echo ""
echo "检查PCL版本..."
pkg-config --modversion pcl_common-1.10 2>/dev/null || echo "PCL版本检查失败"

echo ""
echo "步骤3: 点云数据深度分析"
echo "======================"

# 检查ROS
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "分析bag文件中的点云数据..."
rosbag info "$BAG_FILE" | grep -A 5 -B 5 "PointCloud2"

echo ""
echo "测试点云数据接收..."
timeout 8 rosbag play "$BAG_FILE" --clock &
TEST_PID=$!
sleep 3

# 详细分析点云数据
if rostopic list | grep -q "/velodyne_points"; then
    echo "✅ 点云topic存在"
    
    echo "获取点云样本..."
    SAMPLE=$(timeout 5 rostopic echo /velodyne_points -n 1 2>/dev/null)
    
    if [ -n "$SAMPLE" ]; then
        echo "✅ 点云数据接收成功"
        
        # 分析点云结构
        WIDTH=$(echo "$SAMPLE" | grep "width:" | awk '{print $2}')
        HEIGHT=$(echo "$SAMPLE" | grep "height:" | awk '{print $2}')
        POINT_STEP=$(echo "$SAMPLE" | grep "point_step:" | awk '{print $2}')
        ROW_STEP=$(echo "$SAMPLE" | grep "row_step:" | awk '{print $2}')
        DATA_SIZE=$(echo "$SAMPLE" | grep -A 1 "data:" | tail -1 | wc -c)
        
        echo "点云结构分析:"
        echo "  宽度: $WIDTH"
        echo "  高度: $HEIGHT"
        echo "  点步长: $POINT_STEP"
        echo "  行步长: $ROW_STEP"
        echo "  数据大小: $DATA_SIZE 字符"
        
        if [ -n "$WIDTH" ] && [ -n "$HEIGHT" ]; then
            TOTAL_POINTS=$((WIDTH * HEIGHT))
            echo "  总点数: $TOTAL_POINTS"
            
            # 检查数据一致性
            if [ -n "$POINT_STEP" ] && [ -n "$ROW_STEP" ]; then
                EXPECTED_ROW_STEP=$((WIDTH * POINT_STEP))
                if [ "$ROW_STEP" -eq "$EXPECTED_ROW_STEP" ]; then
                    echo "  ✅ 数据结构一致"
                else
                    echo "  ❌ 数据结构不一致: 期望行步长 $EXPECTED_ROW_STEP, 实际 $ROW_STEP"
                fi
            fi
            
            # 检查点数是否过大
            if [ $TOTAL_POINTS -gt 200000 ]; then
                echo "  ⚠️  点云密度过高: $TOTAL_POINTS 点，可能导致内存问题"
            elif [ $TOTAL_POINTS -lt 100 ]; then
                echo "  ⚠️  点云密度过低: $TOTAL_POINTS 点"
            else
                echo "  ✅ 点云密度正常: $TOTAL_POINTS 点"
            fi
        fi
        
        # 检查字段信息
        echo ""
        echo "点云字段信息:"
        echo "$SAMPLE" | grep -A 20 "fields:" | grep -E "(name:|offset:|datatype:|count:)" | head -20
        
    else
        echo "❌ 无法接收点云数据"
    fi
else
    echo "❌ 点云topic不存在"
fi

kill $TEST_PID 2>/dev/null
sleep 2

echo ""
echo "步骤4: 创建安全版本的SLAM节点"
echo "=========================="

# 创建修复版本的参数配置
cat > slam_safe_params.yaml << 'EOF'
# SLAM安全参数配置
state_estimation_node:
  # 基本topic
  lidar_topic: "/velodyne_points"
  imu_topic: "/imu/data"
  gps_topic: "/rtk/gnss"
  
  # 点云处理安全参数
  enable_point_cloud_validation: true
  min_points_threshold: 1000
  max_points_threshold: 100000
  skip_empty_scans: true
  wait_for_pointcloud: true
  pointcloud_timeout: 20.0
  enable_point_cloud_bounds_check: true
  
  # 内存安全参数
  max_memory_usage_mb: 3072
  enable_memory_monitoring: true
  memory_check_interval: 5.0
  
  # 处理参数 - 极度保守
  voxel_size: 1.5
  downsample_ratio: 0.15
  max_iterations: 8
  transformation_epsilon: 1e-3
  euclidean_fitness_epsilon: 1e-3
  
  # 禁用所有复杂功能
  enable_gps: false
  enable_loop_closure: false
  enable_icp_loop_closure: false
  enable_intensity_processing: false
  enable_feature_extraction: false
  enable_plane_constraint: false
  
  # 数据验证参数
  enable_nan_check: true
  enable_inf_check: true
  enable_bounds_check: true
  max_coordinate_value: 1000.0
  
  # 调试参数
  enable_debug_output: true
  log_pointcloud_stats: true
  log_memory_usage: true
  log_processing_time: true
  
  # 错误处理参数
  enable_error_recovery: true
  max_consecutive_errors: 5
  error_recovery_delay: 2.0
EOF

echo "✅ 安全参数配置已创建"

echo ""
echo "步骤5: 创建内存安全启动脚本"
echo "=========================="

cat > start_slam_memory_safe.sh << 'EOF'
#!/bin/bash

# 内存安全SLAM启动脚本

echo "🛡️  内存安全SLAM启动"
echo "=================="

BAG_FILE="$1"
if [ -z "$BAG_FILE" ] || [ ! -f "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 设置内存限制
echo "设置内存限制..."
ulimit -v 4194304  # 4GB虚拟内存限制
ulimit -m 3145728  # 3GB物理内存限制
ulimit -c unlimited # 启用核心转储

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/memory_safe_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "🔧 加载安全参数"
echo "==============="

# 加载参数文件
if [ -f "slam_safe_params.yaml" ]; then
    rosparam load slam_safe_params.yaml
    echo "✅ 安全参数已加载"
else
    echo "⚠️  参数文件不存在，使用默认安全参数"
    
    # 手动设置关键安全参数
    rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
    rosparam set /state_estimation_node/skip_empty_scans true
    rosparam set /state_estimation_node/min_points_threshold 1000
    rosparam set /state_estimation_node/max_points_threshold 100000
    rosparam set /state_estimation_node/voxel_size 1.5
    rosparam set /state_estimation_node/downsample_ratio 0.15
    rosparam set /state_estimation_node/max_iterations 8
    rosparam set /state_estimation_node/enable_gps false
    rosparam set /state_estimation_node/enable_loop_closure false
    rosparam set /state_estimation_node/enable_intensity_processing false
fi

echo ""
echo "🎬 启动流程"
echo "==========="

echo "步骤1: 预加载bag文件"
rosbag play "$BAG_FILE" --clock --pause --queue=1 &
BAG_PID=$!
sleep 5

echo "步骤2: 验证数据可用性"
if ! rostopic list | grep -q "/velodyne_points"; then
    echo "❌ 点云topic不可用"
    kill $BAG_PID 2>/dev/null
    exit 1
fi

echo "步骤3: 启动SLAM节点（内存监控模式）"
# 使用timeout和内存监控启动SLAM
timeout 300 rosrun state_estimation state_estimation_node &
SLAM_PID=$!

# 启动内存监控
(
    while ps -p $SLAM_PID > /dev/null; do
        MEMORY_MB=$(ps -p $SLAM_PID -o rss= 2>/dev/null | awk '{print int($1/1024)}')
        if [ -n "$MEMORY_MB" ]; then
            echo "内存使用: ${MEMORY_MB}MB"
            if [ $MEMORY_MB -gt 3072 ]; then
                echo "⚠️  内存使用过高，终止进程"
                kill $SLAM_PID 2>/dev/null
                break
            fi
        fi
        sleep 5
    done
) &
MONITOR_PID=$!

sleep 10

echo "步骤4: 检查SLAM状态"
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM节点启动失败"
    kill $BAG_PID $MONITOR_PID 2>/dev/null
    
    # 检查核心转储
    if ls core.* 2>/dev/null; then
        echo "发现核心转储文件:"
        ls -la core.*
        echo "使用GDB分析: gdb devel/lib/state_estimation/state_estimation_node core.*"
    fi
    
    exit 1
fi

echo "✅ SLAM节点运行中"

echo "步骤5: 开始数据播放（极慢速度）"
kill $BAG_PID 2>/dev/null
sleep 2

rosbag play "$BAG_FILE" --clock --rate=0.1 &
BAG_PID=$!

echo "✅ 数据播放开始（0.1倍速）"

echo ""
echo "🔍 系统监控"
echo "==========="

sleep 20

if ps -p $SLAM_PID > /dev/null; then
    echo "✅ 系统运行稳定"
    
    # 检查输出
    if rostopic list | grep -q "/aft_mapped_to_init"; then
        echo "✅ SLAM输出正常"
    fi
    
    echo ""
    echo "🎉 内存安全模式启动成功!"
    echo ""
    echo "监控信息:"
    echo "  SLAM节点: PID $SLAM_PID"
    echo "  内存监控: PID $MONITOR_PID"
    echo "  播放速度: 0.1倍速"
    echo ""
    echo "可以逐步提高播放速度:"
    echo "  kill $BAG_PID"
    echo "  rosbag play $BAG_FILE --clock --rate=0.2 &"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    trap 'kill $SLAM_PID $BAG_PID $MONITOR_PID 2>/dev/null; echo "系统已停止"; exit 0' SIGINT
    
    while ps -p $SLAM_PID > /dev/null; do
        sleep 10
        echo "系统运行中... $(date '+%H:%M:%S')"
    done
    
    echo "SLAM节点已停止"
    kill $BAG_PID $MONITOR_PID 2>/dev/null
    
else
    echo "❌ SLAM节点崩溃"
    kill $BAG_PID $MONITOR_PID 2>/dev/null
    exit 1
fi
EOF

chmod +x start_slam_memory_safe.sh

echo "✅ 内存安全启动脚本已创建"

echo ""
echo "步骤6: 创建GDB调试脚本"
echo "===================="

cat > debug_slam_crash.sh << 'EOF'
#!/bin/bash

# SLAM崩溃GDB调试脚本

echo "🐛 SLAM崩溃GDB调试"
echo "=================="

BAG_FILE="$1"
if [ -z "$BAG_FILE" ] || [ ! -f "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查GDB
if ! command -v gdb &> /dev/null; then
    echo "安装GDB..."
    sudo apt install -y gdb
fi

# 启用核心转储
ulimit -c unlimited

echo ""
echo "准备调试环境..."

# 启动bag播放
rosbag play "$BAG_FILE" --clock --pause &
BAG_PID=$!
sleep 3

# 设置安全参数
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/voxel_size 2.0
rosparam set /state_estimation_node/max_iterations 5

echo ""
echo "启动GDB调试..."
echo "GDB命令提示:"
echo "  run                    - 运行程序"
echo "  bt                     - 显示调用栈"
echo "  bt full                - 显示详细调用栈"
echo "  info registers         - 显示寄存器状态"
echo "  thread apply all bt    - 显示所有线程调用栈"
echo "  continue               - 继续执行"
echo "  quit                   - 退出GDB"
echo ""

# 创建GDB命令文件
cat > /tmp/gdb_commands.gdb << 'EOFGDB'
set environment ROS_MASTER_URI=http://localhost:11311
set environment ROS_HOSTNAME=localhost
set logging file /tmp/slam_crash_debug.log
set logging on
run
bt full
info registers
thread apply all bt
quit
EOFGDB

echo "选择调试模式:"
echo "1) 交互式GDB调试"
echo "2) 自动GDB调试（生成日志）"
echo ""

read -p "请选择 (1-2): " debug_choice

case $debug_choice in
    1)
        echo "启动交互式GDB..."
        gdb --args devel/lib/state_estimation/state_estimation_node
        ;;
    2)
        echo "启动自动GDB调试..."
        gdb -batch -x /tmp/gdb_commands.gdb --args devel/lib/state_estimation/state_estimation_node
        
        echo ""
        echo "调试完成，查看日志:"
        if [ -f "/tmp/slam_crash_debug.log" ]; then
            echo "===================="
            cat /tmp/slam_crash_debug.log
            echo "===================="
        fi
        ;;
    *)
        echo "无效选择"
        ;;
esac

kill $BAG_PID 2>/dev/null
EOF

chmod +x debug_slam_crash.sh

echo "✅ GDB调试脚本已创建"

echo ""
echo "=========================================="
echo "深度修复脚本创建完成"
echo "=========================================="
echo ""
echo "🎯 使用方法:"
echo ""
echo "1. 内存安全启动（推荐）:"
echo "   ./start_slam_memory_safe.sh $BAG_FILE"
echo ""
echo "2. GDB调试崩溃原因:"
echo "   ./debug_slam_crash.sh $BAG_FILE"
echo ""
echo "3. 如果发现核心转储文件:"
echo "   gdb devel/lib/state_estimation/state_estimation_node core.*"
echo ""
echo "4. 检查系统资源:"
echo "   free -h"
echo "   df -h"
