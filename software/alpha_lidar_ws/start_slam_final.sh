#!/bin/bash

# 最终稳定的SLAM启动脚本

echo "=========================================="
echo "🎯 最终稳定SLAM启动脚本"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查编译结果
if [ ! -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "❌ SLAM节点未编译，请先运行编译脚本"
    echo "运行: ./complete_rebuild.sh"
    exit 1
fi

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/final_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "📊 分析bag文件"
echo "=============="
echo "文件: $BAG_FILE"
rosbag info "$BAG_FILE" | head -15

echo ""
echo "🔧 设置最优参数"
echo "==============="

# 清除可能存在的旧参数
rosparam delete /state_estimation_node 2>/dev/null || true

# 设置核心topic
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 点云处理安全参数
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 1000
rosparam set /state_estimation_node/max_points_threshold 150000
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/wait_for_pointcloud true
rosparam set /state_estimation_node/pointcloud_timeout 15.0

# 处理参数 - 保守设置
rosparam set /state_estimation_node/voxel_size 1.0
rosparam set /state_estimation_node/downsample_ratio 0.2
rosparam set /state_estimation_node/max_iterations 10
rosparam set /state_estimation_node/transformation_epsilon 1e-3
rosparam set /state_estimation_node/euclidean_fitness_epsilon 1e-3

# 禁用复杂功能
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_icp_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false
rosparam set /state_estimation_node/enable_feature_extraction false

# 内存和性能参数
rosparam set /state_estimation_node/max_memory_usage_mb 4096
rosparam set /state_estimation_node/enable_memory_monitoring true
rosparam set /state_estimation_node/enable_performance_monitoring true

# 调试参数
rosparam set /state_estimation_node/enable_debug_output true
rosparam set /state_estimation_node/log_pointcloud_stats true
rosparam set /state_estimation_node/log_processing_time true

echo "✅ 参数设置完成"

echo ""
echo "🚀 启动流程"
echo "==========="

echo "步骤1: 预加载bag文件"
echo "启动bag播放器（暂停状态）..."
rosbag play "$BAG_FILE" --clock --pause --queue=1 &
BAG_PID=$!

echo "等待topic注册..."
sleep 8

# 验证topic
echo "验证topic可用性..."
TOPICS=$(rostopic list)

if echo "$TOPICS" | grep -q "/velodyne_points"; then
    echo "✅ 点云topic: /velodyne_points"
else
    echo "❌ 点云topic不存在"
    kill $BAG_PID 2>/dev/null
    exit 1
fi

if echo "$TOPICS" | grep -q "/imu/data"; then
    echo "✅ IMU topic: /imu/data"
else
    echo "⚠️  IMU topic不存在"
fi

if echo "$TOPICS" | grep -q "/rtk/gnss"; then
    echo "✅ GPS topic: /rtk/gnss"
else
    echo "⚠️  GPS topic不存在"
fi

echo ""
echo "步骤2: 启动SLAM节点"
echo "启动SLAM节点..."
rosrun state_estimation state_estimation_node &
SLAM_PID=$!

echo "等待SLAM节点初始化..."
sleep 10

# 检查SLAM节点状态
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM节点启动失败或崩溃"
    echo ""
    echo "查看错误日志:"
    echo "  tail -20 ~/.ros/log/latest/state_estimation_node-*.log"
    echo ""
    echo "可能的解决方案:"
    echo "1. 重新编译: ./complete_rebuild.sh"
    echo "2. 使用调试模式: ./start_slam_debug.sh $BAG_FILE"
    echo "3. 检查依赖: ldd devel/lib/state_estimation/state_estimation_node"
    
    kill $BAG_PID 2>/dev/null
    exit 1
fi

echo "✅ SLAM节点启动成功 (PID: $SLAM_PID)"

echo ""
echo "步骤3: 开始数据播放"
echo "停止暂停的播放器..."
kill $BAG_PID 2>/dev/null
sleep 3

echo "以慢速开始播放数据..."
rosbag play "$BAG_FILE" --clock --rate=0.3 &
BAG_PID=$!

echo "✅ 数据播放已开始（0.3倍速）"

echo ""
echo "步骤4: 系统监控"
echo "等待系统稳定..."
sleep 15

# 检查系统状态
if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM节点运行稳定"
    
    # 检查数据流
    echo "检查数据流..."
    POINTCLOUD_HZ=$(timeout 8 rostopic hz /velodyne_points 2>/dev/null | grep "average rate" | awk '{print $3}')
    if [ -n "$POINTCLOUD_HZ" ]; then
        echo "✅ 点云数据流: ${POINTCLOUD_HZ} Hz"
    else
        echo "⚠️  点云数据流异常"
    fi
    
    # 检查SLAM输出
    if rostopic list | grep -q "/aft_mapped_to_init"; then
        echo "✅ SLAM位姿输出正常"
        
        # 检查是否有位姿数据
        POSE_DATA=$(timeout 5 rostopic echo /aft_mapped_to_init -n 1 2>/dev/null)
        if [ -n "$POSE_DATA" ]; then
            echo "✅ SLAM位姿数据正常"
        else
            echo "⚠️  SLAM位姿数据异常"
        fi
    else
        echo "⚠️  SLAM位姿输出异常"
    fi
    
    # 检查点云输出
    if rostopic list | grep -q "/cloud_registered"; then
        echo "✅ 点云输出正常"
    else
        echo "⚠️  点云输出异常"
    fi
    
    echo ""
    echo "🎉 系统启动成功并运行稳定!"
    echo ""
    echo "系统信息："
    echo "  SLAM节点: 运行中 (PID: $SLAM_PID)"
    echo "  数据播放: 运行中 (PID: $BAG_PID, 0.3倍速)"
    echo "  输出目录: $OUTPUT_DIR"
    echo ""
    echo "监控命令："
    echo "  rostopic echo /aft_mapped_to_init"
    echo "  rostopic echo /cloud_registered"
    echo "  rostopic hz /velodyne_points"
    echo "  rostopic hz /aft_mapped_to_init"
    echo ""
    echo "调节播放速度："
    echo "  # 停止当前播放"
    echo "  kill $BAG_PID"
    echo "  # 以正常速度播放"
    echo "  rosbag play $BAG_FILE --clock --rate=1.0 &"
    echo ""
    echo "可选功能启用（系统稳定后）："
    echo "  rosparam set /state_estimation_node/enable_gps true"
    echo "  rosparam set /state_estimation_node/enable_loop_closure true"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    # 创建停止函数
    cleanup() {
        echo ""
        echo "正在停止系统..."
        echo "停止SLAM节点..."
        kill $SLAM_PID 2>/dev/null
        echo "停止数据播放..."
        kill $BAG_PID 2>/dev/null
        echo "✅ 系统已完全停止"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup SIGINT
    
    # 监控循环
    while ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; do
        sleep 10
        echo "系统运行正常 - $(date '+%H:%M:%S')"
        
        # 定期检查内存使用
        MEMORY_USAGE=$(ps -p $SLAM_PID -o %mem --no-headers 2>/dev/null | tr -d ' ')
        if [ -n "$MEMORY_USAGE" ]; then
            echo "内存使用: ${MEMORY_USAGE}%"
            
            # 如果内存使用过高，发出警告
            if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
                echo "⚠️  内存使用过高，建议重启系统"
            fi
        fi
    done
    
    # 检查进程结束原因
    if ! ps -p $SLAM_PID > /dev/null; then
        echo ""
        echo "❌ SLAM节点意外停止"
        echo "查看崩溃日志: tail -50 ~/.ros/log/latest/state_estimation_node-*.log"
    fi
    
    if ! ps -p $BAG_PID > /dev/null; then
        echo ""
        echo "✅ 数据播放完成"
    fi
    
    cleanup
    
else
    echo "❌ SLAM节点崩溃"
    echo ""
    echo "崩溃分析："
    echo "1. 查看详细日志:"
    echo "   tail -50 ~/.ros/log/latest/state_estimation_node-*.log"
    echo ""
    echo "2. 检查核心转储:"
    echo "   ls -la /var/crash/ 2>/dev/null || echo '无核心转储文件'"
    echo ""
    echo "3. 使用调试模式:"
    echo "   ./start_slam_debug.sh $BAG_FILE"
    echo ""
    echo "4. 检查系统资源:"
    echo "   free -h"
    echo "   df -h"
    
    kill $BAG_PID 2>/dev/null
    exit 1
fi
