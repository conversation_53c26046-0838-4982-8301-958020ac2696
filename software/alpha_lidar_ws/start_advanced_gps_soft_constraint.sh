#!/bin/bash

# 启动深度优化的GPS软约束回环检测系统

echo "=========================================="
echo "🎯 深度优化GPS软约束回环检测系统"
echo "=========================================="
echo ""
echo "系统特性："
echo "✅ GPS质量感知的智能回环检测"
echo "✅ 多阶段候选筛选和验证"
echo "✅ 自适应置信度阈值调节"
echo "✅ 多算法点云匹配 (ICP+NDT+GICP)"
echo "✅ 综合质量评估和决策"
echo "❌ 不使用GPS平面约束，避免点云拼接问题"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/advanced_gps_soft_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "配置GPS质量处理策略："
echo "1) 保守策略 (高质量GPS优先，严格阈值)"
echo "2) 平衡策略 (质量自适应，推荐)"
echo "3) 激进策略 (包容低质量GPS，宽松阈值)"
echo ""

read -p "请选择策略 (1-3): " strategy_choice

case $strategy_choice in
    1)
        GPS_PROXIMITY_THRESHOLD=12.0
        SLAM_THRESHOLD=8.0
        MIN_CONFIDENCE=0.8
        echo "✅ 使用保守策略"
        ;;
    2)
        GPS_PROXIMITY_THRESHOLD=18.0
        SLAM_THRESHOLD=12.0
        MIN_CONFIDENCE=0.7
        echo "✅ 使用平衡策略"
        ;;
    3)
        GPS_PROXIMITY_THRESHOLD=25.0
        SLAM_THRESHOLD=15.0
        MIN_CONFIDENCE=0.6
        echo "✅ 使用激进策略"
        ;;
    *)
        GPS_PROXIMITY_THRESHOLD=18.0
        SLAM_THRESHOLD=12.0
        MIN_CONFIDENCE=0.7
        echo "✅ 使用默认平衡策略"
        ;;
esac

echo ""
echo "启动系统组件..."

# 启动核心SLAM节点（禁用GPS平面约束）
echo "启动SLAM核心节点 (禁用GPS平面约束)..."
rosrun state_estimation state_estimation_node &
sleep 3

# 设置禁用GPS平面约束
rosparam set /state_estimation_node/gps/enable_plane_constraint false
echo "✅ GPS平面约束已禁用"

# 启动深度优化的GPS引导回环检测器
echo "启动深度优化GPS引导回环检测器..."
rosrun state_estimation advanced_gps_guided_loop_detector.py \
    _base_gps_threshold:=$GPS_PROXIMITY_THRESHOLD \
    _base_slam_threshold:=$SLAM_THRESHOLD \
    _min_time_gap:=30.0 \
    _max_time_gap:=300.0 \
    _smoothing_window:=5 \
    _velocity_threshold:=2.0 \
    _heading_threshold:=45.0 &
sleep 3
echo "✅ GPS引导检测器已启动"

# 启动自适应回环触发器
echo "启动自适应回环触发器..."
rosrun state_estimation adaptive_loop_trigger.py &
sleep 2
echo "✅ 自适应触发器已启动"

# 启动高级回环执行器
echo "启动高级回环执行器..."
rosrun state_estimation advanced_loop_executor.py &
sleep 2
echo "✅ 回环执行器已启动"

# 启动强度保持模块
if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
    echo "启动强度保持模块..."
    rosrun state_estimation intensity_preserving_pcd_saver \
        _save_directory:="$OUTPUT_DIR" \
        _save_interval:=10.0 &
    sleep 2
    echo "✅ 强度保持模块已启动"
fi

# 启动智能检测器（用于首尾检测）
if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
    echo "启动智能首尾检测器..."
    rosrun state_estimation intelligent_start_end_detector.py \
        _gps_topic:=/rtk/gnss \
        _departure_threshold:=30.0 \
        _return_threshold:=40.0 \
        _min_trajectory_points:=100 &
    sleep 2
    echo "✅ 智能检测器已启动"
fi

echo ""
echo "等待系统稳定..."
sleep 5

echo ""
echo "=========================================="
echo "🚀 深度优化GPS软约束系统已启动"
echo "=========================================="
echo ""
echo "系统配置："
echo "  策略模式: $([ $strategy_choice -eq 1 ] && echo '保守' || [ $strategy_choice -eq 3 ] && echo '激进' || echo '平衡')"
echo "  GPS接近阈值: ${GPS_PROXIMITY_THRESHOLD}米"
echo "  SLAM匹配阈值: ${SLAM_THRESHOLD}米"
echo "  最小置信度: ${MIN_CONFIDENCE}"
echo "  GPS平面约束: 禁用 ✅"
echo "  输出目录: $OUTPUT_DIR"
echo ""
echo "工作原理："
echo "📍 1. GPS质量实时评估和分级"
echo "🔍 2. 多阶段候选位置筛选"
echo "🧠 3. 智能置信度计算和验证"
echo "⚖️  4. 自适应触发决策"
echo "🔧 5. 多算法点云匹配执行"
echo "📊 6. 综合质量评估和反馈"
echo ""
echo "监控命令："
echo "  GPS质量分析:     rostopic echo /gps_quality_analysis"
echo "  回环候选检测:     rostopic echo /advanced_gps_loop_trigger"
echo "  触发决策过程:     rostopic echo /loop_trigger_decision"
echo "  最终触发信号:     rostopic echo /final_loop_closure_trigger"
echo "  执行结果:        rostopic echo /loop_execution_result"
echo "  匹配质量分数:     rostopic echo /match_quality_score"
echo "  系统综合状态:     rostopic echo /advanced_gps_loop_status"
echo ""
echo "实时参数调节："
echo "  # 调节GPS接近阈值"
echo "  rosparam set /advanced_gps_guided_loop_detector/base_gps_threshold 20.0"
echo ""
echo "  # 调节SLAM匹配阈值"
echo "  rosparam set /advanced_gps_guided_loop_detector/base_slam_threshold 10.0"
echo ""
echo "  # 调节置信度要求"
echo "  rosparam set /adaptive_loop_trigger/min_confidence 0.75"
echo ""
echo "质量等级说明："
echo "  🟢 Excellent: GPS状态≥2, 权重=1.0, 标准阈值"
echo "  🟡 Good:      GPS状态≥1, 权重=0.8, 阈值×1.2"
echo "  🟠 Fair:      GPS状态≥0, 权重=0.6, 阈值×1.5"
echo "  🔴 Poor:      GPS状态<0,  权重=0.3, 阈值×2.0"
echo ""
echo "预期效果："
echo "✅ 根据GPS质量动态调整检测策略"
echo "✅ 避免GPS平面约束导致的点云拼接问题"
echo "✅ 多算法确保最佳匹配结果"
echo "✅ 智能过滤低质量回环候选"
echo "✅ 首尾偏差显著减少 (预期<5米)"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "按 Ctrl+C 停止系统"

# 启动实时监控
echo ""
echo "启动实时监控..."
sleep 2

# 监控GPS质量变化
(
    echo "监控GPS质量变化..."
    rostopic echo /gps_quality_analysis | while read line; do
        if [[ $line == *"average_weight"* ]]; then
            weight=$(echo $line | grep -o '[0-9.]*')
            if (( $(echo "$weight > 0.8" | bc -l) )); then
                echo "$(date '+%H:%M:%S') - 🟢 GPS质量优秀: $weight"
            elif (( $(echo "$weight > 0.6" | bc -l) )); then
                echo "$(date '+%H:%M:%S') - 🟡 GPS质量良好: $weight"
            elif (( $(echo "$weight > 0.4" | bc -l) )); then
                echo "$(date '+%H:%M:%S') - 🟠 GPS质量一般: $weight"
            else
                echo "$(date '+%H:%M:%S') - 🔴 GPS质量较差: $weight"
            fi
        fi
    done
) &

# 监控回环触发
(
    echo "监控回环触发事件..."
    rostopic echo /final_loop_closure_trigger | while read line; do
        if [[ $line == *"enhanced_confidence"* ]]; then
            confidence=$(echo $line | grep -o '[0-9.]*' | head -1)
            echo "$(date '+%H:%M:%S') - 🎯 回环触发! 置信度: $confidence"
        fi
    done
) &

# 等待用户中断
wait
