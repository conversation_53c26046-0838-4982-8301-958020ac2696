#!/bin/bash

# GPS软约束实时监控脚本
# 显示GPS软约束的实时工作状态和效果

echo "=========================================="
echo "🔍 GPS软约束实时监控系统"
echo "=========================================="
echo ""
echo "监控内容："
echo "✅ GPS参考增强触发事件"
echo "✅ SLAM匹配增强参数"
echo "✅ GPS质量变化"
echo "✅ 回环检测置信度"
echo "✅ 强制匹配结果"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

# 创建日志目录
LOG_DIR="/tmp/gps_soft_constraint_monitor_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"
echo "日志目录: $LOG_DIR"

echo ""
echo "选择监控模式："
echo "1) 实时终端显示 (推荐)"
echo "2) 保存到日志文件"
echo "3) 终端显示 + 日志文件"
echo "4) 简化监控模式"
echo ""

read -p "请选择模式 (1-4): " monitor_mode

case $monitor_mode in
    1)
        echo ""
        echo "🔍 启动实时终端监控..."
        echo "按 Ctrl+C 停止监控"
        echo ""
        
        # 实时监控多个topic
        {
            echo "=== GPS参考增强触发监控 ==="
            rostopic echo /slam_enhancement_trigger | while read line; do
                if [[ "$line" == *"gps_reference_slam_enhancement"* ]]; then
                    echo "🎯 [$(date '+%H:%M:%S')] GPS参考增强触发!"
                elif [[ "$line" == *"enhancement_level"* ]]; then
                    level=$(echo "$line" | grep -o '"[^"]*"' | tr -d '"')
                    echo "   增强级别: $level"
                elif [[ "$line" == *"gps_distance"* ]]; then
                    dist=$(echo "$line" | grep -o '[0-9.]*')
                    echo "   GPS距离: ${dist}米"
                elif [[ "$line" == *"enhancement_score"* ]]; then
                    score=$(echo "$line" | grep -o '[0-9.]*')
                    echo "   增强评分: $score"
                    echo "   ----------------------------------------"
                fi
            done
        } &
        
        {
            echo "=== GPS质量监控 ==="
            rostopic echo /gps_reference_slam_status | while read line; do
                if [[ "$line" == *"average_gps_quality"* ]]; then
                    quality=$(echo "$line" | grep -o '[0-9.]*')
                    echo "📡 [$(date '+%H:%M:%S')] 平均GPS质量: $quality"
                elif [[ "$line" == *"total_enhancements"* ]]; then
                    count=$(echo "$line" | grep -o '[0-9]*')
                    echo "🔢 总增强次数: $count"
                fi
            done
        } &
        
        {
            echo "=== 回环置信度监控 ==="
            rostopic echo /loop_confidence_score | while read line; do
                if [[ "$line" == *"data:"* ]]; then
                    confidence=$(echo "$line" | grep -o '[0-9.]*')
                    if (( $(echo "$confidence > 0.7" | bc -l) )); then
                        echo "🔥 [$(date '+%H:%M:%S')] 高置信度回环: $confidence"
                    elif (( $(echo "$confidence > 0.5" | bc -l) )); then
                        echo "⚙️  [$(date '+%H:%M:%S')] 中等置信度回环: $confidence"
                    else
                        echo "📊 [$(date '+%H:%M:%S')] 低置信度回环: $confidence"
                    fi
                fi
            done
        } &
        
        wait
        ;;
        
    2)
        echo ""
        echo "📝 启动日志文件监控..."
        echo "日志保存到: $LOG_DIR"
        
        # 保存到日志文件
        rostopic echo /slam_enhancement_trigger > "$LOG_DIR/enhancement_trigger.log" &
        rostopic echo /gps_reference_slam_status > "$LOG_DIR/gps_status.log" &
        rostopic echo /loop_confidence_score > "$LOG_DIR/confidence.log" &
        rostopic echo /enhanced_slam_parameters > "$LOG_DIR/parameters.log" &
        
        echo "✅ 日志记录已启动，按 Ctrl+C 停止"
        wait
        ;;
        
    3)
        echo ""
        echo "🔍📝 启动终端显示 + 日志文件监控..."
        
        # 终端显示
        {
            rostopic echo /slam_enhancement_trigger | while read line; do
                echo "$line" | tee -a "$LOG_DIR/enhancement_trigger.log"
                if [[ "$line" == *"gps_reference_slam_enhancement"* ]]; then
                    echo "🎯 [$(date '+%H:%M:%S')] GPS参考增强触发!" | tee -a "$LOG_DIR/summary.log"
                fi
            done
        } &
        
        {
            rostopic echo /gps_reference_slam_status | while read line; do
                echo "$line" >> "$LOG_DIR/gps_status.log"
                if [[ "$line" == *"total_enhancements"* ]]; then
                    count=$(echo "$line" | grep -o '[0-9]*')
                    echo "🔢 [$(date '+%H:%M:%S')] 总增强次数: $count" | tee -a "$LOG_DIR/summary.log"
                fi
            done
        } &
        
        wait
        ;;
        
    4)
        echo ""
        echo "📊 启动简化监控模式..."
        echo ""
        
        # 简化监控
        while true; do
            echo "=== GPS软约束状态 [$(date '+%H:%M:%S')] ==="
            
            # 检查节点状态
            if rosnode list | grep -q "gps_reference_enhanced_slam"; then
                echo "✅ GPS参考增强器: 运行中"
            else
                echo "❌ GPS参考增强器: 未运行"
            fi
            
            if rosnode list | grep -q "gps_soft_constraint_loop_detector"; then
                echo "✅ GPS软约束检测器: 运行中"
            else
                echo "❌ GPS软约束检测器: 未运行"
            fi
            
            # 检查topic活动
            if rostopic list | grep -q "/slam_enhancement_trigger"; then
                echo "📡 增强触发topic: 活跃"
            else
                echo "📡 增强触发topic: 无活动"
            fi
            
            if rostopic list | grep -q "/gps_reference_guidance"; then
                echo "🎯 参考引导topic: 活跃"
            else
                echo "🎯 参考引导topic: 无活动"
            fi
            
            # 检查参数
            gps_radius=$(rosparam get /gps_reference_enhanced_slam/gps_reference_radius 2>/dev/null || echo "未设置")
            slam_radius=$(rosparam get /gps_reference_enhanced_slam/slam_enhancement_radius 2>/dev/null || echo "未设置")
            
            echo "⚙️  GPS参考半径: ${gps_radius}米"
            echo "⚙️  SLAM增强半径: ${slam_radius}米"
            
            echo "----------------------------------------"
            sleep 10
        done
        ;;
        
    *)
        echo "无效选择，使用默认实时监控"
        monitor_mode=1
        ;;
esac

echo ""
echo "=========================================="
echo "监控结束"
echo "=========================================="
