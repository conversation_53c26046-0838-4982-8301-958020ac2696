#!/bin/bash
# Enhanced GPS Loop Closure System - Build and Run Script

set -e  # Exit on any error

echo "=========================================="
echo "Enhanced GPS Loop Closure System"
echo "Build and Run Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "src/CMakeLists.txt" ]; then
    print_error "Please run this script from the catkin workspace root directory"
    print_error "Expected: ~/alpha_lidar_GPS/software/alpha_lidar_ws/"
    exit 1
fi

print_step "Step 1: Installing Python dependencies"
echo "Installing required Python packages..."

# Install Python dependencies
pip3 install --user PyYAML numpy scipy || {
    print_warning "pip3 install failed, trying with sudo..."
    sudo pip3 install PyYAML numpy scipy || {
        print_error "Failed to install Python dependencies"
        exit 1
    }
}

print_status "Python dependencies installed successfully"

print_step "Step 2: Setting up environment"
echo "Setting up ROS environment..."

# Source ROS setup
if [ -f "/opt/ros/noetic/setup.bash" ]; then
    source /opt/ros/noetic/setup.bash
    print_status "ROS Noetic environment sourced"
elif [ -f "/opt/ros/melodic/setup.bash" ]; then
    source /opt/ros/melodic/setup.bash
    print_status "ROS Melodic environment sourced"
else
    print_error "ROS installation not found"
    exit 1
fi

# Set environment variables to fix encoding issues
export PYTHONIOENCODING=utf-8
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

print_status "Environment variables set for UTF-8 encoding"

print_step "Step 3: Building the workspace"
echo "Building catkin workspace..."

# Clean previous build if requested
if [ "$1" = "clean" ]; then
    print_warning "Cleaning previous build..."
    rm -rf build/ devel/
fi

# Build the workspace
catkin_make -DCMAKE_BUILD_TYPE=Release || {
    print_error "Build failed!"
    print_error "Common solutions:"
    print_error "1. Install missing dependencies: sudo apt-get install ros-noetic-pcl-ros ros-noetic-tf2-geometry-msgs"
    print_error "2. Clean build: ./build_and_run.sh clean"
    print_error "3. Check CMakeLists.txt for syntax errors"
    exit 1
}

print_status "Build completed successfully"

print_step "Step 4: Sourcing workspace"
# Source the workspace
if [ -f "devel/setup.bash" ]; then
    source devel/setup.bash
    print_status "Workspace sourced successfully"
else
    print_error "Workspace build failed - devel/setup.bash not found"
    exit 1
fi

print_step "Step 5: Verifying installation"
echo "Verifying nodes and launch files..."

# Check if the Python script is executable
PYTHON_SCRIPT="src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py"
if [ -f "$PYTHON_SCRIPT" ]; then
    chmod +x "$PYTHON_SCRIPT"
    print_status "Python script permissions set"
else
    print_error "Python script not found: $PYTHON_SCRIPT"
    exit 1
fi

# Check if C++ executable was built
CPP_EXECUTABLE="devel/lib/state_estimation/enhanced_slam_loop_closure_integration"
if [ -f "$CPP_EXECUTABLE" ]; then
    print_status "C++ executable built successfully"
else
    print_warning "C++ executable not found, but continuing..."
fi

# Check if launch file exists
LAUNCH_FILE="src/state_estimation/launch/mapping_robosense_with_enhanced_gps_loop.launch"
if [ -f "$LAUNCH_FILE" ]; then
    print_status "Launch file found"
else
    print_error "Launch file not found: $LAUNCH_FILE"
    exit 1
fi

print_step "Step 6: System ready"
echo "=========================================="
print_status "Enhanced GPS Loop Closure System is ready!"
echo "=========================================="

echo ""
echo "Usage options:"
echo ""
echo "1. Run the enhanced system:"
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch"
echo ""
echo "2. Run with custom parameters:"
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \\"
echo "     intermediate_loop_threshold:=6.0 \\"
echo "     gps_quality_threshold:=-1"
echo ""
echo "3. Monitor loop detection:"
echo "   rostopic echo /force_loop_closure"
echo "   rostopic echo /intermediate_loop_detected"
echo "   rostopic echo /detected_loop_type"
echo ""
echo "4. Debug mode (with console output):"
echo "   roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch --screen"
echo ""

# Ask if user wants to run the system now
echo ""
read -p "Do you want to start the enhanced GPS loop closure system now? (y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "Starting Enhanced GPS Loop Closure System..."
    echo ""
    print_status "Starting system with screen output for debugging..."
    print_status "Press Ctrl+C to stop the system"
    echo ""
    
    # Start the system
    roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch --screen
else
    print_status "System ready. You can start it manually using the commands above."
fi

echo ""
print_status "Script completed successfully!"
