#!/bin/bash

# GPS软约束SLAM系统编译修复脚本

echo "=========================================="
echo "🔧 GPS软约束SLAM系统编译修复"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤2: 安装完整依赖"
sudo apt update
sudo apt install -y \
    build-essential \
    cmake \
    ros-noetic-desktop-full \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-message-filters \
    libpcl-dev \
    pcl-tools \
    libeigen3-dev \
    libboost-all-dev \
    python3-numpy \
    python3-scipy \
    python3-matplotlib

echo ""
echo "步骤3: 修复Python依赖"
pip3 install numpy scipy matplotlib psutil

echo ""
echo "步骤4: 检查和修复代码问题"

# 检查GPS软约束检测器
if [ ! -f "src/state_estimation/scripts/gps_soft_loop_detector.py" ]; then
    echo "❌ GPS软约束检测器缺失"
    exit 1
else
    echo "✅ GPS软约束检测器存在"
fi

# 检查软约束处理器
if [ ! -f "src/state_estimation/scripts/soft_constraint_loop_processor.py" ]; then
    echo "❌ 软约束处理器缺失"
    exit 1
else
    echo "✅ 软约束处理器存在"
fi

# 检查GPS质量分析器
if [ ! -f "src/state_estimation/scripts/gps_quality_analyzer.py" ]; then
    echo "❌ GPS质量分析器缺失"
    exit 1
else
    echo "✅ GPS质量分析器存在"
fi

# 检查launch文件
if [ ! -f "src/state_estimation/launch/gps_soft_constraint_slam.launch" ]; then
    echo "❌ Launch文件缺失"
    exit 1
else
    echo "✅ Launch文件存在"
fi

echo ""
echo "步骤5: 设置权限"
chmod +x src/state_estimation/scripts/*.py
chmod +x *.sh
echo "✅ 权限设置完成"

echo ""
echo "步骤6: 彻底清理编译"
rm -rf build devel
echo "✅ 清理完成"

echo ""
echo "步骤7: 编译系统"
echo "使用Release模式编译..."

# 方法1: 标准编译
catkin_make -DCMAKE_BUILD_TYPE=Release -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    source devel/setup.bash
    
    echo ""
    echo "步骤8: 验证编译结果"
    
    # 检查核心可执行文件
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点"
    else
        echo "❌ SLAM核心节点编译失败"
        exit 1
    fi
    
    if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
        echo "✅ 强度保持模块"
    else
        echo "⚠️  强度保持模块缺失"
    fi
    
    # 检查Python脚本
    python3 -c "import sys; sys.path.append('src/state_estimation/scripts'); import gps_soft_loop_detector" 2>/dev/null && echo "✅ GPS软约束检测器语法正确" || echo "❌ GPS软约束检测器语法错误"
    
    python3 -c "import sys; sys.path.append('src/state_estimation/scripts'); import soft_constraint_loop_processor" 2>/dev/null && echo "✅ 软约束处理器语法正确" || echo "❌ 软约束处理器语法错误"
    
    python3 -c "import sys; sys.path.append('src/state_estimation/scripts'); import gps_quality_analyzer" 2>/dev/null && echo "✅ GPS质量分析器语法正确" || echo "❌ GPS质量分析器语法错误"
    
    echo ""
    echo "🎉 GPS软约束SLAM系统编译成功!"
    echo ""
    echo "可用的启动选项:"
    echo "1. 完整GPS软约束系统:"
    echo "   ./start_gps_soft_constraint_slam.sh"
    echo ""
    echo "2. 使用launch文件:"
    echo "   roslaunch state_estimation gps_soft_constraint_slam.launch"
    echo ""
    echo "3. 调试模式启动:"
    echo "   ./debug_gps_soft_slam.sh"
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "尝试兼容性编译..."
    
    # 方法2: 兼容性编译
    catkin_make -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_STANDARD=11 -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 兼容性编译成功!"
        source devel/setup.bash
        echo "可以使用基础功能"
    else
        echo ""
        echo "❌ 兼容性编译也失败!"
        echo ""
        echo "建议的解决方案:"
        echo "1. 检查ROS安装:"
        echo "   sudo apt install ros-noetic-desktop-full"
        echo ""
        echo "2. 检查PCL版本:"
        echo "   pkg-config --modversion pcl_common-1.10"
        echo ""
        echo "3. 检查编译器版本:"
        echo "   g++ --version"
        echo ""
        echo "4. 查看详细错误信息并手动修复"
        
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "编译修复完成!"
echo "=========================================="
