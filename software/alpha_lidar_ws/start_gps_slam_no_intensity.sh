#!/bin/bash

# GPS SLAM启动脚本 - 可选择禁用强度处理

echo "=========================================="
echo "🚀 GPS SLAM系统启动 (可配置强度处理)"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_slam_no_intensity_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "配置选项："
echo "1) 启用强度处理 (完整功能)"
echo "2) 禁用强度处理 (性能优先)"
echo "3) 仅保存强度，不分析 (平衡模式)"
echo ""

read -p "请选择模式 (1-3): " intensity_choice

case $intensity_choice in
    1)
        ENABLE_INTENSITY_SAVE=true
        ENABLE_INTENSITY_MONITOR=true
        ENABLE_INTENSITY_ANALYSIS=true
        echo "✅ 启用完整强度处理"
        ;;
    2)
        ENABLE_INTENSITY_SAVE=false
        ENABLE_INTENSITY_MONITOR=false
        ENABLE_INTENSITY_ANALYSIS=false
        echo "❌ 禁用所有强度处理"
        ;;
    3)
        ENABLE_INTENSITY_SAVE=true
        ENABLE_INTENSITY_MONITOR=false
        ENABLE_INTENSITY_ANALYSIS=false
        echo "📁 仅保存强度，不进行分析"
        ;;
    *)
        ENABLE_INTENSITY_SAVE=false
        ENABLE_INTENSITY_MONITOR=false
        ENABLE_INTENSITY_ANALYSIS=false
        echo "❌ 默认禁用强度处理"
        ;;
esac

echo ""
echo "GPS约束配置："
echo "1) 标准约束 (权重0.1)"
echo "2) 弱约束 (权重0.05)"
echo "3) 很弱约束 (权重0.01)"
echo "4) 禁用约束"
echo ""

read -p "请选择GPS约束强度 (1-4): " gps_choice

case $gps_choice in
    1)
        GPS_CONSTRAINT_WEIGHT=0.1
        GPS_ENABLE_CONSTRAINT=true
        echo "⚙️  使用标准GPS约束"
        ;;
    2)
        GPS_CONSTRAINT_WEIGHT=0.05
        GPS_ENABLE_CONSTRAINT=true
        echo "⚙️  使用弱GPS约束"
        ;;
    3)
        GPS_CONSTRAINT_WEIGHT=0.01
        GPS_ENABLE_CONSTRAINT=true
        echo "⚙️  使用很弱GPS约束"
        ;;
    4)
        GPS_CONSTRAINT_WEIGHT=0.0
        GPS_ENABLE_CONSTRAINT=false
        echo "❌ 禁用GPS约束"
        ;;
    *)
        GPS_CONSTRAINT_WEIGHT=0.05
        GPS_ENABLE_CONSTRAINT=true
        echo "⚙️  使用默认弱GPS约束"
        ;;
esac

echo ""
echo "启动系统组件..."

# 启动核心SLAM节点
echo "启动SLAM核心节点..."
rosrun state_estimation state_estimation_node &
sleep 3
echo "✅ SLAM核心节点已启动"

# 启动SLAM集成模块
if [ -f "devel/lib/state_estimation/enhanced_slam_loop_closure_integration" ]; then
    echo "启动SLAM集成模块..."
    rosrun state_estimation enhanced_slam_loop_closure_integration &
    sleep 2
    echo "✅ SLAM集成模块已启动"
fi

# 启动GPS回环优化器
if [ -f "src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py" ]; then
    echo "启动GPS回环优化器..."
    rosrun state_estimation enhanced_gps_loop_closure_optimizer.py &
    sleep 2
    echo "✅ GPS回环优化器已启动"
fi

# 启动智能检测器
if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
    echo "启动智能检测器..."
    rosrun state_estimation intelligent_start_end_detector.py &
    sleep 2
    echo "✅ 智能检测器已启动"
fi

# 启动GPS约束控制器
if [ -f "src/state_estimation/scripts/intelligent_gps_constraint_controller.py" ]; then
    echo "启动GPS约束控制器..."
    rosrun state_estimation intelligent_gps_constraint_controller.py &
    sleep 2
    echo "✅ GPS约束控制器已启动"
fi

# 启动强制匹配器
if [ -f "devel/lib/state_estimation/force_start_end_loop_matcher" ]; then
    echo "启动强制匹配器..."
    rosrun state_estimation force_start_end_loop_matcher &
    sleep 2
    echo "✅ 强制匹配器已启动"
fi

# 条件启动强度相关组件
if [ "$ENABLE_INTENSITY_SAVE" = true ]; then
    if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
        echo "启动强度保持模块..."
        rosrun state_estimation intensity_preserving_pcd_saver \
            _save_directory:="$OUTPUT_DIR" \
            _save_interval:=10.0 &
        sleep 2
        echo "✅ 强度保持模块已启动"
    fi
else
    echo "⏭️  跳过强度保持模块"
fi

if [ "$ENABLE_INTENSITY_MONITOR" = true ]; then
    if [ -f "src/state_estimation/scripts/intensity_quality_monitor.py" ]; then
        echo "启动强度质量监控..."
        rosrun state_estimation intensity_quality_monitor.py &
        sleep 2
        echo "✅ 强度质量监控已启动"
    fi
else
    echo "⏭️  跳过强度质量监控"
fi

if [ "$ENABLE_INTENSITY_ANALYSIS" = true ]; then
    if [ -f "src/state_estimation/scripts/simple_intensity_analyzer.py" ]; then
        echo "启动强度分析器..."
        rosrun state_estimation simple_intensity_analyzer.py &
        sleep 2
        echo "✅ 强度分析器已启动"
    fi
else
    echo "⏭️  跳过强度分析器"
fi

echo ""
echo "设置系统参数..."

# 等待系统稳定
sleep 5

# 设置GPS约束参数
echo "设置GPS约束参数..."
rosparam set /state_estimation_node/gps/enable_plane_constraint $GPS_ENABLE_CONSTRAINT
rosparam set /state_estimation_node/gps/plane_constraint_weight $GPS_CONSTRAINT_WEIGHT
rosparam set /state_estimation_node/gps/xy_correction_rate 0.02
rosparam set /state_estimation_node/gps/height_threshold 0.3

# 设置智能约束控制参数
echo "设置智能约束控制参数..."
rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.4
rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.15
rosparam set /intelligent_gps_constraint_controller/velocity_threshold 2.0

# 设置强度处理参数
if [ "$ENABLE_INTENSITY_SAVE" = true ]; then
    rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation true
else
    rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation false
fi

if [ "$ENABLE_INTENSITY_MONITOR" = true ]; then
    rosparam set /intensity_quality_monitor/enable_monitoring true
else
    rosparam set /intensity_quality_monitor/enable_monitoring false
fi

if [ "$ENABLE_INTENSITY_ANALYSIS" = true ]; then
    rosparam set /simple_intensity_analyzer/enable_analysis true
else
    rosparam set /simple_intensity_analyzer/enable_analysis false
fi

echo "✅ 参数设置完成"

echo ""
echo "=========================================="
echo "🚀 GPS SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统配置:"
echo "  输出目录: $OUTPUT_DIR"
echo "  GPS约束: $([ "$GPS_ENABLE_CONSTRAINT" = true ] && echo "启用 (权重$GPS_CONSTRAINT_WEIGHT)" || echo "禁用")"
echo "  强度保存: $([ "$ENABLE_INTENSITY_SAVE" = true ] && echo "启用" || echo "禁用")"
echo "  强度监控: $([ "$ENABLE_INTENSITY_MONITOR" = true ] && echo "启用" || echo "禁用")"
echo "  强度分析: $([ "$ENABLE_INTENSITY_ANALYSIS" = true ] && echo "启用" || echo "禁用")"
echo ""
echo "实时参数调节命令:"
echo ""
echo "GPS约束调节:"
echo "  禁用GPS约束: rosparam set /state_estimation_node/gps/enable_plane_constraint false"
echo "  启用GPS约束: rosparam set /state_estimation_node/gps/enable_plane_constraint true"
echo "  调节约束权重: rosparam set /state_estimation_node/gps/plane_constraint_weight 0.05"
echo "  调节校正率: rosparam set /state_estimation_node/gps/xy_correction_rate 0.01"
echo ""
echo "强度处理控制:"
echo "  禁用强度保存: rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation false"
echo "  禁用强度监控: rosparam set /intensity_quality_monitor/enable_monitoring false"
echo "  禁用强度分析: rosparam set /simple_intensity_analyzer/enable_analysis false"
echo ""
echo "停止强度处理节点:"
echo "  rosnode kill /intensity_preserving_pcd_saver"
echo "  rosnode kill /intensity_quality_monitor"
echo "  rosnode kill /simple_intensity_analyzer"
echo ""
echo "监控命令:"
echo "  GPS约束状态: rostopic echo /gps_constraint_control"
echo "  智能检测:    rostopic echo /intelligent_detector_status"
echo "  SLAM位姿:    rostopic echo /aft_mapped_to_init"
if [ "$ENABLE_INTENSITY_MONITOR" = true ]; then
    echo "  强度质量:    rostopic echo /intensity_quality_report"
fi
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
