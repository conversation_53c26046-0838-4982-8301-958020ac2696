#!/bin/bash

# GPS参考增强SLAM系统最终编译和执行脚本
# 包含配置文件验证和修改

echo "=========================================="
echo "🔧 GPS参考增强SLAM系统最终编译执行"
echo "=========================================="
echo ""
echo "系统特性："
echo "🎯 GPS仅作为参考位置，不直接修改SLAM"
echo "✅ 完全避免GPS约束导致的点云匹配错位"
echo "🚀 通过增强SLAM自身匹配能力解决首尾偏差"
echo "💎 多分辨率、多算法自适应匹配"
echo ""

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    echo "当前目录: $(pwd)"
    echo "请执行: cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
    exit 1
fi

echo "步骤1: 验证和修改配置文件"

# 检查rs16_rotation_v2.yaml配置
CONFIG_FILE="src/state_estimation/config/rs16_rotation_v2.yaml"
if [ -f "$CONFIG_FILE" ]; then
    echo "检查GPS约束配置..."
    
    # 检查enable_plane_constraint
    if grep -q "enable_plane_constraint: true" "$CONFIG_FILE"; then
        echo "⚠️  发现GPS平面约束已启用，正在禁用..."
        sed -i 's/enable_plane_constraint: true/enable_plane_constraint: false/' "$CONFIG_FILE"
        echo "✅ GPS平面约束已禁用"
    elif grep -q "enable_plane_constraint: false" "$CONFIG_FILE"; then
        echo "✅ GPS平面约束已正确禁用"
    else
        echo "⚠️  未找到GPS平面约束配置，添加禁用配置..."
        sed -i '/gps:/a\    enable_plane_constraint: false  # Disable GPS plane constraint' "$CONFIG_FILE"
    fi
    
    # 检查constraint_mode
    if grep -q "constraint_mode: [1-3]" "$CONFIG_FILE"; then
        echo "⚠️  发现GPS约束模式已启用，正在禁用..."
        sed -i 's/constraint_mode: [1-3]/constraint_mode: 0/' "$CONFIG_FILE"
        echo "✅ GPS约束模式已禁用"
    elif grep -q "constraint_mode: 0" "$CONFIG_FILE"; then
        echo "✅ GPS约束模式已正确禁用"
    fi
    
    # 检查plane_constraint_weight
    if grep -q "plane_constraint_weight: [0-9]" "$CONFIG_FILE"; then
        current_weight=$(grep "plane_constraint_weight:" "$CONFIG_FILE" | grep -o '[0-9.]*')
        if (( $(echo "$current_weight > 0" | bc -l) )); then
            echo "⚠️  发现GPS约束权重 > 0，正在设置为0..."
            sed -i 's/plane_constraint_weight: [0-9.]*/plane_constraint_weight: 0.0/' "$CONFIG_FILE"
            echo "✅ GPS约束权重已设置为0"
        else
            echo "✅ GPS约束权重已正确设置为0"
        fi
    fi
    
    echo "✓ 配置文件验证和修改完成"
else
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    echo "将使用默认参数"
fi

echo ""
echo "步骤2: 设置ROS环境"
source /opt/ros/noetic/setup.bash
echo "✓ ROS环境已设置"

echo ""
echo "步骤3: 安装依赖"
echo "检查并安装必要依赖..."

# 检查Python依赖
python3 -c "import numpy" 2>/dev/null || {
    echo "安装numpy..."
    pip3 install numpy
}

python3 -c "import scipy" 2>/dev/null || {
    echo "安装scipy..."
    pip3 install scipy
}

# 检查ROS依赖
dpkg -l | grep -q ros-noetic-tf2-ros || {
    echo "安装ROS tf2依赖..."
    sudo apt update
    sudo apt install -y ros-noetic-tf2-ros ros-noetic-tf2-geometry-msgs
}

dpkg -l | grep -q libpcl-dev || {
    echo "安装PCL依赖..."
    sudo apt install -y libpcl-dev pcl-tools
}

echo "✓ 依赖检查完成"

echo ""
echo "步骤4: 清理并编译"
rm -rf build devel
echo "✓ 清理完成"

echo "开始编译..."
catkin_make -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    # 设置权限
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤5: 验证系统组件"
    
    # 检查核心组件
    components=(
        "devel/lib/state_estimation/state_estimation_node:SLAM核心节点"
        "devel/lib/state_estimation/force_start_end_loop_matcher:强制匹配器"
        "src/state_estimation/scripts/gps_reference_enhanced_slam.py:GPS参考增强器"
        "src/state_estimation/scripts/intelligent_start_end_detector.py:智能检测器"
    )
    
    all_good=true
    for component in "${components[@]}"; do
        IFS=':' read -r path name <<< "$component"
        if [ -f "$path" ]; then
            echo "✅ $name"
        else
            echo "⚠️  $name (缺失)"
            if [[ "$path" == *"state_estimation_node"* ]]; then
                all_good=false
            fi
        fi
    done
    
    echo ""
    echo "步骤6: 配置验证"
    echo "最终配置验证..."
    
    if grep -q "enable_plane_constraint: false" "$CONFIG_FILE"; then
        echo "✅ GPS平面约束已禁用"
    else
        echo "⚠️  GPS平面约束状态未知"
    fi
    
    if grep -q "constraint_mode: 0" "$CONFIG_FILE"; then
        echo "✅ GPS约束模式已禁用"
    else
        echo "⚠️  GPS约束模式状态未知"
    fi
    
    echo ""
    if [ "$all_good" = true ]; then
        echo "🎉 系统完全准备就绪!"
        
        echo ""
        echo "=========================================="
        echo "🚀 启动选项"
        echo "=========================================="
        echo ""
        echo "选择启动方式："
        echo "1) 启动GPS参考增强SLAM系统"
        echo "2) 启动RVIZ可视化"
        echo "3) 同时启动SLAM和RVIZ"
        echo "4) 显示使用说明"
        echo "5) 仅编译完成"
        echo ""
        
        read -p "请选择 (1-5): " choice
        
        case $choice in
            1)
                echo ""
                echo "🚀 启动GPS参考增强SLAM系统..."
                ./start_gps_reference_enhanced_slam.sh
                ;;
            2)
                echo ""
                echo "🖥️  启动RVIZ可视化..."
                rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz 2>/dev/null || rviz
                ;;
            3)
                echo ""
                echo "🚀 同时启动SLAM和RVIZ..."
                ./start_gps_reference_enhanced_slam.sh &
                sleep 8
                rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz 2>/dev/null || rviz
                ;;
            4)
                echo ""
                echo "📋 使用说明"
                echo "=========================================="
                echo ""
                echo "手动启动命令："
                echo "  启动SLAM: ./start_gps_reference_enhanced_slam.sh"
                echo "  启动RVIZ: rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz"
                echo "  播放数据: rosbag play your_data.bag"
                echo ""
                echo "监控命令："
                echo "  GPS参考状态: rostopic echo /gps_reference_slam_status"
                echo "  增强触发:    rostopic echo /slam_enhancement_trigger"
                echo "  增强参数:    rostopic echo /enhanced_slam_parameters"
                echo "  参考引导:    rostopic echo /gps_reference_guidance"
                echo ""
                echo "参数调节："
                echo "  GPS参考半径: rosparam set /gps_reference_enhanced_slam/gps_reference_radius 40.0"
                echo "  增强半径:    rosparam set /gps_reference_enhanced_slam/slam_enhancement_radius 25.0"
                echo "  搜索半径:    rosparam set /gps_reference_enhanced_slam/enhanced_search_radius 100.0"
                echo ""
                echo "配置文件位置："
                echo "  $CONFIG_FILE"
                echo ""
                echo "关键配置项："
                echo "  enable_plane_constraint: false  (已禁用GPS平面约束)"
                echo "  constraint_mode: 0              (已禁用GPS约束模式)"
                echo "  plane_constraint_weight: 0.0    (GPS约束权重为0)"
                ;;
            5)
                echo ""
                echo "✅ 编译完成，系统已准备就绪"
                ;;
            *)
                echo ""
                echo "无效选择，编译完成"
                ;;
        esac
        
    else
        echo "⚠️  部分组件缺失，但核心功能可用"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方案："
    echo "1. 检查ROS完整安装:"
    echo "   sudo apt install ros-noetic-desktop-full"
    echo ""
    echo "2. 检查依赖:"
    echo "   sudo apt install libpcl-dev python3-numpy python3-scipy"
    echo ""
    echo "3. 单独编译:"
    echo "   catkin_make --only-pkg-with-deps state_estimation -j1"
    
    exit 1
fi

echo ""
echo "=========================================="
echo "🎯 GPS参考增强SLAM系统说明"
echo "=========================================="
echo ""
echo "系统工作原理："
echo "🎯 GPS仅提供参考位置，不直接修改SLAM轨迹"
echo "📍 避免GPS约束导致的点云匹配错位问题"
echo "🔄 GPS接近历史位置时触发SLAM匹配增强"
echo "💎 根据GPS质量动态选择增强级别"
echo "⚡ 多分辨率、多算法自适应匹配"
echo ""
echo "配置文件修改："
echo "✅ enable_plane_constraint: false (禁用GPS平面约束)"
echo "✅ constraint_mode: 0 (禁用GPS约束模式)"
echo "✅ plane_constraint_weight: 0.0 (GPS约束权重为0)"
echo ""
echo "预期效果："
echo "✅ 完全避免点云匹配错位"
echo "✅ 首尾偏差显著减少"
echo "✅ SLAM轨迹更加连续和精确"
echo "✅ 保持完整的强度信息"
echo ""
echo "=========================================="
echo "系统准备完成!"
echo "=========================================="
