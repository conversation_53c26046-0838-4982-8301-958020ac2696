#!/bin/bash

# 实时调节GPS回环检测参数脚本

echo "=========================================="
echo "GPS回环检测参数实时调节工具"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "错误: ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "当前参数设置："
echo "距离阈值: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 2>/dev/null || echo '未设置')"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius 2>/dev/null || echo '未设置')"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold 2>/dev/null || echo '未设置')"
echo ""

echo "选择调节方案："
echo "1) 轻度放宽 (距离35m, 搜索45m, 匹配0.7)"
echo "2) 中度放宽 (距离45m, 搜索55m, 匹配0.8)"
echo "3) 大幅放宽 (距离55m, 搜索65m, 匹配0.85)"
echo "4) 超级放宽 (距离70m, 搜索80m, 匹配0.9)"
echo "5) 自定义设置"
echo "6) 查看当前状态"
echo ""

read -p "请选择 (1-6): " choice

case $choice in
    1)
        echo "应用轻度放宽参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 35.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 45.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.7
        ;;
    2)
        echo "应用中度放宽参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 45.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 55.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.8
        ;;
    3)
        echo "应用大幅放宽参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 55.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 65.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.85
        ;;
    4)
        echo "应用超级放宽参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 70.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 80.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.9
        ;;
    5)
        echo "自定义参数设置："
        read -p "首尾距离阈值 (米, 当前: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 2>/dev/null)): " distance
        read -p "搜索半径 (米, 当前: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius 2>/dev/null)): " radius
        read -p "匹配阈值 (0-1, 当前: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold 2>/dev/null)): " threshold
        
        if [ ! -z "$distance" ]; then
            rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold $distance
        fi
        if [ ! -z "$radius" ]; then
            rosparam set /enhanced_slam_loop_closure_integration/force_search_radius $radius
        fi
        if [ ! -z "$threshold" ]; then
            rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold $threshold
        fi
        ;;
    6)
        echo "当前系统状态："
        echo "正在获取GPS回环状态..."
        timeout 5 rostopic echo /enhanced_gps_loop_closure_status -n 1
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "参数已更新："
echo "距离阈值: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold)"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius)"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold)"
echo ""
echo "监控首尾回环检测："
echo "rostopic echo /enhanced_gps_loop_closure_status | grep 'Start-end'"
