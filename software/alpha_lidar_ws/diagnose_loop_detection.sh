#!/bin/bash

# GPS回环检测问题诊断脚本

echo "=========================================="
echo "GPS回环检测系统诊断"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行"
    echo "解决方法: 启动roscore或SLAM系统"
    exit 1
fi
echo "✓ ROS正在运行"

# 检查关键节点
echo ""
echo "检查关键节点状态:"
nodes=("enhanced_gps_loop_optimizer" "enhanced_slam_loop_integration" "intensity_preserving_saver")
for node in "${nodes[@]}"; do
    if rosnode list | grep -q "$node"; then
        echo "✓ $node 正在运行"
    else
        echo "❌ $node 未运行"
    fi
done

# 检查关键话题
echo ""
echo "检查关键话题:"
topics=("/rtk/gnss" "/velodyne_points" "/force_loop_closure" "/enhanced_gps_loop_closure_status")
for topic in "${topics[@]}"; do
    if rostopic list | grep -q "$topic"; then
        echo "✓ $topic 存在"
    else
        echo "❌ $topic 不存在"
    fi
done

# 检查GPS数据
echo ""
echo "检查GPS数据 (5秒):"
timeout 5 rostopic echo /rtk/gnss -n 1 | head -10

# 检查当前参数
echo ""
echo "当前GPS回环参数:"
echo "距离阈值: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 2>/dev/null || echo '未设置')"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius 2>/dev/null || echo '未设置')"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold 2>/dev/null || echo '未设置')"

# 检查回环状态
echo ""
echo "最新回环状态 (10秒):"
timeout 10 rostopic echo /enhanced_gps_loop_closure_status -n 1

echo ""
echo "=========================================="
echo "诊断建议:"
echo "=========================================="

# 获取轨迹信息进行分析
trajectory_info=$(timeout 5 rostopic echo /enhanced_gps_loop_closure_status -n 1 2>/dev/null)
if [ $? -eq 0 ]; then
    trajectory_length=$(echo "$trajectory_info" | grep "Trajectory length" | grep -o '[0-9.]*m' | head -1)
    detected_loops=$(echo "$trajectory_info" | grep "Detected loops" | grep -o '[0-9]*' | head -1)
    distance_threshold=$(echo "$trajectory_info" | grep "Start-end distance threshold" | grep -o '[0-9.]*m' | head -1)
    
    echo "轨迹长度: $trajectory_length"
    echo "检测到的回环: $detected_loops"
    echo "距离阈值: $distance_threshold"
    echo ""
    
    # 提供具体建议
    if [[ "$distance_threshold" == "5.0m" ]]; then
        echo "⚠️  距离阈值太小 (5.0m)，建议:"
        echo "   ./adjust_loop_params.sh  # 选择选项2或3"
        echo "   或者重新启动使用: ./ultra_loose_start_end.sh"
    elif [[ "$detected_loops" == "0" ]] || [[ -z "$detected_loops" ]]; then
        echo "⚠️  未检测到回环，建议:"
        echo "   1. 增大距离阈值: ./adjust_loop_params.sh"
        echo "   2. 检查轨迹是否形成闭环"
        echo "   3. 确保GPS数据质量"
    else
        echo "✓ 系统运行正常，已检测到 $detected_loops 个回环"
    fi
else
    echo "⚠️  无法获取回环状态，可能的原因:"
    echo "   1. GPS回环检测器未启动"
    echo "   2. GPS数据不可用"
    echo "   3. 系统刚启动，等待数据积累"
fi

echo ""
echo "快速修复命令:"
echo "  立即放宽参数: ./adjust_loop_params.sh"
echo "  重启超宽松模式: ./ultra_loose_start_end.sh"
echo "  监控状态: rostopic echo /enhanced_gps_loop_closure_status"
