#!/bin/bash

# 点云匹配错乱问题修复脚本
# 自动调优关键参数以提高匹配稳定性

echo "=========================================="
echo "🔧 点云匹配错乱问题修复脚本"
echo "=========================================="

CONFIG_FILE="src/state_estimation/config/rs16_rotation_v2.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "📝 备份原始配置文件..."
cp "$CONFIG_FILE" "${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"

echo "🔧 应用点云匹配优化参数..."

# 1. 优化核心SLAM参数
echo "  - 优化SLAM核心参数"
sed -i 's/max_iteration: 4/max_iteration: 8/' "$CONFIG_FILE"
sed -i 's/voxel_size: 0.25/voxel_size: 0.2/' "$CONFIG_FILE"
sed -i 's/max_layer: 2/max_layer: 3/' "$CONFIG_FILE"
sed -i 's/plannar_threshold: 0.01/plannar_threshold: 0.005/' "$CONFIG_FILE"
sed -i 's/down_sample_size: 0.2/down_sample_size: 0.15/' "$CONFIG_FILE"

# 2. 优化体素地图参数
echo "  - 优化体素地图参数"
sed -i 's/max_points_size: 1000/max_points_size: 1500/' "$CONFIG_FILE"
sed -i 's/max_cov_points_size: 1000/max_cov_points_size: 1500/' "$CONFIG_FILE"
sed -i 's/layer_point_size: \[ 5, 5, 5, 5, 5 \]/layer_point_size: [ 8, 8, 8, 8, 8 ]/' "$CONFIG_FILE"

# 3. 优化噪声模型
echo "  - 优化噪声模型参数"
sed -i 's/ranging_cov: 0.04/ranging_cov: 0.02/' "$CONFIG_FILE"
sed -i 's/angle_cov: 0.1/angle_cov: 0.05/' "$CONFIG_FILE"
sed -i 's/acc_cov: 0.1/acc_cov: 0.05/' "$CONFIG_FILE"
sed -i 's/gyr_cov: 0.01/gyr_cov: 0.005/' "$CONFIG_FILE"

# 4. 禁用可能干扰的GPS约束
echo "  - 禁用GPS平面约束"
sed -i 's/enable_plane_constraint: false/enable_plane_constraint: false/' "$CONFIG_FILE"
sed -i 's/plane_constraint_weight: 0.0/plane_constraint_weight: 0.0/' "$CONFIG_FILE"

# 5. 优化ICP参数
echo "  - 优化ICP回环检测参数"
sed -i 's/icp_fitness_threshold: 0.3/icp_fitness_threshold: 0.2/' "$CONFIG_FILE"
sed -i 's/icp_max_correspondence_distance: 1.0/icp_max_correspondence_distance: 0.8/' "$CONFIG_FILE"

# 6. 调整检测范围
echo "  - 调整检测范围"
sed -i 's/det_range:     100.0/det_range:     80.0/' "$CONFIG_FILE"
sed -i 's/blind: 0.8/blind: 0.5/' "$CONFIG_FILE"

echo ""
echo "✅ 参数优化完成!"
echo ""
echo "📊 优化后的关键参数:"
echo "  - 最大迭代次数: 4 → 8"
echo "  - 体素大小: 0.25 → 0.2"
echo "  - 平面阈值: 0.01 → 0.005"
echo "  - 距离噪声: 0.04 → 0.02"
echo "  - 角度噪声: 0.1 → 0.05"
echo "  - ICP适应度阈值: 0.3 → 0.2"
echo ""
echo "🔄 重新编译系统..."
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation" -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 系统优化完成!"
    echo ""
    echo "📋 测试建议:"
    echo "1. 使用较慢的播放速度测试: rosbag play data.bag -r 0.5"
    echo "2. 监控匹配质量: rostopic echo /icp_fitness_score"
    echo "3. 观察轨迹连续性: rviz中查看/path话题"
    echo "4. 如果仍有问题，可进一步降低播放速度或调整参数"
    echo ""
    echo "🔧 如需恢复原始配置:"
    echo "   cp ${CONFIG_FILE}.backup.* $CONFIG_FILE"
else
    echo ""
    echo "❌ 编译失败，请检查错误信息"
    echo "🔄 恢复原始配置..."
    cp "${CONFIG_FILE}.backup."* "$CONFIG_FILE"
fi
