#!/bin/bash

# 数据源验证脚本

echo "=========================================="
echo "📊 数据源验证"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "步骤1: 检查ROS环境"
echo "=================="

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "✅ ROS环境正常"

echo ""
echo "步骤2: 检查可用的topic"
echo "===================="

echo "等待topic列表..."
sleep 2

echo ""
echo "所有可用topic:"
rostopic list

echo ""
echo "点云相关topic:"
rostopic list | grep -E "(points|cloud|velodyne|lidar)" || echo "❌ 未找到点云topic"

echo ""
echo "IMU相关topic:"
rostopic list | grep -E "(imu|IMU)" || echo "❌ 未找到IMU topic"

echo ""
echo "GPS相关topic:"
rostopic list | grep -E "(gps|gnss|GPS|rtk)" || echo "❌ 未找到GPS topic"

echo ""
echo "步骤3: 检查数据流"
echo "================"

# 检查点云数据
LIDAR_TOPICS=$(rostopic list | grep -E "(points|cloud|velodyne)" | head -3)

if [ -n "$LIDAR_TOPICS" ]; then
    echo ""
    echo "检查点云数据流:"
    for topic in $LIDAR_TOPICS; do
        echo ""
        echo "检查topic: $topic"
        echo "------------------------"
        
        # 检查消息类型
        MSG_TYPE=$(rostopic type "$topic" 2>/dev/null)
        echo "消息类型: $MSG_TYPE"
        
        # 检查发布频率
        echo "检查发布频率 (5秒)..."
        FREQ=$(timeout 5 rostopic hz "$topic" 2>/dev/null | grep "average rate" | awk '{print $3}')
        if [ -n "$FREQ" ]; then
            echo "发布频率: ${FREQ} Hz"
        else
            echo "❌ 无数据或频率过低"
            continue
        fi
        
        # 检查消息内容
        echo "检查消息内容..."
        MSG_INFO=$(timeout 3 rostopic echo "$topic" -n 1 2>/dev/null)
        if [ -n "$MSG_INFO" ]; then
            # 提取点云信息
            POINT_COUNT=$(echo "$MSG_INFO" | grep -E "width:|height:" | head -2)
            echo "点云信息:"
            echo "$POINT_COUNT"
            
            # 检查是否有强度信息
            if echo "$MSG_INFO" | grep -q "intensity"; then
                echo "✅ 包含强度信息"
            else
                echo "⚠️  不包含强度信息"
            fi
        else
            echo "❌ 无法读取消息内容"
        fi
    done
else
    echo "❌ 未找到点云topic，请检查数据源"
fi

# 检查IMU数据
IMU_TOPICS=$(rostopic list | grep -E "(imu|IMU)" | head -2)

if [ -n "$IMU_TOPICS" ]; then
    echo ""
    echo "检查IMU数据流:"
    for topic in $IMU_TOPICS; do
        echo ""
        echo "检查topic: $topic"
        echo "------------------------"
        
        MSG_TYPE=$(rostopic type "$topic" 2>/dev/null)
        echo "消息类型: $MSG_TYPE"
        
        FREQ=$(timeout 5 rostopic hz "$topic" 2>/dev/null | grep "average rate" | awk '{print $3}')
        if [ -n "$FREQ" ]; then
            echo "发布频率: ${FREQ} Hz"
        else
            echo "❌ 无数据或频率过低"
        fi
    done
else
    echo "❌ 未找到IMU topic"
fi

# 检查GPS数据
GPS_TOPICS=$(rostopic list | grep -E "(gps|gnss|GPS|rtk)" | head -2)

if [ -n "$GPS_TOPICS" ]; then
    echo ""
    echo "检查GPS数据流:"
    for topic in $GPS_TOPICS; do
        echo ""
        echo "检查topic: $topic"
        echo "------------------------"
        
        MSG_TYPE=$(rostopic type "$topic" 2>/dev/null)
        echo "消息类型: $MSG_TYPE"
        
        FREQ=$(timeout 5 rostopic hz "$topic" 2>/dev/null | grep "average rate" | awk '{print $3}')
        if [ -n "$FREQ" ]; then
            echo "发布频率: ${FREQ} Hz"
        else
            echo "❌ 无数据或频率过低"
        fi
    done
else
    echo "❌ 未找到GPS topic"
fi

echo ""
echo "步骤4: 生成配置建议"
echo "=================="

# 找到最佳的topic配置
BEST_LIDAR=$(rostopic list | grep -E "(velodyne_points|points_raw|cloud)" | head -1)
BEST_IMU=$(rostopic list | grep -E "(/imu/data|/imu_raw)" | head -1)
BEST_GPS=$(rostopic list | grep -E "(/rtk/gnss|/gps/fix|/navsat)" | head -1)

echo ""
echo "🎯 推荐的topic配置:"
echo "==================="

if [ -n "$BEST_LIDAR" ]; then
    echo "点云topic: $BEST_LIDAR"
    echo "  rosparam set /state_estimation_node/lidar_topic $BEST_LIDAR"
else
    echo "❌ 未找到合适的点云topic"
fi

if [ -n "$BEST_IMU" ]; then
    echo "IMU topic: $BEST_IMU"
    echo "  rosparam set /state_estimation_node/imu_topic $BEST_IMU"
else
    echo "❌ 未找到合适的IMU topic"
fi

if [ -n "$BEST_GPS" ]; then
    echo "GPS topic: $BEST_GPS"
    echo "  rosparam set /state_estimation_node/gps_topic $BEST_GPS"
else
    echo "❌ 未找到合适的GPS topic"
fi

echo ""
echo "步骤5: 创建自动配置脚本"
echo "======================"

# 创建自动配置脚本
cat > auto_configure_topics.sh << EOF
#!/bin/bash

# 自动topic配置脚本
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "自动配置topic..."

# 设置找到的最佳topic
$([ -n "$BEST_LIDAR" ] && echo "rosparam set /state_estimation_node/lidar_topic $BEST_LIDAR")
$([ -n "$BEST_IMU" ] && echo "rosparam set /state_estimation_node/imu_topic $BEST_IMU")
$([ -n "$BEST_GPS" ] && echo "rosparam set /state_estimation_node/gps_topic $BEST_GPS")

# 设置安全参数
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/voxel_size 0.8
rosparam set /state_estimation_node/max_iterations 20

echo "✅ Topic配置完成"
EOF

chmod +x auto_configure_topics.sh

echo "✅ 自动配置脚本已创建: auto_configure_topics.sh"

echo ""
echo "步骤6: 数据质量评估"
echo "=================="

if [ -n "$BEST_LIDAR" ]; then
    echo ""
    echo "评估点云数据质量..."
    
    # 获取一帧点云数据进行分析
    SAMPLE_MSG=$(timeout 5 rostopic echo "$BEST_LIDAR" -n 1 2>/dev/null)
    
    if [ -n "$SAMPLE_MSG" ]; then
        # 提取点云尺寸信息
        WIDTH=$(echo "$SAMPLE_MSG" | grep "width:" | awk '{print $2}')
        HEIGHT=$(echo "$SAMPLE_MSG" | grep "height:" | awk '{print $2}')
        
        if [ -n "$WIDTH" ] && [ -n "$HEIGHT" ]; then
            TOTAL_POINTS=$((WIDTH * HEIGHT))
            echo "点云尺寸: ${WIDTH} x ${HEIGHT} = ${TOTAL_POINTS} 点"
            
            if [ $TOTAL_POINTS -gt 10000 ]; then
                echo "✅ 点云密度充足"
            elif [ $TOTAL_POINTS -gt 1000 ]; then
                echo "⚠️  点云密度中等，建议降低处理参数"
            else
                echo "❌ 点云密度过低，可能影响SLAM效果"
            fi
        fi
        
        # 检查点云字段
        if echo "$SAMPLE_MSG" | grep -q "intensity"; then
            echo "✅ 包含强度字段"
        fi
        
        if echo "$SAMPLE_MSG" | grep -q "ring"; then
            echo "✅ 包含线束信息"
        fi
    fi
fi

echo ""
echo "=========================================="
echo "数据源验证完成"
echo "=========================================="
echo ""
echo "🎯 下一步操作建议:"
echo ""
echo "1. 如果找到了合适的topic，运行:"
echo "   ./auto_configure_topics.sh"
echo ""
echo "2. 然后使用安全模式启动SLAM:"
echo "   roslaunch state_estimation safe_slam.launch"
echo ""
echo "3. 如果仍然崩溃，运行崩溃修复脚本:"
echo "   ./fix_slam_crash.sh"
echo ""
echo "4. 播放数据进行测试:"
echo "   rosbag play your_data.bag"
