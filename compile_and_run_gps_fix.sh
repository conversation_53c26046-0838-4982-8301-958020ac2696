#!/bin/bash

# GPS大偏移修复 - 完整编译和执行脚本

echo "=========================================="
echo "🚀 GPS大偏移修复 - 完整解决方案"
echo "=========================================="

# 设置工作目录
WORKSPACE_DIR="/home/<USER>/slam_share/AI_code/github_alidar03/alpha_lidar_GPS/software/alpha_lidar_ws"
BAG_FILE="/home/<USER>/slam_share/AI_code/github_alidar03/alpha_lidar_GPS/datasets/UM982loop_715std_maximum_synced.bag"

cd "$WORKSPACE_DIR"

echo ""
echo "步骤1: 环境准备"

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo ""
echo "步骤2: 编译增强GPS系统"

# 清理编译
echo "清理之前的编译..."
rm -rf build/ devel/
catkin_make clean

# 重新编译
echo "编译增强GPS回环检测系统..."
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation" -j4

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 重新source
source devel/setup.bash

echo ""
echo "步骤3: 检查数据文件"

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ 数据文件不存在: $BAG_FILE"
    echo "请检查路径或使用其他bag文件"
    exit 1
fi

echo "✅ 数据文件存在: $BAG_FILE"

echo ""
echo "步骤4: 启动增强GPS SLAM系统"

# 启动极端GPS偏移修复系统
echo "启动极端GPS偏移修复SLAM系统..."
roslaunch state_estimation mapping_robosense_extreme_gps_fix.launch &
LAUNCH_PID=$!

echo "等待系统初始化..."
sleep 10

# 检查启动状态
if ! ps -p $LAUNCH_PID > /dev/null; then
    echo "❌ 系统启动失败"
    exit 1
fi

echo "✅ 系统启动成功"

echo ""
echo "步骤5: 应用激进GPS约束参数"

# 应用激进参数修复大偏移
echo "应用激进GPS约束参数..."
chmod +x fix_large_gps_offset.sh
./fix_large_gps_offset.sh

echo ""
echo "步骤6: 播放数据包"

echo "开始播放数据包..."
echo "数据包: $BAG_FILE"

# 播放bag文件
rosbag play "$BAG_FILE" --pause &
BAG_PID=$!

echo ""
echo "🎯 系统已启动！"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Launch PID: $LAUNCH_PID"
echo "Bag PID: $BAG_PID"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo ""
echo "📊 实时监控命令 (新终端中运行):"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "# 监控GPS回环距离"
echo "rostopic echo /loop_closure_distance"
echo ""
echo "# 监控回环检测结果"
echo "rostopic echo /force_loop_closure"
echo ""
echo "# 监控匹配分数"
echo "rostopic echo /matching_score"
echo ""
echo "# 监控回环类型"
echo "rostopic echo /detected_loop_type"
echo ""
echo "# 监控GPS状态"
echo "rostopic echo /rtk/gnss"

echo ""
echo "🎮 控制命令:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "# 在bag播放窗口按空格键开始播放"
echo "# 如果出现过度校正，运行:"
echo "./restore_conservative_gps.sh"
echo ""
echo "# 停止系统:"
echo "kill $LAUNCH_PID $BAG_PID"

echo ""
echo "⚠️  重要提示:"
echo "1. 当前使用激进GPS约束，可能过度校正"
echo "2. 请实时监控首尾偏移变化"
echo "3. 如果偏移降低到5米以内，考虑切换到保守模式"
echo "4. 系统会自动记录轨迹和回环检测结果"

echo ""
echo "🎯 预期效果:"
echo "- 首尾偏移从几十米降低到几米甚至厘米级"
echo "- 回环检测频率显著增加"
echo "- GPS约束会持续校正SLAM轨迹"

# 等待用户操作
echo ""
echo "按 Ctrl+C 停止监控，系统将继续运行..."
trap 'echo "监控停止，系统继续运行..."; exit 0' INT

# 持续监控
while true; do
    sleep 5
    if ! ps -p $LAUNCH_PID > /dev/null; then
        echo "❌ SLAM系统已停止"
        break
    fi
    if ! ps -p $BAG_PID > /dev/null; then
        echo "✅ 数据包播放完成"
        break
    fi
done
