#!/bin/bash

# 点云匹配错乱修复脚本
# 从激进模式降级到平衡模式，避免过度校正

echo "=========================================="
echo "🔧 点云匹配错乱修复 - 平衡模式"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动roscore"
    exit 1
fi

echo ""
echo "🎯 问题诊断: 点云匹配错乱"
echo "原因: GPS约束过于激进，导致过度校正"
echo "策略: 降级到平衡模式，保持精度的同时避免错乱"

echo ""
echo "步骤1: 立即停止过度校正"

# 降低GPS约束强度到平衡水平
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.3
echo "✅ 平面约束权重: 0.98 → 0.3 (大幅降低)"

rosparam set /state_estimation_node/gps/xy_correction_rate 0.15
echo "✅ XY校正率: 98% → 15% (大幅降低)"

rosparam set /state_estimation_node/gps/correction_rate 0.25
echo "✅ 高度校正率: 95% → 25% (大幅降低)"

echo ""
echo "步骤2: 调整回环检测参数到合理范围"

# 减小GPS回环检测距离阈值
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 25.0
echo "✅ GPS回环距离阈值: 150.0m → 25.0m"

# 增加最小轨迹长度要求
rosparam set /enhanced_gps_loop_closure_optimizer/min_trajectory_length 40.0
echo "✅ 最小轨迹长度: 10.0m → 40.0m"

# 降低检测频率
rosparam set /enhanced_gps_loop_closure_optimizer/check_interval 1.0
echo "✅ 检查间隔: 0.1s → 1.0s"

# 增加冷却时间
rosparam set /enhanced_gps_loop_closure_optimizer/loop_detection_cooldown 3.0
echo "✅ 冷却时间: 0.1s → 3.0s"

echo ""
echo "步骤3: 调整SLAM匹配参数到合理范围"

# 减小搜索半径
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 40.0
echo "✅ 搜索半径: 250.0m → 40.0m"

# 严格化匹配分数阈值
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.6
echo "✅ 起点-终点匹配阈值: 5.0 → 0.6"

# 减少搜索候选数
rosparam set /enhanced_slam_loop_closure_integration/max_search_candidates 20
echo "✅ 最大候选数: 150 → 20"

# 减小体素大小保持细节
rosparam set /enhanced_slam_loop_closure_integration/voxel_leaf_size 0.3
echo "✅ 体素大小: 1.5m → 0.3m"

echo ""
echo "步骤4: 调整GPS校正阈值"

# 增大校正阈值，减少频繁校正
rosparam set /state_estimation_node/gps/xy_correction_threshold 0.3
echo "✅ XY校正阈值: 0.005m → 0.3m"

rosparam set /state_estimation_node/gps/height_correction_threshold 0.2
echo "✅ 高度校正阈值: 0.01m → 0.2m"

echo ""
echo "步骤5: 调整ICP参数"

# 减小ICP触发距离
rosparam set /state_estimation_node/gps/icp_trigger_distance 30.0
echo "✅ ICP触发距离: 200.0m → 30.0m"

# 严格化ICP匹配阈值
rosparam set /state_estimation_node/gps/icp_fitness_threshold 0.4
echo "✅ ICP匹配阈值: 10.0 → 0.4"

# 增加最小回环距离
rosparam set /state_estimation_node/gps/loop_closure_min_distance 30.0
echo "✅ 最小回环距离: 5.0m → 30.0m"

echo ""
echo "步骤6: 启用渐进校正模式"

# 发布温和的约束控制信号
rostopic pub /gps_constraint_control std_msgs/Bool "data: true" -1 &
echo "✅ 启用温和约束控制"

echo ""
echo "🎯 平衡模式参数调整完成！"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "GPS回环距离阈值: 25.0m (适中)"
echo "SLAM搜索半径: 40.0m (适中)" 
echo "匹配分数阈值: 0.6 (适中严格)"
echo "平面约束权重: 0.3 (适中)"
echo "XY校正率: 15% (温和)"
echo "高度校正率: 25% (温和)"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo ""
echo "✅ 修复策略："
echo "1. 大幅降低GPS约束强度，避免过度校正"
echo "2. 缩小搜索范围，提高匹配精度"
echo "3. 严格化匹配阈值，避免错误匹配"
echo "4. 降低校正频率，减少系统扰动"
echo "5. 保持适度的回环检测能力"

echo ""
echo "📊 预期效果："
echo "- 点云匹配错乱问题应该立即缓解"
echo "- 系统稳定性显著提升"
echo "- 仍保持一定的GPS校正能力"
echo "- 首尾偏移预期控制在5-15米范围"

echo ""
echo "⚠️  后续建议："
echo "1. 观察点云匹配是否恢复正常"
echo "2. 如果仍有问题，运行: ./restore_conservative_gps.sh"
echo "3. 如果效果良好，可以微调参数进一步优化"
echo "4. 实时监控: ./monitor_gps_performance.sh"
