#!/bin/bash

# GPS性能实时监控脚本

echo "=========================================="
echo "📊 GPS回环检测性能监控"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行"
    exit 1
fi

echo ""
echo "🎯 监控GPS回环检测效果..."
echo "按 Ctrl+C 停止监控"

# 创建监控日志文件
LOG_FILE="/tmp/gps_performance_$(date +%Y%m%d_%H%M%S).log"
echo "日志文件: $LOG_FILE"

# 初始化计数器
LOOP_COUNT=0
FORCE_COUNT=0
SUCCESS_COUNT=0

echo ""
echo "实时监控数据:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 监控函数
monitor_topics() {
    while true; do
        # 获取当前时间
        TIMESTAMP=$(date '+%H:%M:%S')
        
        # 获取回环距离
        DISTANCE=$(timeout 1 rostopic echo /loop_closure_distance -n 1 2>/dev/null | grep "data:" | awk '{print $2}')
        
        # 获取强制回环状态
        FORCE_STATUS=$(timeout 1 rostopic echo /force_loop_closure -n 1 2>/dev/null | grep "data:" | awk '{print $2}')
        
        # 获取回环结果
        RESULT=$(timeout 1 rostopic echo /loop_closure_result -n 1 2>/dev/null | grep "data:" | awk '{print $2}')
        
        # 获取匹配分数
        SCORE=$(timeout 1 rostopic echo /matching_score -n 1 2>/dev/null | grep "data:" | awk '{print $2}')
        
        # 获取回环类型
        LOOP_TYPE=$(timeout 1 rostopic echo /detected_loop_type -n 1 2>/dev/null | grep "data:" | awk '{print $2}' | tr -d '"')
        
        # 统计计数
        if [ "$FORCE_STATUS" = "True" ]; then
            FORCE_COUNT=$((FORCE_COUNT + 1))
        fi
        
        if [ "$RESULT" = "True" ]; then
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        fi
        
        # 显示当前状态
        printf "\r[$TIMESTAMP] 距离:%-8s 强制:%-5s 结果:%-5s 分数:%-8s 类型:%-12s 强制:%d 成功:%d" \
               "${DISTANCE:-N/A}" "${FORCE_STATUS:-N/A}" "${RESULT:-N/A}" "${SCORE:-N/A}" "${LOOP_TYPE:-N/A}" \
               "$FORCE_COUNT" "$SUCCESS_COUNT"
        
        # 记录到日志
        echo "[$TIMESTAMP] Distance:${DISTANCE:-N/A} Force:${FORCE_STATUS:-N/A} Result:${RESULT:-N/A} Score:${SCORE:-N/A} Type:${LOOP_TYPE:-N/A}" >> "$LOG_FILE"
        
        # 检查关键事件
        if [ "$FORCE_STATUS" = "True" ]; then
            echo "" # 换行
            echo "🔄 [$TIMESTAMP] 检测到强制回环触发！距离: ${DISTANCE:-N/A}m"
        fi
        
        if [ "$RESULT" = "True" ]; then
            echo "" # 换行
            echo "✅ [$TIMESTAMP] 回环检测成功！分数: ${SCORE:-N/A}, 类型: ${LOOP_TYPE:-N/A}"
        fi
        
        # 检查距离变化
        if [ ! -z "$DISTANCE" ]; then
            # 使用bc进行浮点数比较（如果可用）
            if command -v bc >/dev/null 2>&1; then
                if [ $(echo "$DISTANCE < 5.0" | bc -l) -eq 1 ]; then
                    echo "" # 换行
                    echo "🎯 [$TIMESTAMP] 接近起点！距离: ${DISTANCE}m - 建议切换到保守模式"
                fi
                
                if [ $(echo "$DISTANCE < 1.0" | bc -l) -eq 1 ]; then
                    echo "" # 换行
                    echo "🏆 [$TIMESTAMP] 非常接近起点！距离: ${DISTANCE}m - 回环效果良好"
                fi
            fi
        fi
        
        sleep 1
    done
}

# 启动监控
trap 'echo ""; echo "监控停止"; echo "总计: 强制回环 $FORCE_COUNT 次, 成功回环 $SUCCESS_COUNT 次"; echo "日志保存在: $LOG_FILE"; exit 0' INT

monitor_topics
