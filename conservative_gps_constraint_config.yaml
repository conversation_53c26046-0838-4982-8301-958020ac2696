# 保守GPS约束配置 - 专门解决首尾偏离问题
# 目标：降低GPS软约束对SLAM匹配的错误影响，减少首尾偏离

# ========================================
# 保守GPS回环检测参数
# ========================================
conservative_gps_loop_closure_optimizer:
  # 基础回环检测参数 - 严格设置
  gps_topic: "/rtk/gnss"
  loop_closure_distance_threshold: 5.0      # 严格的首尾距离阈值(5米)
  min_trajectory_length: 150.0              # 增大最小轨迹长度(150米)，确保轨迹充分
  gps_quality_threshold: 0                  # 只接受RTK固定解，提高GPS质量
  check_interval: 2.0                       # 降低检查频率(2秒)，避免频繁触发
  
  # 中间区域回环检测参数 - 暂时禁用
  intermediate_loop_threshold: 999.0        # 设置极大值，禁用中间回环检测
  min_loop_separation: 100.0                # 增大分离距离
  trajectory_window_size: 50                # 减小分析窗口
  
  # 重复访问回环检测参数 - 暂时禁用
  revisit_threshold: 999.0                  # 设置极大值，禁用重访检测
  revisit_time_threshold: 60.0              # 增大时间间隔阈值
  
  # 系统性能参数
  max_trajectory_points: 1000               # 限制轨迹点数，减少计算负担
  loop_detection_cooldown: 10.0             # 增大冷却时间(10秒)，避免频繁检测

# ========================================
# 保守SLAM回环检测集成模块参数
# ========================================
conservative_slam_loop_closure_integration:
  # 基础搜索参数 - 严格设置
  force_search_radius: 15.0                 # 减小搜索半径(15米)
  max_search_candidates: 5                  # 减少候选数量，提高匹配质量
  min_keyframes_for_loop: 100               # 增大最小关键帧数
  
  # 不同回环类型的搜索半径 - 严格限制
  intermediate_search_radius: 999.0         # 禁用中间回环搜索
  revisit_search_radius: 999.0              # 禁用重访回环搜索
  
  # 不同回环类型的匹配分数阈值 - 严格要求
  start_end_score_threshold: 0.15           # 严格的首尾匹配阈值(0.15)
  intermediate_score_threshold: 0.10        # 严格的中间回环匹配阈值
  revisit_score_threshold: 0.10             # 严格的重访回环匹配阈值
  
  # 点云处理参数 - 高精度设置
  voxel_leaf_size: 0.05                     # 小体素大小，提高匹配精度
  sliding_window_size: 50                   # 减小滑动窗口
  temporal_consistency_weight: 0.5          # 增大时间一致性权重
  
  # NDT配准参数 - 严格设置
  ndt_transformation_epsilon: 0.001         # 严格的NDT变换收敛阈值
  ndt_step_size: 0.05                       # 小步长，提高精度
  ndt_resolution: 0.5                       # 小分辨率，提高精度
  ndt_max_iterations: 50                    # 增加迭代次数
  
  # ICP配准参数 - 严格设置
  icp_max_iterations: 100                   # 增加ICP迭代次数
  icp_transformation_epsilon: 1e-8          # 严格的ICP变换收敛阈值
  icp_euclidean_fitness_epsilon: 1e-8       # 严格的ICP欧几里得适应度阈值

# ========================================
# 保守GPS约束参数 - 用于voxelMapping.cpp
# ========================================
conservative_gps_constraints:
  # 基础约束参数
  enable_gps_correction: true               # 启用GPS校正
  enable_gps_loop_closure: true             # 启用GPS回环检测
  enable_gps_plane_constraint: false        # 禁用GPS平面约束
  enable_gps_icp_loop_closure: false        # 禁用GPS触发的ICP回环检测
  
  # 校正参数 - 温和设置
  height_correction_threshold: 1.0          # 增大高度校正阈值(1米)
  correction_rate: 0.02                     # 极小的校正率(2%)
  xy_correction_threshold: 999.0            # 禁用XY校正
  xy_correction_rate: 0.0                   # 禁用XY校正率
  
  # 回环检测参数 - 严格设置
  loop_closure_distance: 3.0                # 严格的回环距离(3米)
  loop_closure_min_distance: 200.0          # 大幅增加最小轨迹长度(200米)
  icp_trigger_distance: 5.0                 # 严格的ICP触发距离(5米)
  
  # 约束强度参数 - 极度保守
  plane_constraint_weight: 0.0              # 禁用平面约束权重
  constraint_window_size: 10                # 小约束窗口
  
  # 智能校正参数 - 重新设计
  large_error_threshold: 20.0               # 大误差阈值(20米)
  medium_error_threshold: 10.0              # 中等误差阈值(10米)
  small_error_threshold: 5.0                # 小误差阈值(5米)
  
  # 保守的校正因子
  large_error_correction_factor: 0.1        # 大误差校正因子(10%)
  medium_error_correction_factor: 0.05      # 中等误差校正因子(5%)
  small_error_correction_factor: 0.02       # 小误差校正因子(2%)
  tiny_error_correction_factor: 0.01        # 微小误差校正因子(1%)
  
  # 安全限制
  max_single_correction: 1.0                # 单次最大校正距离(1米)
  max_total_correction: 10.0                # 总最大校正距离(10米)
  correction_safety_factor: 0.5             # 校正安全因子

# ========================================
# 错误匹配检测和预防参数
# ========================================
error_matching_prevention:
  # 匹配质量检查
  enable_matching_quality_check: true       # 启用匹配质量检查
  min_matching_points: 500                  # 最小匹配点数
  max_matching_distance: 0.3                # 最大匹配距离
  matching_consistency_threshold: 0.8       # 匹配一致性阈值
  
  # 几何一致性检查
  enable_geometric_consistency: true        # 启用几何一致性检查
  max_translation_jump: 2.0                 # 最大平移跳跃(2米)
  max_rotation_jump: 0.2                    # 最大旋转跳跃(0.2弧度)
  
  # 时间一致性检查
  enable_temporal_consistency: true         # 启用时间一致性检查
  max_velocity_change: 1.0                  # 最大速度变化(1m/s)
  velocity_smoothing_window: 10             # 速度平滑窗口
  
  # 异常检测
  enable_outlier_detection: true            # 启用异常检测
  outlier_detection_threshold: 3.0          # 异常检测阈值(3倍标准差)
  min_inlier_ratio: 0.7                     # 最小内点比例

# ========================================
# 调试和监控参数
# ========================================
debug_monitoring:
  enable_detailed_logging: true             # 启用详细日志
  log_matching_details: true                # 记录匹配详情
  log_correction_history: true              # 记录校正历史
  publish_debug_markers: true               # 发布调试标记
  save_problematic_matches: true            # 保存有问题的匹配
  
  # 性能监控
  monitor_processing_time: true             # 监控处理时间
  monitor_memory_usage: true                # 监控内存使用
  monitor_correction_frequency: true        # 监控校正频率
  
  # 警告阈值
  excessive_correction_threshold: 5.0       # 过度校正警告阈值
  frequent_correction_threshold: 10         # 频繁校正警告阈值(次数/分钟)
  large_error_warning_threshold: 15.0       # 大误差警告阈值

# ========================================
# 运行时参数调整建议
# ========================================
runtime_tuning_guide:
  # 如果首尾偏离仍然很大(>10米)
  if_large_deviation:
    - "进一步减小correction_rate到0.01"
    - "增大loop_closure_min_distance到300米"
    - "暂时禁用enable_gps_loop_closure"
    - "只使用高度校正，禁用XY校正"
  
  # 如果匹配质量差
  if_poor_matching:
    - "减小start_end_score_threshold到0.10"
    - "增大min_keyframes_for_loop到150"
    - "减小voxel_leaf_size到0.03"
    - "增加NDT迭代次数到100"
  
  # 如果系统不稳定
  if_system_unstable:
    - "增大loop_detection_cooldown到20秒"
    - "减小max_search_candidates到3"
    - "启用error_matching_prevention所有功能"
    - "设置更严格的异常检测阈值"
