#!/bin/bash

# GPS大偏移修复脚本 - 解决首尾偏离几十米问题
# 采用激进策略大幅增强GPS约束

echo "=========================================="
echo "🔧 GPS大偏移修复 - 激进约束策略"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动roscore"
    exit 1
fi

echo ""
echo "🎯 问题诊断: 首尾偏离几十米"
echo "原因: GPS软约束过于保守，无法有效校正大偏移"
echo "策略: 激进增强GPS约束权重和触发条件"

echo ""
echo "步骤1: 激进调整GPS回环检测参数"

# 超大幅增加GPS回环检测距离阈值
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 100.0
echo "✅ GPS回环距离阈值: 5.0m → 100.0m (20倍增加)"

# 大幅降低最小轨迹长度要求
rosparam set /enhanced_gps_loop_closure_optimizer/min_trajectory_length 15.0
echo "✅ 最小轨迹长度: 50.0m → 15.0m"

# 最快检测频率
rosparam set /enhanced_gps_loop_closure_optimizer/check_interval 0.1
echo "✅ 检查间隔: 1.0s → 0.1s (10倍加快)"

# 最短冷却时间
rosparam set /enhanced_gps_loop_closure_optimizer/loop_detection_cooldown 0.5
echo "✅ 冷却时间: 5.0s → 0.5s"

echo ""
echo "步骤2: 激进调整SLAM匹配参数"

# 超大搜索半径
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 200.0
echo "✅ 搜索半径: 10.0m → 200.0m (20倍增加)"

# 极度放宽匹配分数阈值
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 3.0
echo "✅ 起点-终点匹配阈值: 0.25 → 3.0 (12倍放宽)"

# 大幅增加搜索候选数
rosparam set /enhanced_slam_loop_closure_integration/max_search_candidates 100
echo "✅ 最大候选数: 10 → 100"

# 大体素减少计算量
rosparam set /enhanced_slam_loop_closure_integration/voxel_leaf_size 1.0
echo "✅ 体素大小: 0.1m → 1.0m"

echo ""
echo "步骤3: 激进增强GPS约束强度"

# 强制启用GPS平面约束
rosparam set /state_estimation_node/gps/enable_plane_constraint true
echo "✅ GPS平面约束: 强制启用"

# 接近最大平面约束权重
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.95
echo "✅ 平面约束权重: 0.1 → 0.95 (接近最大值)"

# 最低XY校正阈值
rosparam set /state_estimation_node/gps/xy_correction_threshold 0.01
echo "✅ XY校正阈值: 0.8m → 0.01m (80倍严格)"

# 接近最大XY校正率
rosparam set /state_estimation_node/gps/xy_correction_rate 0.95
echo "✅ XY校正率: 3% → 95% (强力校正)"

# 接近最大高度校正率
rosparam set /state_estimation_node/gps/correction_rate 0.95
echo "✅ 高度校正率: 10% → 95% (接近完全校正)"

# 最低高度校正阈值
rosparam set /state_estimation_node/gps/height_correction_threshold 0.01
echo "✅ 高度校正阈值: 0.3m → 0.01m (30倍严格)"

echo ""
echo "步骤4: 激进调整ICP回环检测"

# 超大ICP触发距离
rosparam set /state_estimation_node/gps/icp_trigger_distance 150.0
echo "✅ ICP触发距离: 20.0m → 150.0m (7.5倍增加)"

# 极度放宽ICP匹配阈值
rosparam set /state_estimation_node/gps/icp_fitness_threshold 5.0
echo "✅ ICP匹配阈值: 0.3 → 5.0 (16倍放宽)"

# 最低回环距离要求
rosparam set /state_estimation_node/gps/loop_closure_min_distance 5.0
echo "✅ 最小回环距离: 50.0m → 5.0m"

echo ""
echo "步骤5: 强制启用所有GPS功能"

rosparam set /state_estimation_node/gps/enable_correction true
rosparam set /state_estimation_node/gps/enable_loop_closure true
rosparam set /state_estimation_node/gps/enable_icp_loop_closure true
rosparam set /state_estimation_node/gps/enable_plane_constraint true
echo "✅ 所有GPS功能强制启用"

echo ""
echo "步骤6: 设置强制回环模式"

# 发布强制回环信号
rostopic pub /force_loop_closure std_msgs/Bool "data: true" -1 &
echo "✅ 发布强制回环信号"

# 启用动态约束控制
rostopic pub /gps_constraint_control std_msgs/Bool "data: true" -1 &
echo "✅ 启用动态约束控制"

echo ""
echo "🎯 激进参数调整完成！"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "GPS回环距离阈值: 100.0m (原5.0m, 20倍)"
echo "SLAM搜索半径: 200.0m (原10.0m, 20倍)" 
echo "匹配分数阈值: 3.0 (原0.25, 12倍)"
echo "平面约束权重: 0.95 (原0.1, 9.5倍)"
echo "XY校正率: 95% (原3%, 32倍)"
echo "高度校正率: 95% (原10%, 9.5倍)"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo ""
echo "⚠️  激进调整警告："
echo "1. 这是针对大偏移的极端策略，可能过度校正"
echo "2. 计算负载会显著增加，请监控CPU使用率"
echo "3. 如果出现震荡，立即运行: ./adjust_gps_constraints.sh 1"
echo "4. 建议实时监控匹配效果和系统稳定性"

echo ""
echo "📊 实时监控命令："
echo "rostopic echo /loop_closure_distance"
echo "rostopic echo /force_loop_closure" 
echo "rostopic echo /loop_closure_result"
echo "rostopic echo /matching_score"
echo "rostopic echo /detected_loop_type"

echo ""
echo "🔄 如果效果不佳，尝试以下恢复命令："
echo "./adjust_gps_constraints.sh 1  # 恢复保守模式"
echo "./adjust_gps_constraints.sh 2  # 中等模式"
